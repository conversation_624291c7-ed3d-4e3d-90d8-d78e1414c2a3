<?php

namespace App\Models\Product;

use App\Models\BaseModel;

class AccountProductModel extends BaseModel
{
    protected static $connection = 'default';
    protected static $table = 'account_product';

    public static function getAccountProByPidsAccIds($pids = [], $accountIds = [])
    {
        $where = [];
        if ($pids) {
            $where[] = ['product_id', 'in', $pids];
        }
        if ($accountIds) {
            $where[] = ['account_id', 'in', $accountIds];
        }

        return self::getAll($where);
    }

    /**
     *
     *
     * @param array $account_ids
     * @param       $product_ids
     *
     * @static
     * @return mixed
     * @throws \Exception
     * <AUTHOR> 2023-10-19 14:34:15
     */
    public static function getListWithAccountsAndProduts(array $account_ids, $product_ids) {
        $where = [
            ['account_id','in',$account_ids],
        ];
        if(!empty($product_ids)){
            $where[] = ['product_id','in',$product_ids];
        }
        return self::getAll($where);
    }
}