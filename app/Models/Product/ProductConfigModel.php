<?php

namespace App\Models\Product;

use App\Models\BaseModel;
use Exception;

class ProductConfigModel extends BaseModel
{
    protected static $connection = 'rc';
    protected static $table = 'product_config';


    /**
     * 根据主体和主产品id获取配置列表
     *
     * @param $group_ids
     * @param $product_id
     * @param $item_ids
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-11-01 11:23:39
     */
    public static function getListByGroupIdAndProductId($group_ids,$product_id,$item_ids){
        $where = [];

        if(!empty($group_ids)){
            $where[] = ['group_id','in',$group_ids];
        }else{
            $where[] = ['group_id','!=',''];
        }

        if(!empty($product_item_ids)){
            $where[] = ['config_item_id','in',$item_ids];
        }

        //客户,账号 id须空
        $where[] = ['customer_id','=',''];
        $where[] = ['account_id','=',''];

        if($product_id > 0){
            $where[] = ['product_id','=',$product_id];
        }
        return self::getAll($where);
    }


    /**
     * 根据客户和主产品id获取配置列表
     *
     * @param $customer_ids
     * @param $product_id
     * @param $item_ids
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-11-01 14:18:45
     */
    public static function getListByCustomerIdAndProductId($customer_ids,$product_id,$item_ids){
        $where = [];

        if(!empty($customer_ids)){
            $where[] = ['customer_id','in',$customer_ids];
        }else{
            $where[] = ['customer_id','!=',''];
        }

        if(!empty($product_item_ids)){
            $where[] = ['config_item_id','in',$item_ids];
        }

        //主体,账号 id须空
        $where[] = ['group_id','=',''];
        $where[] = ['account_id','=',''];

        if($product_id > 0){
            $where[] = ['product_id','=',$product_id];
        }
        return self::getAll($where);
    }

    /**
     * 根据账号和主产品id获取配置列表
     *
     * @param $account_ids
     * @param $product_id
     * @param $item_ids
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-11-01 14:20:42
     */
    public static function getListByAccountIdAndProductId($account_ids,$product_id,$item_ids){
        $where = [];

        if(!empty($account_ids)){
            $where[] = ['account_id','in',$account_ids];
        }else{
            $where[] = ['account_id','!=',''];
        }

        if(!empty($product_item_ids)){
            $where[] = ['config_item_id','in',$item_ids];
        }

        //主体,账号 id须空
        $where[] = ['group_id','=',''];
        $where[] = ['customer_id','=',''];

        if($product_id > 0){
            $where[] = ['product_id','=',$product_id];
        }
        return self::getAll($where);
    }


    /**
     * 获取子产品配置列表
     *
     * @param $product_id
     * @param $group_id
     * @param $customer_id
     * @param $account_id
     * @param $config_path
     * @param $item_ids
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-11-02 16:32:11
     */
    public static function getConfigList($product_id,$group_id,$customer_id,$account_id,$config_path,$item_ids){
        $where = [];

        if(!empty($group_id)){
            $where[] = ['group_id','=',$group_id];
        }
        if(!empty($customer_id)){
            $where[] = ['customer_id','=',$customer_id];
        }
        if(!empty($account_id)){
            $where[] = ['account_id','=',$account_id];
        }
        if(!empty($product_id)){
            $where[] = ['product_id','in',$product_id];
        }
        if(!empty($config_path)){
            $where[] = ['config_path','=',$config_path];
        }

        if(!empty($item_ids)){
            $where[] = ['config_item_id','in',$item_ids];
        }
        if(empty($where)){
            return [];
        }
        $where[] = ['deleted_at', 'is null', ''];
        return self::getAll($where);
    }


    /**
     * 获取全部这个配置项,这个客户(主体)的配置
     *
     * @param $product_id
     * @param $item_id
     * @param $group_id
     * @param $customer_id
     * @param $account_id
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-11-03 16:11:39
     */
    public static function getAllItemConfigs($product_id, $item_id, $group_id, $customer_id, $account_id) {
        $where = [];
        $where[] = ['product_id','=',$product_id];
        $where[] = ['config_item_id','=',$item_id];

        if(!empty($group_id)){
            $where[] = ['group_id','=',$group_id];
        }
        if(empty($group_id) && !empty($customer_id)){
            $where[] = ['customer_id','=',$customer_id];
        }
        if(empty($group_id) && empty($customer_id) && !empty($account_id)){
            $where[] = ['account_id','=',$account_id];
        }

        $where[] = ['deleted_at', 'is null', ''];
        return self::getAll($where);
    }

    /**
     * 根据主体id获取配置列表
     *
     * @param        $group_ids
     * @param        $product_ids
     * @param array  $product_item_ids
     * @param string $config_path
     *
     * @return array|mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-11-08 10:52:00
     */
    public static function getConfigOnlyGroupIds($group_ids,$product_ids,$product_item_ids = [],$config_path = ''){
        if(empty($group_ids) || empty($product_item_ids)){
            return [];
        }
        $where = [
            ['group_id', 'in', $group_ids],
            ['customer_id', '=', ''],
            ['account_id', '=', ''],
            ['config_item_id', 'in', $product_item_ids],
            ['deleted_at', 'is null', ''],
        ];
        if(!empty($config_path)) {
            $where[] = ['config_path', '=', $config_path];
        }
        if(!empty($product_ids)) {
            $where[] = ['product_id', 'in', $product_ids];
        }
        return self::getAll($where);
    }


    /**
     * 根据客户id获取配置列表
     *
     * @param        $customer_ids
     * @param        $product_ids
     * @param array  $product_item_ids
     * @param string $config_path
     *
     * @return array|mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-11-08 10:54:00
     */
    public static function getConfigOnlyCustomerIds($customer_ids,$product_ids,$product_item_ids = [],$config_path = ''){
        if(empty($customer_ids) || empty($product_item_ids)){
            return [];
        }
        $where = [
            ['customer_id', 'in', $customer_ids],
            ['account_id', '=', ''],
            ['config_item_id', 'in', $product_item_ids],
            ['deleted_at', 'is null', ''],
        ];
        if(!empty($config_path)) {
            $where[] = ['config_path', '=', $config_path];
        }
        if(!empty($product_ids)) {
            $where[] = ['product_id', 'in', $product_ids];
        }
        return ProductConfigModel::getAll($where);
    }


    /**
     * 根据账号id获取配置列表
     *
     * @param        $account_ids
     * @param        $product_ids
     * @param array  $product_item_ids
     * @param string $config_path
     *
     * @return array|mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-11-08 10:56:16
     */
    public static function getConfigOnlyAccountIds($account_ids,$product_ids,$product_item_ids = [],$config_path = ''){
        if(empty($account_ids) || empty($product_item_ids)){
            return [];
        }
        $where = [
            ['account_id', 'in', $account_ids],
            ['config_item_id', 'in', $product_item_ids],
            ['deleted_at', 'is null', ''],
        ];

        if(!empty($config_path)) {
            $where[] = ['config_path', '=', $config_path];
        }
        if(!empty($product_ids)) {
            $where[] = ['product_id', 'in', $product_ids];
        }
        return ProductConfigModel::getAll($where);
    }
}