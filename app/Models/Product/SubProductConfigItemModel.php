<?php

namespace App\Models\Product;

use App\Models\BaseModel;
use Exception;

class SubProductConfigItemModel extends BaseModel
{
    protected static $connection = 'rc';
    protected static $table = 'sub_product_config_item';

    public static function getAllAvailableItemByPids($pids = [])
    {
        $where = [
            ['deleted_at', 'is null', '']
        ];
        if ($pids) {
            $where[] = ['sub_product_id', 'in', $pids];
        }
        return self::getAll($where);
    }


    /**
     * 根据配置名称获取配置项列表
     *
     * @param $item_name
     * @param $product_id int 子产品id
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-11-01 14:05:37
     */
    public static function getListByItemNameAndSubProductId($item_name, $product_id) {
        $where = [
            ['deleted_at', 'is null', ''],
        ];
        if(!empty($product_id)) {
            $where[] = ['product_id', '=', $product_id];
        }
        if(!empty($item_name)) {
            $where[] = ['item_name', '=', $item_name];
        }

        return self::getAll($where);
    }


    /**
     *
     *
     * @param $item_name
     * @param $product_ids
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-11-14 11:19:45
     */
    public static function getListByItemNameAndSubProductIds($item_name, $product_ids) {
        $where = [
            ['deleted_at', 'is null', ''],
        ];
        if(!empty($product_id)) {
            $where[] = ['product_id', 'in', $product_ids];
        }
        if(!empty($item_name)) {
            $where[] = ['item_name', '=', $item_name];
        }

        return self::getAll($where);
    }

    /**
     * 获取配置项详情
     *
     * @param $id
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-11-03 16:43:55
     */
    public static function getInfoById($id) {
        $where = [
            ['id', '=', $id],
        ];

        return self::getOne($where);
    }


    /**
     * 获取配置项详情
     *
     * @param $id
     * @param $product_id
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-12-14 16:30:12
     */
    public static function getInfoByIdAndProductId($id,$product_id) {
        $where = [
            ['id', '=', $id],
            ['product_id', '=', $product_id],
        ];

        return self::getOne($where);
    }
}