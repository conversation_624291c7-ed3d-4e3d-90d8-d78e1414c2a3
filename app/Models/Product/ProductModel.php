<?php

namespace App\Models\Product;

use App\Models\BaseModel;
use App\Utils\Helpers\Enu;
use Exception;

class ProductModel extends BaseModel
{
    protected static $connection = 'default';
    protected static $table = 'product';

    public static function getSubProductsByFatherId($fatherId)
    {
        $where = [
            ['father_id', '=', $fatherId]
        ];
        return self::getAll($where);
    }

    public static function getMainProductInfo($fatherId)
    {
        $where = [
            ['product_id', '=', $fatherId]
        ];
        return self::getOne($where);
    }

    /**
     * 获取子产品
     *
     * @param          $father_ids
     * @param string[] $field
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-11-14 14:43:16
     */
    public static function getSubProductsByFatherIds($father_ids,$field = ['product_id']) {
        $where = [
            ['father_id', 'in', $father_ids]
        ];
        return self::getAll($where,$field);
    }
    /**
     * 获取所有邦秒验产品数据
     *
     * @return mixed
     * <AUTHOR> 2023-10-16 11:16:24
     *
     * @static
     */
    public static function getBMYProducts(){
        $sql = 'select `product_id`,`product_name`,`father_id` from `product` where `product_id` = '.Enu::PRODUCT_BMY_PRODUCT_ID . ' or `father_id` = ' . Enu::PRODUCT_BMY_PRODUCT_ID;
        return self::query($sql,[],'all');
    }


    /**
     * 根据父id获取列表,包含主产品和子产品
     *
     * @param $father_ids
     *
     * @static
     * @return mixed
     * <AUTHOR> 2023-10-16 11:56:00
     */
    public static function getListByFatherids($father_ids = null){
        if(is_null($father_ids) || empty($father_ids)){//全部产品
           $sql = 'select `product_id`,`product_name`,`father_id` from `product`';
        }else{//指定主产品及其子产品
           $father_ids = implode(',', $father_ids);
           $sql = 'select `product_id`,`product_name`,`father_id` from `product` where `product_id` in (' . $father_ids . ') or `father_id` in (' . $father_ids . ')';
        }
        return self::query($sql,[],'all');
    }


    /**
     * 根据产品id获取列表
     *
     * @param $where
     * @param $field
     * @param $page_size
     * @param $page
     * @param $order
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-10-16 12:11:25
     */
    public static function getProductList( $where = [],$field = ['*'],$page_size = 10,$page = 1,$order = ['id','desc']){
        $limit = $page_size;
        $offset = ($page - 1) * $limit;
        $page = ['limit'=>$limit,'offset'=>$offset];
        return self::getAll($where,$field,$page,$order);
    }


    /**
     * 根据产品id获取数量
     *
     * @param $where
     *
     * @static
     * @return int|mixed
     * @throws Exception
     * <AUTHOR> 2023-10-16 12:11:28
     */
    public static function getProductCount($where){
        return self::getCount($where);
    }


    /**
     * 根据产品id获取产品信息
     *
     * @param          $product_id
     * @param string[] $field
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-10-18 14:29:04
     */
    public static function getInfoByProductId($product_id,$field = ['*']) {
        $where = [['product_id', '=', $product_id]];
        return self::getOne($where,$field);
    }

    /**
     * 根据产品id获取产品信息
     *
     * @param          $product_ids
     * @param string[] $field
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-10-18 14:29:04
     */
    public static function getInfoByProductIds($product_ids,$field = ['*']) {
        $where = [['product_id', 'in', $product_ids]];
        return self::getAll($where,$field);
    }


    /**
     * 根据产品名称查询产品信息
     *
     * @param $product_name
     * @param $field
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2024-03-01 17:11:10
     */
    public static function getInfoByProductName($product_name,$field = ['*']) {
        $where = [['product_name', '=', $product_name]];
        return self::getOne($where,$field);
    }

    /**
     * 根据产品名称查询产品信息
     *
     * @param          $product_names
     * @param string[] $field
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2024-03-01 17:11:10
     */
    public static function getInfoByProductNames($product_names,$field = ['*']) {
        $where = [['product_name', 'in', $product_names]];
        return self::getAll($where,$field);
    }


    /**
     * 获取主产品id
     *
     * @param $product_id
     * @param $field
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-10-26 11:54:37
     */
    public static function getFatherId($product_id,$field = ['*']) {
        $where = [['product_id', '=', $product_id]];
        $product_info = self::getOne($where,$field);

        if($product_info['father_id'] == 0 || $product_info['father_id'] == $product_info['product_id']){
            return intval($product_info['product_id']);
        }else{
            return intval($product_info['father_id']);
        }
    }


    /**
     * 获取主产品
     *
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-11-14 14:40:07
     *
     * @static
     */
    public static function getFatherProduct($field = ['product_id']) {
        $where = [['father_id', 'in', [0,401]]];
        return self::getAll($where,$field);
    }
}