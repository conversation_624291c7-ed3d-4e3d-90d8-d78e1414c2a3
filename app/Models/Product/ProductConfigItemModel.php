<?php

namespace App\Models\Product;

use App\Models\BaseModel;
use App\Utils\Helpers\Enu;
use Exception;

class ProductConfigItemModel extends BaseModel
{
    protected static $connection = 'rc';
    protected static $table = 'product_config_item';

    public static function getAllAvailableItemByPids($pids = [])
    {
        $where = [
            ['deleted_at', 'is null', '']
        ];
        if ($pids) {
            $where[] = ['product_id', 'in', $pids];
        }
        return self::getAll($where);
    }


    /**
     * 获取自己跑可继承的配置项
     *
     * @param $product_id
     * @param $role
     * @param $modify
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-10-26 11:19:43
     */
    public static function getExtendItems($product_id,$role,$modify) {
        $where = [
            ['product_id', '=', $product_id],
            ['deleted_at', 'is null', ''],
            ['role', '=', $role],
            ['extend', '=', Enu::PRODUCT_CONFIG_ITEM_SUB_CAN_EXTEND],
        ];
        if($modify == Enu::PRODUCT_CONFIG_ITEM_CUSTOMER_CAN_MODIFY) {
            $where[] = ['modify', '=', Enu::PRODUCT_CONFIG_ITEM_CUSTOMER_CAN_MODIFY];
        }

        return self::getAll($where);
    }


    /**
     * 根据配置名称获取配置项列表
     *
     * @param $item_name
     * @param $product_id int 主产品id
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-11-01 14:05:37
     */
    public static function getListByItemNameAndProductId($item_name, $product_id) {
        $where = [
            ['deleted_at', 'is null', ''],
        ];
        if(!empty($product_id)) {
            $where[] = ['product_id', '=', $product_id];
        }
        if(!empty($item_name)) {
            $where[] = ['item_name', '=', $item_name];
        }

        return self::getAll($where);
    }


    /**
     * 根据配置名称获取配置项列表
     *
     * @param $item_name
     * @param $product_ids
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-11-14 11:19:13
     */
    public static function getListByItemNameAndProductIds($item_name, $product_ids) {
        $where = [
            ['deleted_at', 'is null', ''],
        ];
        if(!empty($product_id)) {
            $where[] = ['product_id', 'in', $product_id];
        }
        if(!empty($item_name)) {
            $where[] = ['item_name', '=', $item_name];
        }

        return self::getAll($where);
    }

    /**
     * 获取配置项详情
     *
     * @param $id
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-11-03 16:43:55
     */
    public static function getInfoById($id) {
        $where = [
            ['id', '=', $id],
        ];

        return self::getOne($where);
    }

    /**
     * 获取产品所有的配置项
     *
     * @param $product_id
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-11-03 17:14:26
     */
    public static function getAllProductConfigItem($product_id) {
        $where = [['product_id','=',$product_id]];
        return self::getAll($where,['*'],[],['id','desc']);
    }


    /**
     * 获取配置项详情
     *
     * @param $id
     * @param $product_id
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-12-14 16:22:48
     */
    public static function getInfoByIdAndProductId($id,$product_id) {
        $where = [
            ['id', '=', $id],
            ['product_id', '=', $product_id],
        ];

        return self::getOne($where);
    }
}