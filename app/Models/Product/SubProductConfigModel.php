<?php

namespace App\Models\Product;

use App\Models\BaseModel;
use App\Utils\Helpers\Enu;
use Exception;

class SubProductConfigModel extends BaseModel
{
    protected static $connection = 'rc';
    protected static $table = 'sub_product_config';


    /**
     * 主体-子产品 配置列表
     *
     * @param $group_ids
     * @param $sub_product_id
     * @param $item_ids
     * @param $config_item_source
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-11-01 14:26:12
     */
    public static function getListByGroupIdAndProductId($group_ids,$sub_product_id,$item_ids,$config_item_source){
        $where = [];

        if(!empty($group_ids)){
            $where[] = ['group_id','in',$group_ids];
        }else{
            $where[] = ['group_id','!=',''];
        }

        if(!empty($item_ids)){
            $where[] = ['config_item_id','in',$item_ids];
            $where[] = ['config_item_source','=',$config_item_source];//1 主产品配置项  2 子产品配置项
        }

        //客户,账号 id须空
        $where[] = ['customer_id','=',''];
        $where[] = ['account_id','=',''];

        if($sub_product_id > 0){
            $where[] = ['sub_product_id','=',$sub_product_id];
        }
        return self::getAll($where);
    }


    /**
     * 客户-子产品 配置列表
     *
     * @param $customer_ids
     * @param $sub_product_id
     * @param $item_ids
     * @param $config_item_source
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-11-01 14:26:56
     */
    public static function getListByCustomerIdAndProductId($customer_ids,$sub_product_id,$item_ids,$config_item_source){
        $where = [];

        if(!empty($customer_ids)){
            $where[] = ['customer_id','in',$customer_ids];
        }else{
            $where[] = ['customer_id','!=',''];
        }

        if(!empty($item_ids)){
            $where[] = ['config_item_id','in',$item_ids];
            $where[] = ['config_item_source','=',$config_item_source];//1 主产品配置项  2 子产品配置项
        }

        //客户,账号 id须空
        $where[] = ['group_id','=',''];
        $where[] = ['account_id','=',''];

        if($sub_product_id > 0){
            $where[] = ['sub_product_id','=',$sub_product_id];
        }
        return self::getAll($where);
    }


    /**
     * 账号-子产品 配置列表
     *
     * @param $account_ids
     * @param $sub_product_id
     * @param $item_ids
     * @param $config_item_source
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-11-01 14:27:36
     */
    public static function getListByAccountIdAndProductId($account_ids,$sub_product_id,$item_ids,$config_item_source){
        $where = [];

        if(!empty($account_ids)){
            $where[] = ['account_id','in',$account_ids];
        }else{
            $where[] = ['account_id','!=',''];
        }

        if(!empty($item_ids)){
            $where[] = ['config_item_id','in',$item_ids];
            $where[] = ['config_item_source','=',$config_item_source];//1 主产品配置项  2 子产品配置项
        }

        //客户,账号 id须空
        $where[] = ['group_id','=',''];
        $where[] = ['customer_id','=',''];

        if($sub_product_id > 0){
            $where[] = ['sub_product_id','=',$sub_product_id];
        }
        return self::getAll($where);
    }


    /**
     * 获取子产品配置列表
     *
     * @param $sub_product_id_arr
     * @param $group_id
     * @param $customer_id
     * @param $account_id
     * @param $config_path
     * @param $config_item_source
     * @param $item_ids
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-11-02 16:32:11
     */
    public static function getConfigList($sub_product_id_arr,$group_id,$customer_id,$account_id,$config_path,$config_item_source,$item_ids){
        $where = [];
        if(!empty($sub_product_id_arr)){
            $where[] = ['sub_product_id','in',$sub_product_id_arr];
        }
        if(!empty($group_id)){
            $where[] = ['group_id','=',$group_id];
        }
        if(!empty($customer_id)){
            $where[] = ['customer_id','=',$customer_id];
        }
        if(!empty($account_id)){
            $where[] = ['account_id','=',$account_id];
        }
        if(!empty($config_path)){
            $where[] = ['config_path','=',$config_path];
        }

        if(!empty($item_ids)){
            $where[] = ['config_item_id','in',$item_ids];
            $where[] = ['config_item_source','=',$config_item_source];//1 主产品配置项  2 子产品配置项
        }
        if(empty($where)){
            return [];
        }
        $where[] = ['deleted_at', 'is null', ''];
        return self::getAll($where);
    }

    /**
     * 获取全部这个配置项,这个客户(主体)的配置
     *
     * @param $sub_product_id
     * @param $item_id
     * @param $item_source
     * @param $group_id
     * @param $customer_id
     * @param $account_id
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-11-03 16:15:16
     */
    public static function getAllItemConfigs($sub_product_id, $item_id, $item_source, $group_id, $customer_id, $account_id) {
        $where = [];
        $where[] = ['sub_product_id','=',$sub_product_id];
        $where[] = ['config_item_id','=',$item_id];
        $where[] = ['config_item_source','=',$item_source];//1 主产品配置项  2 子产品配置项

        if(!empty($group_id)){
            $where[] = ['group_id','=',$group_id];
        }
        if(empty($group_id) && !empty($customer_id)){
            $where[] = ['customer_id','=',$customer_id];
        }
        if(empty($group_id) && empty($customer_id) && !empty($account_id)){
            $where[] = ['account_id','=',$account_id];
        }

        $where[] = ['deleted_at', 'is null', ''];
        return self::getAll($where);
    }


    /**
     * 删除产品配置 仅可删除继承自主产品的配置项
     *
     * @param $sub_product_id
     * @param $config_item_id
     * @param $admin
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-11-07 11:19:53
     */
   public static function delSubProductConfig($sub_product_id,$config_item_id,$admin){
        $where = [
            ['sub_product_id','=',$sub_product_id],
            ['group_id','=',''],
            ['customer_id','=',''],
            ['account_id','=',''],
            ['config_path','=','sub_product'],
            ['config_item_id','=',$config_item_id],
            ['config_item_source','=',Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT],
            ['deleted_at','is null',''],
        ];
        $data = [
            'deleted_at' => date(Enu::TIME_FORMAT),
            'admin' => $admin
        ];
        return self::edit($where,$data);
    }

    /**
     * 根据主体id获取配置列表
     *
     * @param        $group_ids
     * @param        $sub_product_id_arr
     * @param        $config_item_source
     * @param array  $sub_product_item_ids
     * @param string $config_path
     *
     * @return array|mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-11-08 10:51:43
     */
    public static function getConfigOnlyGroupIds($group_ids,$sub_product_id_arr,$config_item_source,$sub_product_item_ids = [],$config_path = ''){
        if(empty($group_ids) || empty($sub_product_item_ids)){
            return [];
        }
        $where = [
            ['group_id', 'in', $group_ids],
            ['customer_id', '=', ''],
            ['account_id', '=', ''],
            ['config_item_source', '=', $config_item_source],
            ['config_item_id', 'in', $sub_product_item_ids],
            ['deleted_at', 'is null', ''],
        ];
        if(!empty($config_path)) {
            $where[] = ['config_path', '=', $config_path];
        }
        if(!empty($sub_product_id_arr)) {
            $where[] = ['sub_product_id', 'in', $sub_product_id_arr];
        }
        return SubProductConfigModel::getAll($where);
    }


    /**
     * 根据客户id获取配置列表
     *
     * @param        $customer_ids
     * @param        $sub_product_id_arr
     * @param        $config_item_source
     * @param array  $sub_product_item_ids
     * @param string $config_path
     *
     * @return array|mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-11-08 10:54:55
     */
    public static function getConfigOnlyCustomerIds($customer_ids,$sub_product_id_arr,$config_item_source,$sub_product_item_ids = [],$config_path = ''){
        if(empty($customer_ids) || empty($sub_product_item_ids)){
            return [];
        }
        $where = [
            ['customer_id', 'in', $customer_ids],
            ['account_id', '=', ''],
            ['config_item_id', 'in', $sub_product_item_ids],
            ['config_item_source', '=', $config_item_source],
            ['deleted_at', 'is null', ''],
        ];
        if(!empty($config_path)) {
            $where[] = ['config_path', '=', $config_path];
        }
        if(!empty($sub_product_id_arr)) {
            $where[] = ['sub_product_id', 'in', $sub_product_id_arr];
        }
        return SubProductConfigModel::getAll($where);
    }


    /**
     * 根据账号id获取配置列表
     *
     * @param        $account_ids
     * @param        $config_item_source
     * @param array  $sub_product_item_ids
     * @param string $config_path
     *
     * @return array|mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-11-08 10:57:53
     */
    public static function getConfigOnlyAccountIds($account_ids,$sub_product_id_arr,$config_item_source,$sub_product_item_ids = [],$config_path = ''){
        if(empty($account_ids) || empty($sub_product_item_ids)){
            return [];
        }
        $where = [
            ['account_id', 'in', $account_ids],
            ['config_item_source', '=', $config_item_source],
            ['config_item_id', 'in', $sub_product_item_ids],
            ['deleted_at', 'is null', ''],
        ];

        if(!empty($config_path)) {
            $where[] = ['config_path', '=', $config_path];
        }
        if(!empty($sub_product_id_arr)) {
            $where[] = ['sub_product_id', 'in', $sub_product_id_arr];
        }
        return SubProductConfigModel::getAll($where);
    }
}