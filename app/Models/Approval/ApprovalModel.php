<?php

namespace App\Models\Approval;

use App\Models\BaseModel;
use App\Utils\Helpers\Enu;
use Exception;

class ApprovalModel extends BaseModel
{
    protected static $connection = 'rc';
    protected static $table = 'approval';


    /**
     * 检查是否存在审批
     *
     * @param $type
     * @param $group_id
     * @param $customer_id
     * @param $account_id
     * @param $product_id
     * @param $sub_product_id
     * @param $config_item_id
     * @param $config_item_source
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-11-10 19:10:10
     */
    public static function hasApproval($type,$group_id,$customer_id,$account_id,$product_id,$sub_product_id,$config_item_id,$config_item_source){
        if(empty($config_item_source)){
            $config_item_source = 0;
        }
        if(empty($product_id)){
            $product_id = 0;
        }
        if(empty($sub_product_id)){
            $sub_product_id = 0;
        }
        $where = [
            ['status', '=', Enu::APPROVAL_STATUS_NEED_APPROVAL],
            ['type', '=' ,$type],
            ['group_id', '=' ,$group_id],
            ['customer_id', '=' ,$customer_id],
            ['account_id', '=' ,$account_id],
            ['product_id', '=' ,$product_id],
            ['sub_product_id', '=' ,$sub_product_id],
            ['config_item_id', '=' ,$config_item_id],
            ['config_item_source', '=' ,$config_item_source],
        ];
        return self::getOne($where);
    }
}