<?php

namespace App\Models\System;

use App\Models\BaseModel;
use Exception;

class SystemRoleModel extends BaseModel
{
    protected static $connection = 'crs';
    protected static $table = 'crs_system_role';


    /**
     * 获取所有角色
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2023-11-06 18:54:50
     *
     * @static
     */
    public static function getAllRoles(){
        $where[] = ['disabled','=',1];
        return self::getAll($where);
    }
}