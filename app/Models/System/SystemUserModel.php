<?php

namespace App\Models\System;

use App\Models\BaseModel;
use Exception;

class SystemUserModel extends BaseModel
{
    protected static $connection = 'crs';
    protected static $table = 'crs_system_user';


    /**
     * 获取用户名map  username => realname
     *
     * @param $usernames
     *
     * @static
     * @return array
     * @throws Exception
     * <AUTHOR> 2023-10-31 17:28:25
     */
    public static function getUserNameMaps($usernames = []){
        $where = [];
        if(!empty($usernames)){
            $where[] = ['username','in',$usernames];
        }
        $res = self::getAll($where);

        return array_column($res, 'realname','username');
    }


    /**
     *
     *
     * @param $realname
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2024-03-05 17:49:39
     */
    public static function getUserByRealname($realname = []){
        $where = [];
        if(!empty($realname)){
            $where[] = ['realname','=',$realname];
        }
        return self::getOne($where);
    }
}