<?php

namespace App\Models\System;

use App\Models\BaseModel;
use Exception;

class SystemUserRoleModel extends BaseModel
{
    protected static $connection = 'crs';
    protected static $table = 'crs_system_user_role';

    /**
     *
     *
     * @param $usernames
     *
     * @static
     * @return array
     * @throws Exception
     * <AUTHOR> 2023-11-06 17:48:59
     */
    public static function getUserRoles($usernames ){
        $where[] = ['username','=',$usernames];
        $res = self::getAll($where);

        return array_column($res, 'roleid');
    }
}