<?php

namespace App\Models\Customer;

use App\Models\BaseModel;
use Exception;

class AccountModel extends BaseModel
{
    protected static $connection = 'default';
    protected static $table = 'account';

    public static function getAccountCusGroupInfosByIds($groupIds = [], $customerIds = [], $accountIds = [])
    {
        $where = [];
        if ($groupIds) {
            $where[] = " c.group_id in ('" . join("','",$groupIds) . "') ";
        }
        if ($customerIds) {
            $where[] = " c.customer_id in ('" . join("','",$customerIds) . "') ";
        }
        if ($accountIds) {
            $where[] = " a.account_id in ('" . join("','",$accountIds) . "') ";
        }
        $sql =<<<SQL
SELECT
	a.account_id,
	a.account_name,
	c.customer_id ,
	c.name as customer_name,
	c.group_id,
	cg.group_name
FROM
	account a 
	LEFT JOIN customer c on a.customer_id = c.customer_id
	LEFT JOIN customer_group cg on cg.group_id = c.group_id
SQL;
        if ($where) {
            $sql .= ' WHERE ' . join(' AND ', $where);
        }

        return self::query($sql, [], 'all');
    }

    public static function getAllCount($where = [])
    {
        return self::getCount($where);
    }

    public static function getList($where = [],$field = ['*'],$page_size = 10,$page = 1,$order = ['id','desc'])
    {
        $limit = $page_size;
        $offset = ($page - 1) * $limit;
        $page = ['limit'=>$limit,'offset'=>$offset];
        return self::getAll($where,$field,$page,$order);
    }


    /**
     * 根据客户id获取账号列表
     *
     * @param $customer_ids
     *
     * @static
     * @return array|mixed
     * @throws Exception
     * <AUTHOR> 2023-11-01 15:01:09
     */
    public static function getListByCustomerIds($customer_ids) {
        if(empty($customer_ids)){
            return [];
        }

        $where = [
            ['customer_id', 'in', $customer_ids],
            ['apikey', '!=', ''],
        ];

        return self::getAll($where);
    }

    public static function getListByCustomerId($customer_id) {
        $where = [];
        $where[] = ['apikey', '!=', ''];
        if(!empty($customer_id)){
            $where[] = ['customer_id', '=', $customer_id];
        }

        return self::getAll($where);
    }

    /**
     * 根据账号id获取列表
     *
     * @param $account_id_arr
     *
     * @static
     * @return array|mixed
     * @throws Exception
     * <AUTHOR> 2023-11-01 15:29:42
     */
    public static function getListByAccountIds($account_id_arr) {
        if(empty($account_id_arr)){
            return [];
        }

        $where = [
            ['account_id', 'in', $account_id_arr]
        ];
        return self::getAll($where);
    }


    /**
     * 根据账号id获取账号详情
     *
     * @param $account_id
     *
     * @static
     * @return array|mixed
     * @throws Exception
     * <AUTHOR> 2023-11-02 14:28:06
     */
    public static function getInfoByAccountId($account_id) {
        if(empty($account_id)){
            return [];
        }

        $where = [
            ['account_id', '=', $account_id]
        ];

        return self::getOne($where);
    }



}