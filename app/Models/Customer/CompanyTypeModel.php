<?php

namespace App\Models\Customer;

use App\Models\BaseModel;
use Exception;

class CompanyTypeModel extends BaseModel
{
    protected static $connection = 'default';
    protected static $table = 'companytype';


    /**
     * 获取全部公司类型
     *
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-10-31 17:34:19
     *
     * @static
     */
    public static function getAllTypes(){
        return self::getAll([],['id', 'parent_id', 'name']);
    }
}