<?php

namespace App\Models\Customer;

use App\Models\BaseModel;

class CustomerModel extends BaseModel
{
    protected static $connection = 'default';
    protected static $table = 'customer';


    /**
     * 根据主体id获取客户列表,没有主体返回空
     *
     * @param $group_ids
     *
     * @static
     * @return mixed
     * @throws \Exception
     * <AUTHOR> 2023-11-01 14:48:42
     */
    public static function getListByGroupIds($group_ids) {
        if(empty($group_ids)){
            return [];
        }

        $where = [
            ['group_id', 'in', $group_ids]
        ];

        return self::getAll($where);
    }

    /**
     * 根据主体获取客户,如果没有主体则为所有客户
     *
     * @param $group_id
     *
     * @static
     * @return mixed
     * @throws \Exception
     * <AUTHOR> 2023-11-06 16:54:45
     */
    public static function getListWidthGroupId($group_id) {
        $where = [];
        if(!empty($group_id)){
            $where[] = ['group_id', '=', $group_id];
        }

        return self::getAll($where);
    }



    /**
     * 根据客户id获取详情
     *
     * @param $customer_id
     *
     * @static
     * @return array|mixed
     * @throws \Exception
     * <AUTHOR> 2023-11-02 14:30:12
     */
    public static function getInfoByCustomerId($customer_id) {
        if(empty($customer_id)){
            return [];
        }

        $where = [
            ['customer_id', '=', $customer_id]
        ];

        return self::getOne($where);
    }


    /**
     * 根据客户id获取详情
     *
     * @param $customer_name
     *
     * @static
     * @return array|mixed
     * @throws \Exception
     * <AUTHOR> 2023-11-02 14:30:12
     */
    public static function getInfoByCustomerName($customer_name) {
        if(empty($customer_name)){
            return [];
        }

        $where = [
            ['name', '=', $customer_name]
        ];

        return self::getOne($where);
    }



    /**
     * 通过客户id获取列表
     *
     * @param $customer_ids
     * @param $field
     * @param $page_size
     * @param $page
     * @param $order
     *
     * @static
     * @return mixed
     * @throws \Exception
     * <AUTHOR> 2023-11-08 11:48:10
     */
    public static function getCustomerListByCustomerIds($customer_ids,$field = ['*'],$page_size = 10,$page = 1,$order = ['id','desc']){
        $limit = $page_size;
        $offset = ($page - 1) * $limit;
        $page = ['limit'=>$limit,'offset'=>$offset];
        $where = [['customer_id','in',$customer_ids]];
        return CustomerModel::getAll($where,$field,$page,$order);
    }


    /**
     * 通过客户id获取列表总数
     *
     * @param $customer_ids
     *
     * @static
     * @return int|mixed
     * @throws \Exception
     * <AUTHOR> 2023-11-08 11:48:35
     */
    public static function getCustomerCountByCustomerIds($customer_ids){
        $where = [['customer_id','in',$customer_ids]];
        return CustomerModel::getCount($where);
    }


    /**
     * 根据客户id获取列表,不分页
     *
     * @param $customer_ids
     *
     * @static
     * @return mixed
     * @throws \Exception
     * <AUTHOR> 2023-11-20 17:01:13
     */
    public static function getCustomerListByCustomerIdsNoPage($customer_ids){
        $where = [['customer_id','in',$customer_ids]];
        return CustomerModel::getAll($where);
    }
}