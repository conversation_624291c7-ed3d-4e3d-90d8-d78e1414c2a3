<?php

namespace App\Models\Contract;

use App\Models\BaseModel;
use App\Utils\Helpers\Enu;
use Exception;

class ContractModel extends BaseModel
{
    protected static $connection = 'rc';
    protected static $table      = 'contract';


    /**
     * 检查是否存在审批
     *
     * @param $contract_nos
     *
     * @return bool
     * @throws Exception
     * @static
     * <AUTHOR> 2023-11-10 19:10:10
     */
    public static function hasContract($contract_nos) {

        $where = [
            ['contract_no', 'in', $contract_nos],
            ['delated_at', 'is null', ''],
        ];
        $res = self::getAll($where);

        if(empty($res)){
            return false;
        }else{
            return implode(",",array_column($res,'contract_no'));
        }
    }


    /**
     * 根据公司名称获取合同列表
     *
     * @param $company_name
     *
     * @static
     * @return array
     * @throws Exception
     * <AUTHOR> 2024-03-05 18:51:43
     */
    public static function getListByCompanyName($company_name) {
        $where = [
            ['company_name', 'like', $company_name],
            ['delated_at', 'is null', ''],
        ];

        return self::getAll($where);
    }
}