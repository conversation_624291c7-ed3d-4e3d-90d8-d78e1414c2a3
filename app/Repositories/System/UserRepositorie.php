<?php
namespace App\Repositories\System;

use App\Models\System\SystemUserModel;
use App\Repositories\BaseRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

class UserRepositorie extends BaseRepositorie
{
    /**
     * 获取用户列表
     * @throws Exception
     */
    public static function getAllUser(){
        return SystemUserModel::getAll([]);
    }

    /**
     * 获取用户列表
     * @throws Exception
     */
    public static function getUserList($username_arr){
        $where = [['username','in',$username_arr]];
        return SystemUserModel::getAll($where);
    }

    /**
     * 获取一个用户的用户信息
     * @throws Exception
     */
    public static function getUser($username){
        $where = [['username','=',$username]];
        return SystemUserModel::getOne($where);
    }


    /**
     * @throws Exception
     */
    public static function updateTokenInfo($username, $now_str, $token, $expire_at){
        $where = [['username','=',$username]];
        $data = [
            'lastloginip'     => get_client_ip(),
            'lastlogintime'   => $now_str,
            'token'           => $token,
            'token_expire_at' => $expire_at,
        ];
        return SystemUserModel::edit($where,$data);
    }

    /**
     * @throws Exception
     */
    public static function createUser($username, $email, $now_str, $token, $expire_at){
        $data = [
            'username'        => $username,
            'email'           => $email,
            'lastloginip'     => get_client_ip(),
            'lastlogintime'   => $now_str,
            'token'           => $token,
            'token_expire_at' => $expire_at,
        ];
        return SystemUserModel::add($data);
    }


    public static function createToken($username,$now_str){
        $str = implode('-',[$username,$now_str,Enu::TOKEN_SALT]);
        return md5($str);
    }
}