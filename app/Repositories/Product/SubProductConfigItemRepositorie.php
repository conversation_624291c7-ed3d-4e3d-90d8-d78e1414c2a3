<?php
namespace App\Repositories\Product;

use App\Models\Product\SubProductConfigItemModel;
use App\Repositories\BaseRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

class SubProductConfigItemRepositorie extends BaseRepositorie
{
    /**
     * @throws Exception
     */
    public static function getAllSubProductConfigItem($product_id){
        $where = [['product_id','=',$product_id]];
        return SubProductConfigItemModel::getAll($where);
    }


    /**
     * 获取可被客户修改的子产品配置项
     *
     * @param $product_id
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-28 13:45:42
     */
    public static function getSubProductConfigItemCanModify($product_id){
        $where = [
            ['product_id','=',$product_id],
            ['modify','=',Enu::PRODUCT_CONFIG_ITEM_CUSTOMER_CAN_MODIFY],
        ];
        return SubProductConfigItemModel::getAll($where);
    }

    /**
     * 获取总数
     *
     * @param $where
     *
     * @static
     * @return int|mixed
     * @throws Exception
     * <AUTHOR> 2023-09-27 16:35:30
     */
    public static function getCount($where){
        return SubProductConfigItemModel::getCount($where);
    }


    /**
     * 添加子产品配置项
     *
     * @param $data
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-27 16:34:53
     */
    public static function createSubProductConfigItem($data){
        $now = date(Enu::TIME_FORMAT);
        $data['created_at'] = $now;
        $data['updated_at'] = $now;

        return SubProductConfigItemModel::add($data,true);
    }


    /**
     * 通过配置项id获取配置项信息
     *
     * @param $product_config_id
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-27 16:35:09
     */
    public static function getSubProductConfigItemById($product_config_id){
        $where = [['id','=',$product_config_id]];
        return SubProductConfigItemModel::getOne($where);
    }


    /**
     * 获取产品配置项
     *
     * @param $sub_product_ids
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-10-11 19:41:25
     */
    public static function getConfigItemListBySubProductIds($sub_product_ids){
        $where = [['product_id','in',$sub_product_ids]];
        return SubProductConfigItemModel::getAll($where);
    }
}