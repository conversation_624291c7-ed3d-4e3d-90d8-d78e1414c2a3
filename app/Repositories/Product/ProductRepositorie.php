<?php
namespace App\Repositories\Product;

use App\Models\Product\ProductModel;
use App\Repositories\BaseRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

class ProductRepositorie extends BaseRepositorie
{
    /**
     * @throws Exception
     */
    public static function getProductList( $where = [],$field = ['*'],$page_size = 10,$page = 1,$order = ['id','desc']){
        $limit = $page_size;
        $offset = ($page - 1) * $limit;
        $page = ['limit'=>$limit,'offset'=>$offset];
        $where[] = ['father_id','in',[0,401]];
        return ProductModel::getAll($where,$field,$page,$order);
    }


    /**
     * @throws Exception
     */
    public static function getProductCount($where){
        $where[] = ['father_id','in',[0,401]];
        return ProductModel::getCount($where);
    }


    /**
     * 主产品是否存在子产品
     *
     * @param $product_ids
     *
     * @static
     * @return array
     * @throws Exception
     * <AUTHOR> 2023-09-25 17:12:51
     */
    public static function productHasSubProduct($product_ids){
        $where = [['father_id','in',$product_ids]];
        $sub_product_list = ProductModel::getAll($where);
        $res = [];
        foreach($product_ids as $product_id){
            $res[$product_id] = false;
        }
        foreach($sub_product_list as $sub_product_info){
            $father_id = $sub_product_info['father_id'];
            $res[$father_id] = true;

        }
        return $res;
    }


    /**
     * 获取可用的主产品
     *
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-25 18:16:27
     *
     * @static
     */
    public static function getAllProduct(){
        $where = [];
        return ProductModel::getAll($where);
    }


    /**
     * 添加产品
     *
     * @param $product_id
     * @param $product_name
     * @param $product_key
     * @param $admin
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-09-26 11:09:41
     */
    public static function createProduct($product_id,$product_name,$product_key,$admin){
        $now = time();//date(Enu::TIME_FORMAT);
        $data = [
            'product_id'   => $product_id,
            'product_name' => $product_name,
            'product_key'  => $product_key,
            'admin'        => $admin,
            'create_at'   => $now,
            'update_at'   => $now,
        ];
        return ProductModel::add($data,true);
    }


    /**
     * 编辑产品名称
     *
     * @param $product_id
     * @param $product_name
     * @param $admin
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-26 16:29:57
     */
    public static function edit($product_id,$product_name,$admin){
        $now = time();// date(Enu::TIME_FORMAT);
        $where = [['product_id','=',$product_id]];
        $data = [
            'product_name' => $product_name,
            'admin'        => $admin,
            'update_at'   => $now,
        ];
        return ProductModel::edit($where,$data);
    }


    /**
     * 获取产品详情
     *
     * @param $product_id
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-26 16:47:26
     */
    public static function info($product_id,$fields = ['*']){
        return ProductModel::getOne([['product_id','=',$product_id]],$fields);
    }


    /**
     * 根据产品id获取列表
     *
     * @param $product_ids
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-28 02:50:47
     */
    public static function getProductListByProductIds($product_ids){
        $where = [['product_id','in',$product_ids]];
        return ProductModel::getAll($where);
    }


    /**
     * 获取主产品列表
     *
     * @param $product_ids
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-10-09 10:32:59
     */
    public static function getMainProductListByProductIds($product_ids){
        $where = [
            ['product_id','in',$product_ids],
            ['father_id','in',[0,401]]
        ];
        return ProductModel::getAll($where);
    }
}