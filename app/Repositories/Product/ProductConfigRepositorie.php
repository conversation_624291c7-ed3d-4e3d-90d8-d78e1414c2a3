<?php
namespace App\Repositories\Product;

use App\Models\Product\ProductConfigModel;
use App\Repositories\BaseRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

class ProductConfigRepositorie extends BaseRepositorie
{


    /**
     * 获取该产品的配置
     *
     * @param $product_id
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-26 17:59:22
     */
    public static function getProductConfigList($product_id){
        $where = [
            ['product_id', '=', $product_id],
            ['group_id', '=', ''],
            ['customer_id', '=', ''],
            ['account_id', '=', ''],
            ['deleted_at', 'is null', ''],
        ];
        return ProductConfigModel::getAll($where);
    }


    /**
     * 根据产品id和配置项id获取配置信息
     *
     * @param $product_id
     * @param $config_item_id
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-27 00:02:16
     */
    public static function getProductConfigByProductIdAndItemId($product_id,$config_item_id){
        $where = [
            ['product_id', '=', $product_id],
            ['group_id', '=', ''],
            ['customer_id', '=', ''],
            ['account_id', '=', ''],
            ['config_item_id', '=', $config_item_id],
            ['deleted_at', 'is null', ''],
        ];
        return ProductConfigModel::getOne($where);
    }


    /**
     * 更新配置值
     *
     * @param $product_id
     * @param $config_item_id
     * @param $new_value
     * @param $admin
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-27 00:06:25
     */
    public static function edit($product_id,$config_item_id,$new_value,$admin,$config_path){
        $now = date(Enu::TIME_FORMAT);
        $where = [
            ['product_id', '=', $product_id],
            ['group_id', '=', ''],
            ['customer_id', '=', ''],
            ['account_id', '=', ''],
            ['config_item_id', '=', $config_item_id],
            ['config_path', '=', $config_path],
            ['deleted_at', 'is null', ''],
        ];

        $data = [
            'item_value' => $new_value,
            'updated_at' => $now,
            'admin' => $admin
        ];
        return ProductConfigModel::edit($where,$data);
    }

    /**
     * 添加配置
     *
     * @param $product_id
     * @param $config_item_id
     * @param $new_value
     * @param $admin
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-27 00:09:27
     */
    public static function add($product_id,$config_item_id,$new_value,$admin,$config_path){
        $now = date(Enu::TIME_FORMAT);
        $data = [
            'product_id'     => $product_id,
            'config_item_id' => $config_item_id,
            'group_id'       => '',
            'customer_id'    => '',
            'account_id'     => '',
            'item_value'     => $new_value,
            'config_path'    => $config_path,
            'created_at'     => $now,
            'updated_at'     => $now,
            'admin'          => $admin,
        ];
        return ProductConfigModel::add($data);
    }


    /**
     * 获取主体-产品配置值
     *
     * @param $product_id
     * @param $group_id
     * @param $product_config_item_ids
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-28 11:37:56
     */
    public static function getProductGroupConfigList($product_id,$group_id,$product_config_item_ids){
        $where = [
            ['product_id', '=', $product_id],
            ['group_id', '=', $group_id],
            ['customer_id', '=', ''],
            ['account_id', '=', ''],
            ['config_item_id', 'in', $product_config_item_ids],
            ['deleted_at', 'is null', ''],
        ];
        return ProductConfigModel::getAll($where);
    }

    /**
     * 获取客户-产品配置值
     *
     * @param $product_id
     * @param $customer_id
     * @param $product_config_item_ids
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-28 14:52:05
     */
    public static function getProductCustomerConfigList($product_id,$customer_id,$product_config_item_ids){
        $where = [
            ['product_id', '=', $product_id],
            ['customer_id', '=', $customer_id],
            ['account_id', '=', ''],
            ['config_item_id', 'in', $product_config_item_ids],
            ['deleted_at', 'is null', ''],
        ];
        return ProductConfigModel::getAll($where);
    }

    /**
     * 获取账户-产品配置值
     *
     * @param $product_id
     * @param $account_id
     * @param $product_config_item_ids
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-28 14:52:05
     */
    public static function getProductAccountConfigList($product_id,$account_id,$product_config_item_ids){
        $where = [
            ['product_id', '=', $product_id],
            ['account_id', '=', $account_id],
            ['config_item_id', 'in', $product_config_item_ids],
            ['deleted_at', 'is null', ''],
        ];
        return ProductConfigModel::getAll($where);
    }


    /**
     * 根据主体,客户,账号,配置项id获取配置信息
     *
     * @param $product_id
     * @param $group_id
     * @param $customer_id
     * @param $account_id
     * @param $config_item_id
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-10-07 19:59:40
     */
    public static function infoWithGCA($product_id,$group_id,$customer_id,$account_id,$config_item_id){
        $where = [
            ['product_id', '=', $product_id],
            ['config_item_id', '=', $config_item_id],
            ['deleted_at', 'is null', ''],
        ];

        gca_where($where,$group_id,$customer_id,$account_id);

        return ProductConfigModel::getOne($where);
    }


    /**
     * 根据主体,客户,账号,配置项id修改配置项值
     *
     * @param $product_id
     * @param $group_id
     * @param $customer_id
     * @param $account_id
     * @param $config_item_id
     * @param $new_value
     * @param $admin
     * @param $config_path
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-10-07 20:01:29
     */
    public static function editWithGCA($product_id,$group_id,$customer_id,$account_id,$config_item_id,$new_value,$admin,$config_path){
        $now = date(Enu::TIME_FORMAT);
        $where = [
            ['product_id', '=', $product_id],
            ['config_item_id', '=', $config_item_id],
            ['config_path', '=', $config_path],
            ['deleted_at', 'is null', ''],
        ];

        gca_where($where,$group_id,$customer_id,$account_id);

        $data = [
            'item_value' => $new_value,
            'updated_at' => $now,
            'admin'      => $admin,
        ];
        return ProductConfigModel::edit($where,$data);
    }


    /**
     * 删除客户产品配置
     *
     * @param $product_id
     * @param $group_id
     * @param $customer_id
     * @param $account_id
     * @param $config_item_id
     * @param $admin
     * @param $config_path
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-11-07 18:24:14
     */
    public static function deleteWithGCA($product_id,$group_id,$customer_id,$account_id,$config_item_id,$admin,$config_path){
        $now = date(Enu::TIME_FORMAT);
        $where = [
            ['product_id', '=', $product_id],
            ['config_item_id', '=', $config_item_id],
            ['config_path', '=', $config_path],
            ['deleted_at', 'is null', ''],
        ];

        gca_where($where,$group_id,$customer_id,$account_id);

        $data = [
            'updated_at' => $now,
            'deleted_at' => $now,
            'admin'      => $admin,
        ];
        return ProductConfigModel::edit($where,$data);
    }



    /**
     * 添加配置项值
     *
     * @param      $product_id
     * @param      $group_id
     * @param      $customer_id
     * @param      $account_id
     * @param      $config_item_id
     * @param      $new_value
     * @param      $admin
     * @param null $config_item_source 保持参数统一
     * @param      $config_path
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-10-07 20:01:54
     */
    public static function addWithGCA($product_id,$group_id,$customer_id,$account_id,$config_item_id,$new_value,$admin,$config_path,$config_item_source = null){

        $now = date(Enu::TIME_FORMAT);
        $data = [
            'config_path'    => $config_path,
            'product_id'     => $product_id,
            'config_item_id' => $config_item_id,
            'group_id'       => $group_id,
            'customer_id'    => $customer_id,
            'account_id'     => $account_id,
            'item_value'     => $new_value,
            'created_at'     => $now,
            'updated_at'     => $now,
            'admin'          => $admin,
        ];
        return ProductConfigModel::add($data);
    }


    /**
     * 根据主体id获取配置列表
     *
     * @param $group_ids
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-10-11 10:49:58
     */
    public static function getConfigWithGroupIds($group_ids){
        if(empty($group_ids)){
            return [];
        }
        $where = [
            ['group_id', 'in', $group_ids],
            ['deleted_at', 'is null', ''],
        ];
        return ProductConfigModel::getAll($where);
    }

    /**
     * 根据客户id获取配置列表
     *
     * @param $customer_ids
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-10-11 10:49:58
     */
    public static function getConfigWithCustomerIds($customer_ids){
        if(empty($customer_ids)){
            return [];
        }
        $where = [
            ['customer_id', 'in', $customer_ids],
            ['deleted_at', 'is null', ''],
        ];
        return ProductConfigModel::getAll($where);
    }


    /**
     * 根据账号id获取配置列表
     *
     * @param $account_ids
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-10-11 10:49:58
     */
    public static function getConfigWithAccountIds($account_ids){
        if(empty($account_ids)){
            return [];
        }
        $where = [
            ['account_id', 'in', $account_ids],
            ['deleted_at', 'is null', ''],
        ];
        return ProductConfigModel::getAll($where);
    }

    public static function getConfigWithGroupIdsPids($group_ids = [], $pids = []){
        $where = [
            ['deleted_at', 'is null', ''],
            ['customer_id', '=', ''],
            ['account_id', '=', ''],
        ];
        if ($group_ids) {
            $where[] = ['group_id', 'in', $group_ids];
        }
        if ($pids) {
            $where[] = ['product_id', 'in', $pids];
        }
        return ProductConfigModel::getAll($where);
    }

    public static function getConfigWithCusIdsPids($cus_ids = [], $pids = []){
        $where = [
            ['deleted_at', 'is null', ''],
            ['account_id', '=', ''],
        ];
        if ($cus_ids) {
            $where[] = ['customer_id', 'in', $cus_ids];
        }
        if ($pids) {
            $where[] = ['product_id', 'in', $pids];
        }
        return ProductConfigModel::getAll($where);
    }

    public static function getConfigWithAccountIdsPids($account_ids = [], $pids = []){
        $where = [
            ['deleted_at', 'is null', '']
        ];
        if ($account_ids) {
            $where[] = ['account_id', 'in', $account_ids];
        }
        if ($pids) {
            $where[] = ['product_id', 'in', $pids];
        }
        return ProductConfigModel::getAll($where);
    }
}