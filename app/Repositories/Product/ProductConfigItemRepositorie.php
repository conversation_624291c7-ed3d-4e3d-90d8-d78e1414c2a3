<?php
namespace App\Repositories\Product;

use App\Models\Product\ProductConfigItemModel;
use App\Repositories\BaseRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

class ProductConfigItemRepositorie extends BaseRepositorie
{
    /**
     * @throws Exception
     */
    public static function getAllProductConfigItem($product_id){
        $where = [['product_id','=',$product_id]];
        return ProductConfigItemModel::getAll($where);
    }

    /**
     * 获取 子产品不可继承 客户可修改 的配置项
     *
     * @param $product_id
     * @param $role
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-09-28 11:28:41
     */
    public static function getProductConfigItemsCanModifyCanNotExtend($product_id, $role){
        $where = [
            ['product_id', '=', $product_id],
            ['role', 'in', $role],
            ['extend', '=', Enu::PRODUCT_CONFIG_ITEM_SUB_CAN_NOT_EXTEND],
            ['modify', '=', Enu::PRODUCT_CONFIG_ITEM_CUSTOMER_CAN_MODIFY],
        ];
        return ProductConfigItemModel::getAll($where);
    }


    /**
     * 获取 客户可修改 的配置项
     *
     * @param $product_id
     * @param $role
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-11-02 18:42:30
     */
    public static function getProductConfigItemsCanModify($product_id, $role){
        $where = [
            ['product_id', '=', $product_id],
            ['role', 'in', $role],
            ['modify', '=', Enu::PRODUCT_CONFIG_ITEM_CUSTOMER_CAN_MODIFY],
        ];
        return ProductConfigItemModel::getAll($where);
    }

    /**
     * 获取 子产品可继承 客户可修改 的配置项
     *
     * @param $product_id
     * @param $role
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-09-28 13:48:59
     */
    public static function getProductConfigItemsCanModifyCanExtend($product_id, $role){
        $where = [
            ['product_id', '=', $product_id],
            ['role', 'in', $role],
            ['extend', '=', Enu::PRODUCT_CONFIG_ITEM_SUB_CAN_EXTEND],
            ['modify', '=', Enu::PRODUCT_CONFIG_ITEM_CUSTOMER_CAN_MODIFY],
        ];
        return ProductConfigItemModel::getAll($where);
    }

    /**
     * 获取总数
     *
     * @param $where
     *
     * @static
     * @return int|mixed
     * @throws Exception
     * <AUTHOR> 2023-09-26 13:52:52
     */
    public static function getCount($where){
        return ProductConfigItemModel::getCount($where);
    }


    /**
     * 添加主产品配置项
     *
     * @param $data
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-26 14:35:29
     */
    public static function createProductConfigItem($data){
        $now = date(Enu::TIME_FORMAT);
        $data['created_at'] = $now;
        $data['updated_at'] = $now;

        return ProductConfigItemModel::add($data,true);
    }


    /**
     * 通过配置项id获取配置项信息
     *
     * @param $product_config_id
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-26 23:47:38
     */
    public static function getProductConfigItemById($product_config_id){
        $where = [['id','=',$product_config_id]];
        return ProductConfigItemModel::getOne($where);
    }


    /**
     * 获取产品配置项
     *
     * @param $product_ids
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-10-11 19:41:25
     */
    public static function getConfigItemListByProductIds($product_ids){
        $where = [['product_id','in',$product_ids]];
        return ProductConfigItemModel::getAll($where);
    }
}