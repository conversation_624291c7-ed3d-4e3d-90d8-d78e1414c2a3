<?php
namespace App\Repositories\Product;

use App\Models\Product\AccountProductModel;
use App\Repositories\BaseRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

class AccountProductRepositorie extends BaseRepositorie
{

    /**
     *
     *
     * @throws Exception
     */
    public static function getListWithAccounts($account_ids){
        $where = [
            ['account_id','in',$account_ids],
        ];
        return AccountProductModel::getAll($where);
    }

    /**
     *
     *
     * @param $product_ids
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-09-28 02:05:04
     */
    public static function getListWithProductIds($product_ids){
        $where = [
            ['product_id','in',$product_ids],
        ];
        return AccountProductModel::getAll($where);
    }

    /**
     * 添加客户产品
     *
     * @param $data
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-28 16:20:07
     */
    public static function add($data){
        return AccountProductModel::add($data,true);
    }
}