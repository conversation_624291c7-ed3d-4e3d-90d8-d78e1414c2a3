<?php
namespace App\Repositories\Product;

use App\Models\Product\ProductModel;
use App\Repositories\BaseRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

class SubProductRepositorie extends BaseRepositorie
{
    /**
     * 获取可用的主产品
     *
     * @param $father_id
     *
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-27 18:00:36
     *
     * @static
     */
    public static function getAllSubProduct($father_id = ''){
        if(!empty($father_id)){
            $where[] = ['father_id','=',$father_id];
        }else {
            $where[] = ['father_id', 'not in', [0, 401]];
        }

        return ProductModel::getAll($where);
    }

    /**
     * @throws Exception
     */
    public static function getSubProductList( $where = [],$field = ['*'],$page_size = 10,$page = 1,$order = ['id','desc']){
        $limit = $page_size;
        $offset = ($page - 1) * $limit;
        $page = ['limit'=>$limit,'offset'=>$offset];
        return ProductModel::getAll($where,$field,$page,$order);
    }


    /**
     * @throws Exception
     */
    public static function getSubProductCount($where){
        return ProductModel::getCount($where);
    }

    /**
     * 添加子产品
     *
     * @param $father_id
     * @param $product_id
     * @param $product_name
     * @param $product_key
     * @param $admin
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-27 14:38:53
     */
    public static function createProduct($father_id,$product_id,$product_name,$product_key,$admin){
        $now = date(Enu::TIME_FORMAT);
        $data = [
            'father_id'    => $father_id,
            'product_id'   => $product_id,
            'product_name' => $product_name,
            'product_key'  => $product_key,
            'admin'        => $admin,
            'created_at'   => $now,
            'updated_at'   => $now,
        ];
        return ProductModel::add($data,true);
    }


    /**
     * 编辑子产品
     *
     * @param $product_id
     * @param $product_name
     * @param $admin
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-09-27 14:45:54
     */
    public static function edit($product_id,$product_name,$admin){
        $where = [
            ['product_id','=',$product_id],
        ];
        $data = [
            'product_name' => $product_name,
            'admin'        => $admin,
            'update_at'    => time(),
        ];
        return ProductModel::edit($where,$data);
    }

    /**
     * 获取产品详情
     *
     * @param $product_id
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-27 16:46:30
     */
    public static function info($product_id,$fields = ['product_id','product_name','product_key','admin','father_id']){
        $where = [
            ['product_id','=',$product_id],
            ['father_id','not in',[0,401]],
        ];
        return ProductModel::getOne($where,$fields);
    }


    /**
     * 根据产品id获取列表
     *
     * @param $product_ids
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-28 02:51:52
     */
    public static function getSubProductListByProductIds($product_ids){
        $where = [
            ['product_id','in',$product_ids],
            ['father_id','not in',[0,401]],
        ];
        return ProductModel::getAll($where);
    }


    /**
     * 
     *
     * @param $sub_product_ids
     *
     * @static
     * @return array
     * @throws Exception
     * <AUTHOR> 2023-10-12 10:40:58
     */
    public static function getProductMap($sub_product_ids){
        $where = [
            ['product_id', 'in', $sub_product_ids],
        ];
        $sub_product_list = ProductModel::getAll($where);

        $product_map = [];
        array_map(function($item) use (&$product_map){
            $product_map[$item['product_id']] = intval($item['father_id']);
        },$sub_product_list);

        return $product_map;
    }
}