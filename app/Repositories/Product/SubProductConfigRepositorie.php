<?php
namespace App\Repositories\Product;

use App\Models\Product\SubProductConfigModel;
use App\Repositories\BaseRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

class SubProductConfigRepositorie extends BaseRepositorie
{


    /**
     * 获取该产品的配置
     *
     * @param $product_id
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-26 17:59:22
     */
    public static function getSubProductConfigList($product_id){
        $where = [
            ['sub_product_id', '=', $product_id],
            ['group_id', '=', ''],
            ['customer_id', '=', ''],
            ['account_id', '=', ''],
            ['deleted_at', 'is null', ''],
        ];
        return SubProductConfigModel::getAll($where);
    }


    /**
     * 根据产品id和配置项id获取配置信息
     *
     * @param $product_id
     * @param $item_id
     * @param $config_item_source
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-09-27 00:02:16
     */
    public static function getSubProductConfigByProductIdAndItemId($product_id,$item_id,$config_item_source){
        $where = [
            ['sub_product_id', '=', $product_id],
            ['group_id', '=', ''],
            ['customer_id', '=', ''],
            ['account_id', '=', ''],
            ['config_item_id', '=', $item_id],
            ['config_item_source', '=', $config_item_source],
            ['deleted_at', 'is null', ''],
        ];
        return SubProductConfigModel::getOne($where);
    }


    /**
     * 更新 子产品 配置值
     *
     * @param $product_id
     * @param $item_id
     * @param $new_value
     * @param $admin
     * @param $config_item_source
     * @param $config_path
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-09-27 00:06:25
     */
    public static function edit($product_id,$item_id,$new_value,$admin,$config_item_source,$config_path){
        $now = date(Enu::TIME_FORMAT);
        $where = [
            ['sub_product_id', '=', $product_id],
            ['group_id', '=', ''],
            ['customer_id', '=', ''],
            ['account_id', '=', ''],
            ['config_item_id', '=', $item_id],
            ['deleted_at', 'is null', ''],
            ['config_path', '=', $config_path],
            ['config_item_source', '=', $config_item_source],
        ];

        $data = [
            'item_value'         => $new_value,
            'updated_at'         => $now,
            'admin'              => $admin,
        ];
        return SubProductConfigModel::edit($where,$data);
    }

    /**
     * 添加 子产品 配置
     *
     * @param $product_id
     * @param $item_id
     * @param $new_value
     * @param $admin
     * @param $config_item_source
     * @param $config_path
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-09-27 00:09:27
     */
    public static function add($product_id,$item_id,$new_value,$admin,$config_item_source,$config_path){
        $now = date(Enu::TIME_FORMAT);
        $data = [
            'sub_product_id'     => $product_id,
            'config_item_id'     => $item_id,
            'group_id'           => '',
            'customer_id'        => '',
            'account_id'         => '',
            'item_value'         => $new_value,
            'created_at'         => $now,
            'updated_at'         => $now,
            'admin'              => $admin,
            'config_item_source' => $config_item_source,
            'config_path'        => $config_path,
        ];
        return SubProductConfigModel::add($data);
    }


    /**
     * 根据主体,客户,账号,配置项id获取配置信息
     *
     * @param $sub_product_id
     * @param $group_id
     * @param $customer_id
     * @param $account_id
     * @param $config_item_id
     * @param $config_item_source
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-10-07 19:59:40
     */
    public static function infoWithGCA($sub_product_id,$group_id,$customer_id,$account_id,$config_item_id,$config_item_source){
        $where = [
            ['sub_product_id', '=', $sub_product_id],
            ['config_item_id', '=', $config_item_id],
            ['config_item_source', '=', $config_item_source],
            ['deleted_at', 'is null', ''],
        ];

        gca_where($where,$group_id,$customer_id,$account_id);

        return SubProductConfigModel::getOne($where);
    }


    /**
     * 根据主体,客户,账号,配置项id修改配置项值
     *
     * @param $sub_product_id
     * @param $group_id
     * @param $customer_id
     * @param $account_id
     * @param $config_item_id
     * @param $new_value
     * @param $admin
     * @param $config_path
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-10-07 20:01:29
     */
    public static function editWithGCA($sub_product_id,$group_id,$customer_id,$account_id,$config_item_id,$new_value,$admin,$config_path){
        $now = date(Enu::TIME_FORMAT);
        $where = [
            ['sub_product_id', '=', $sub_product_id],
            ['config_item_id', '=', $config_item_id],
            ['config_path', '=', $config_path],
            ['deleted_at', 'is null', ''],
        ];

        gca_where($where,$group_id,$customer_id,$account_id);

        $data = [
            'item_value' => $new_value,
            'updated_at' => $now,
            'admin' => $admin
        ];
        return SubProductConfigModel::edit($where,$data);
    }


    /**
     * 删除客户产品配置
     *
     * @param $sub_product_id
     * @param $group_id
     * @param $customer_id
     * @param $account_id
     * @param $config_item_id
     * @param $admin
     * @param $config_path
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-11-07 18:22:56
     */
    public static function deleteWithGCA($sub_product_id,$group_id,$customer_id,$account_id,$config_item_id,$admin,$config_path){
        $now = date(Enu::TIME_FORMAT);
        $where = [
            ['sub_product_id', '=', $sub_product_id],
            ['config_item_id', '=', $config_item_id],
            ['config_path', '=', $config_path],
            ['deleted_at', 'is null', ''],
        ];

        gca_where($where,$group_id,$customer_id,$account_id);

        $data = [
            'updated_at' => $now,
            'deleted_at' => $now,
            'admin' => $admin
        ];
        return SubProductConfigModel::edit($where,$data);
    }


    /**
     * 添加配置项值
     *
     * @param     $sub_product_id
     * @param     $group_id
     * @param     $customer_id
     * @param     $account_id
     * @param     $config_item_id
     * @param     $new_value
     * @param     $admin
     * @param int $config_item_source
     * @param     $config_path
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-10-07 20:01:54
     */
    public static function addWithGCA($sub_product_id,$group_id,$customer_id,$account_id,$config_item_id,$new_value,$admin,$config_path,$config_item_source = 1){
        $now = date(Enu::TIME_FORMAT);
        $data = [
            'config_path'        => $config_path,
            'sub_product_id'     => $sub_product_id,
            'config_item_source' => $config_item_source,
            'config_item_id'     => $config_item_id,
            'group_id'           => $group_id,
            'customer_id'        => $customer_id,
            'account_id'         => $account_id,
            'item_value'         => $new_value,
            'created_at'         => $now,
            'updated_at'         => $now,
            'admin'              => $admin,
        ];
        return SubProductConfigModel::add($data);
    }

    public static function getConfigWithGroupIdsPids($group_ids = [], $pids = []){
        $where = [
            ['deleted_at', 'is null', ''],
            ['customer_id', '=', ''],
            ['account_id', '=', ''],
        ];
        if ($group_ids) {
            $where[] = ['group_id', 'in', $group_ids];
        }
        if ($pids) {
            $where[] = ['sub_product_id', 'in', $pids];
        }
        return SubProductConfigModel::getAll($where);
    }

    public static function getConfigWithCusIdsPids($cus_ids = [], $pids = []){
        $where = [
            ['deleted_at', 'is null', ''],
            ['account_id', '=', ''],
        ];
        if ($cus_ids) {
            $where[] = ['customer_id', 'in', $cus_ids];
        }
        if ($pids) {
            $where[] = ['sub_product_id', 'in', $pids];
        }
        return SubProductConfigModel::getAll($where);
    }

    public static function getConfigWithAccountIdsPids($account_ids = [], $pids = []){
        $where = [
            ['deleted_at', 'is null', '']
        ];
        if ($account_ids) {
            $where[] = ['account_id', 'in', $account_ids];
        }
        if ($pids) {
            $where[] = ['sub_product_id', 'in', $pids];
        }
        return SubProductConfigModel::getAll($where);
    }
    /**
     * 获取主体-子产品配置值
     *
     * @param $group_id
     * @param $sub_product_id
     * @param $sub_product_config_item_ids
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-28 14:18:31
     */
    public static function getSubProductGroupConfigList($group_id, $sub_product_id, $sub_product_config_item_ids){
        $where = [
            ['sub_product_id', '=', $sub_product_id],
            ['group_id', '=', $group_id],
            ['customer_id', '=', ''],
            ['account_id', '=', ''],
            ['config_item_id', 'in', $sub_product_config_item_ids],
            ['deleted_at', 'is null', ''],
        ];
        return SubProductConfigModel::getAll($where);
    }

    /**
     * 获取客户-子产品配置值
     *
     * @param $customer_id
     * @param $sub_product_id
     * @param $sub_product_config_item_ids
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-28 14:56:26
     */
    public static function getSubProductCustomerConfigList($customer_id, $sub_product_id, $sub_product_config_item_ids,$config_item_source){
        $where = [
            ['sub_product_id', '=', $sub_product_id],
            ['customer_id', '=', $customer_id],
            ['account_id', '=', ''],
            ['config_item_source', '=', $config_item_source],
            ['config_item_id', 'in', $sub_product_config_item_ids],
            ['deleted_at', 'is null', ''],
        ];
        return SubProductConfigModel::getAll($where);
    }


    /**
     * 获取账号-子产品配置值
     *
     * @param $account_id
     * @param $sub_product_id
     * @param $sub_product_config_item_ids
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-28 14:56:37
     */
    public static function getSubProductAccountConfigList($account_id, $sub_product_id, $sub_product_config_item_ids){
        $where = [
            ['sub_product_id', '=', $sub_product_id],
            ['account_id', '=', $account_id],
            ['config_item_id', 'in', $sub_product_config_item_ids],
            ['deleted_at', 'is null', ''],
        ];
        return SubProductConfigModel::getAll($where);
    }



    /**
     * 根据主体id获取配置列表
     *
     * @param $group_ids
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-10-11 18:06:26
     */
    public static function getConfigWithGroupIds($group_ids){
        if(empty($group_ids)){
            return [];
        }
        $where = [
            ['group_id', 'in', $group_ids],
            ['deleted_at', 'is null', ''],
        ];
        return SubProductConfigModel::getAll($where);
    }


    /**
     * 根据客户id获取配置列表
     *
     * @param $customer_ids
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-10-11 18:33:16
     */
    public static function getConfigWithCustomerIds($customer_ids){
        if(empty($customer_ids)){
            return [];
        }
        $where = [
            ['customer_id', 'in', $customer_ids],
            ['deleted_at', 'is null', ''],
        ];
        return SubProductConfigModel::getAll($where);
    }


    /**
     * 根据账号id获取配置列表
     *
     * @param $account_ids
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-10-11 19:03:09
     */
    public static function getConfigWithAccountIds($account_ids){
        if(empty($account_ids)){
            return [];
        }
        $where = [
            ['account_id', 'in', $account_ids],
            ['deleted_at', 'is null', ''],
        ];
        return SubProductConfigModel::getAll($where);
    }
}