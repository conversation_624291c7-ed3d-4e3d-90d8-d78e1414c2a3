<?php
namespace App\Repositories;

use App\Repositories\Customer\AccountRepositorie;
use App\Repositories\Customer\CustomerGroupRepositorie;
use App\Repositories\Customer\CustomerRepositorie;
use App\Repositories\Product\ProductRepositorie;
use App\Repositories\Product\SubProductRepositorie;
use Exception;

class OptionsRepositorie extends BaseRepositorie
{

    /**
     * 产品选项列表
     * 所有产品
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2023-10-10 15:58:32
     *
     * @static
     */
    public static function product_options($product_id){
        $_product_id_arr = SubProductRepositorie::getAllSubProduct($product_id);
        $product_id_arr = [];
        foreach($_product_id_arr as $product_info){
            $product_id_arr[] = [
                'value' => $product_info['product_id'],
                'label' => $product_info['product_name'],
            ];
        }
        return $product_id_arr;
    }


    /**
     * 主体选项
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2023-10-10 16:01:51
     *
     * @static
     */
    public static function group_options() {
        $_group_id_arr = CustomerGroupRepositorie::getAllCustomerGroup();
        $group_id_arr  = [];
        foreach ($_group_id_arr as $group_info) {
            $group_id_arr[] = [
                'value' => $group_info['group_id'],
                'label' => $group_info['group_name'],
            ];
        }
        return $group_id_arr;
    }


    /**
     * 客户筛选
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2023-10-10 16:02:39
     *
     * @static
     */
    public static function customer_options(){
        $_customer_id_arr = CustomerRepositorie::getAllCustomer();
        $customer_id_arr = [];
        foreach($_customer_id_arr as $customer_info){
            $customer_id_arr[] = [
                'value' => $customer_info['customer_id'],
                'label' => $customer_info['name'],
            ];
        }
        return $customer_id_arr;
    }


    /**
     * 账号筛选
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2023-10-10 16:02:39
     *
     * @static
     */
    public static function account_options(){
        $_customer_id_arr = AccountRepositorie::getAllAccount();
        $customer_id_arr = [];
        foreach($_customer_id_arr as $customer_info){
            $customer_id_arr[] = [
                'value' => $customer_info['account_id'],
                'label' => $customer_info['account_name'],
            ];
        }
        return $customer_id_arr;
    }
}