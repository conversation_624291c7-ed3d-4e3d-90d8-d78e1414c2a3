<?php

namespace App\Repositories\Customer;


use App\Models\Customer\CustomerGroupModel;
use App\Repositories\BaseRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

class CustomerGroupRepositorie extends BaseRepositorie
{

    /**
     * 获取列表
     *
     * @param array    $where
     * @param string[] $field
     * @param int      $page_size
     * @param int      $page
     *
     * @return int|mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-09-18 18:52:33
     */
    public static function getCustomerGroupList($where = [], $field = ['*'], $page_size = 10, $page = 1, $order = []) {
        $limit  = $page_size;
        $offset = ($page - 1) * $limit;
        $page   = ['limit' => $limit, 'offset' => $offset];
        return CustomerGroupModel::getAll($where, $field, $page, $order);
    }

    /**
     * 根据主体名称查询主体
     *
     * @param $group_name
     * @param $group_id string 排除这个group_id的主体
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-19 01:05:41
     */
    public static function getCustomerGroupCountByName($group_name,$group_id = '') {
        $where = [];
        $where[] = ['group_name','=',$group_name];
        if(!empty($group_id)){
            $where[] = ['group_id','!=',$group_id];
        }
        return CustomerGroupModel::getCount($where);
    }


    /**
     * 获取总数
     *
     * @param $where
     *
     * @static
     * @return int|mixed
     * @throws Exception
     * <AUTHOR> 2023-09-18 18:52:33
     */
    public static function getCustomerGroupCount($where) {
        return CustomerGroupModel::getCount($where);
    }


    /**
     * 获取主体列表
     *
     * @param array    $where
     * @param string[] $field
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-09-18 18:54:42
     */
    public static function getAllCustomerGroup($where = [], $field = ['group_id', 'group_name']) {
        $where[] = ['status', '=', Enu::CUSTOMER_GROUP_STATUS_ENABLE];
        return CustomerGroupModel::getAll($where, $field);
    }

    /**
     * 获取主体详情
     *
     * @param $group_id
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-09-19 01:15:42
     */
    public static function getCustomerGroupInfo($group_id) {
        $where = [['group_id', '=',$group_id]];
        return CustomerGroupModel::getOne($where);
    }

    /**
     * 添加主体
     *
     * @param $group_id
     * @param $group_name
     * @param $admin
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-09-19 00:27:49
     */
    public static function add($group_id, $group_name,$admin) {
        $now  = date(Enu::TIME_FORMAT);
        $data = [
            'group_id'   => $group_id,
            'group_name' => $group_name,
            'admin'      => $admin,
            'created_at' => $now,
            'updated_at' => $now,
        ];
        return CustomerGroupModel::add($data);
    }


    /**
     * 编辑主体 只有名称可以进行编辑
     *
     * @param $group_id
     * @param $group_name
     * @param $admin
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-09-19 00:30:29
     */
    public static function edit($group_id, $group_name,$admin) {
        $now   = date(Enu::TIME_FORMAT);
        $where = [['group_id', '=', $group_id]];
        $data  = [
            'group_name' => $group_name,
            'admin'      => $admin,
            'updated_at' => $now,
        ];
        return CustomerGroupModel::edit($where, $data);
    }


    /**
     * 根据group_id获取列表
     *
     * @param $group_ids
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-28 02:57:49
     */
    public static function getCustomerGroupListByGrouupIds($group_ids){
        $where = [['group_id','in',$group_ids]];
        return CustomerGroupModel::getAll($where);
    }
}