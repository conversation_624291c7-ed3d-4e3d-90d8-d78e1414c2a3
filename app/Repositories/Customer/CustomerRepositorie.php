<?php
namespace App\Repositories\Customer;


use App\Models\Customer\CustomerModel;
use App\Repositories\BaseRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

class CustomerRepositorie extends BaseRepositorie
{

    /**
     * @param $customer_id
     *
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-18 00:27:57
     */
    public static function getCustomerInfo($customer_id){
        $where = [['customer_id','=',$customer_id]];
        return CustomerModel::getOne($where);
    }

    /**
     * @throws Exception
     */
    public static function getCustomerList( $where = [],$field = ['*'],$page_size = 10,$page = 1,$order = ['id','desc']){
        $limit = $page_size;
        $offset = ($page - 1) * $limit;
        $page = ['limit'=>$limit,'offset'=>$offset];
        return CustomerModel::getAll($where,$field,$page,$order);
    }


    /**
     * @throws Exception
     */
    public static function getCustomerCount($where){
        return CustomerModel::getCount($where);
    }


    /**
     * @throws Exception
     */
    public static function getAllCustomer($field = ['*']){
        // $where = [['status','=',Enu::CUSTOMER_STATUS_ENABLE]];
        $where = [];
        return CustomerModel::getAll($where,$field);
    }


    /**
     * 添加客户
     * @param $data
     *
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-15 19:14:25
     */
    public static function createCustomer($data){
        return CustomerModel::add($data);
    }


    /**
     * 更新客户信息
     *
     * @param $customer_id
     * @param $data
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-18 14:23:03
     */
    public static function editCustomer($customer_id,$data){
        $where = [['customer_id','=',$customer_id]];
        return CustomerModel::edit($where,$data);
    }


    /**
     * 获取没有关联主体的客户
     *
     * @param $field
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-18 19:36:49
     */
    public static function getCustomerWithoutGroup($field = ['*']){
        $where = [
            ['status','=',Enu::CUSTOMER_STATUS_ENABLE],
            ['group_id','=','']
        ];
        return CustomerModel::getAll($where,$field);
    }


    /**
     * 获取主体下的客户
     *
     * @param          $group_id
     * @param string[] $field
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-09-18 19:36:49
     */
    public static function getGroupCustomers($group_id,$field = ['*']){
        $where = [
            ['status','=',Enu::CUSTOMER_STATUS_ENABLE],
            ['group_id','=',$group_id]
        ];
        return CustomerModel::getAll($where,$field);
    }


    /**
     * 获取主体的客户
     *
     * @param          $group_id_arr
     * @param string[] $field
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-09-18 19:36:49
     */
    public static function getCustomerWithGroup($group_id_arr, $field = ['*']){
        $where = [
            ['status','=',Enu::CUSTOMER_STATUS_ENABLE],
            ['group_id','in',$group_id_arr]
        ];
        return CustomerModel::getAll($where,$field);
    }


    /**
     *
     *
     * @param $customer_ids
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-10-10 16:47:12
     */
    public static function getListByCustomerIds($customer_ids){
        $where = [['customer_id','in',$customer_ids]];
        return CustomerModel::getAll($where);
    }

    public static function getCustomersWithGroupIdsWithoutStatus($group_id_arr){
        $where = [
            ['group_id','in',$group_id_arr]
        ];
        return CustomerModel::getAll($where);
    }
}