<?php
namespace App\Repositories\Customer;

use App\Models\Customer\CompanyTypeModel;
use App\Repositories\BaseRepositorie;
use Exception;

class CompanyTypeRepositorie extends BaseRepositorie
{

    /**
     * 获取公司类型
     * 返回Element plus级联选择器结构
     *
     * @throws Exception
     */
    public static function getCompanyType(){
        $_company_type_list = CompanyTypeModel::getAll([],['id', 'parent_id', 'name']);

        $company_type_list = [];
        foreach($_company_type_list as $info){
            if($info['parent_id'] != 0){ continue; }
            $company_id = intval($info['id']);
            $company_type_list[$company_id] = [
                'value' => $company_id,
                'label' => $info['name'],
            ];
        }

        foreach($_company_type_list as $info){
            if($info['parent_id'] == 0){ continue; }
            $company_id = intval($info['id']);
            $parent_id = intval($info['parent_id']);
            $company_type_list[$parent_id]['children'][] = [
                'value' => $company_id,
                'label' => $info['name'],
            ];
        }

        $res = [];
        foreach ($company_type_list as $key => $item) {
            $res[] = $item;
        }

        return $res;
    }


    /**
     * 获取公司类型名称 父类型名称-子类型名称
     * @throws Exception
     */
    public static function getCompanyTypeName(){
        $_company_type_name_list = CompanyTypeModel::getAll([],['id', 'parent_id', 'name']);
        $_company_type_name_list = array_column($_company_type_name_list, null,'id');
        $company_type_name_list = [];
        foreach($_company_type_name_list as $ct_id => $item){
            if($item['parent_id'] == 0){
                continue;
            }
            $company_type_name_list[$ct_id] = [
                'name' => $_company_type_name_list[$item['parent_id']]['name'].'-'.$item['name'],
                'ids'  => [intval($item['parent_id']),intval($item['id'])],
            ];
        }
        return $company_type_name_list;
    }
}