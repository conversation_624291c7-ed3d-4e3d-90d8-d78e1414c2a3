<?php
namespace App\Repositories\Customer;

use App\Models\Customer\AccountModel;
use App\Repositories\BaseRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

class AccountRepositorie extends BaseRepositorie
{


    /**
     * 根据客户id获取账号列表
     *
     * @param        $customer_ids
     * @param string $account_id
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-09-27 20:18:20
     */
    public static function getListWithCustomers($customer_ids,$account_id = ''){
        $where = [
            ['customer_id','in',$customer_ids],
            ['father_id','!=','0'],
        ];
        if(!empty($account_id)){
            $where[] = ['account_id','=',$account_id];
        }
        return AccountModel::getAll($where);
    }


    /**
     * 根据账号id获取账号列表
     *
     * @param $account_id
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-27 20:18:11
     */
    public static function getListByAccountId($account_id){
        $where = [
            ['account_id','=',$account_id],
        ];
        return AccountModel::getAll($where);
    }


    /**
     * 根据多个账号id获取数据
     *
     * @param $account_ids
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-28 02:44:52
     */
    public static function getListByAccountIds($account_ids){
        $where = [
            ['account_id','in',$account_ids],
        ];
        return AccountModel::getAll($where);
    }


    /**
     * 所有账号
     *
     * @param $field
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-28 04:40:38
     */
    public static function getAllAccount($field = ['*']){
        // $where = [['status','=',Enu::ACCOUNT_STATUS_ENABLE]];
        $where = [];
        $where[] = ['apikey', '!=', ''];
        return AccountModel::getAll($where,$field);
    }


    /**
     *
     *
     * @param $customer_id
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-28 16:42:20
     */
    public static function getAccountInfo($customer_id){
        $where = [['customer_id','=',$customer_id]];
        return AccountModel::getOne($where);
    }
}