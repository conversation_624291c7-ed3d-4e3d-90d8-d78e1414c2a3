<?php
namespace App\Repositories\Customer;

use App\Models\Customer\CustomerSalesmanHistoryModel;
use App\Repositories\BaseRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

class CustomerSalesmanHistoryRepositorie extends BaseRepositorie
{


    /**
     * @param $customer_id
     * @param $salesman
     * @param $start_day string Ymd
     *
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-15 16:56:04
     */
    public static function add($customer_id,$salesman,$start_day){
        $now_str = date(Enu::TIME_FORMAT);
        $data = [
            'customer_id' => $customer_id,
            'salesman'    => $salesman,
            'start_day'   => $start_day,
            'created_at'  => $now_str,
            'updated_at'  => $now_str,
        ];
        return CustomerSalesmanHistoryModel::add($data);
    }


    /**
     * 获取客户所有商务
     *
     * @param $customer_id
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-18 11:48:18
     */
    public static function getCustomerSalesman($customer_id){
        $where = [['customer_id','=',$customer_id]];
        return CustomerSalesmanHistoryModel::getAll($where);
    }


    /**
     * 更新历史商务
     *
     * @param $where
     * @param $data
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-18 14:47:51
     */
    public static function edit($where,$data){
        return CustomerSalesmanHistoryModel::edit($where,$data);
    }
}