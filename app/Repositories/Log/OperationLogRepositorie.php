<?php
namespace App\Repositories\Log;


use App\Models\Log\OperationLogModel;
use App\Repositories\BaseRepositorie;
use App\Utils\Helpers\Enu;
use DateTimeImmutable;
use DateTimeZone;
use Exception;

class OperationLogRepositorie extends BaseRepositorie
{

    private $current_request_operation_log = 0;
    private static $instance;//单例

    private function __construct() {
    }

    /**
     * 单例
     *
     * @return self
     * <AUTHOR> 2023-10-10 11:56:29
     *
     * @static
     */
    public static function getInstance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 检查是否存在
     *
     * @return bool
     * <AUTHOR> 2023-10-10 11:56:17
     *
     * @static
     */
    public static function hasInstance(){

        return null !== self::$instance;
    }

    /**
     * 添加操作记录
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-10-09 18:21:22
     *
     * @static
     */
    public function add($data){
        $data['created_at'] = date(Enu::TIME_FORMAT);
        $this->current_request_operation_log = OperationLogModel::add($data,true);
    }

    /**
     * 记录操作结果
     *
     * @param $res_code
     * @param $res_data
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-10-10 11:24:54
     */
    public function update($res_code,$res_data){
        $where = [
            ['id', '=', $this->current_request_operation_log],
        ];
        $data = [
            'code'       => $res_code,
            'data'       => $res_data,
            'updated_at' => date(Enu::TIME_FORMAT),
        ];
        OperationLogModel::edit($where, $data);
    }
}