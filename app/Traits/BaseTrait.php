<?php

namespace App\Traits;

use App\Repositories\Log\OperationLogRepositorie;
use App\Utils\Helpers\Enu;
use Yulore\Components\Request;

trait BaseTrait
{
    /**
     * 产品前缀，用于区分不同项目；
     */
    // protected $prefix;
    // protected $cache;
    // protected $pids;

    /** @var Request $request */
    protected $request;

    public function init()
    {
        $this->request = app('request');
        // $this->prefix  = config('app_prefix');
        // $this->pids    = config("app_pids");
        // $this->cache = app('redis')->connection();
    }

    /** admin使用 操作成功 */
    protected function success($msg = '', $data = [])
    {
        header('Content-Type: application/json; charset=utf-8');
        $return = ['status' => Enu::RESP_OK, 'msg' => $msg ? $msg : 'ok'];
        $data && $return['data'] = $data;
        exit(json_encode($return));

    }

    /** admin使用 操作失败 */
    protected function error($msg = '', $data = [])
    {
        header('Content-Type: application/json; charset=utf-8');
        $return = ['status' => Enu::RESP_FAILED, 'msg' => $msg ? $msg : 'no'];
        $data && $return['data'] = $data;
        exit(json_encode($return));
    }

    /**
     * @param int   $status
     * @param array $data
     * return json
     *
     * @throws \Exception
     * <AUTHOR>
     * @desc   所有接口结构统一返回
     * @date   2018-07-09
     *
     */
    protected function response($status, $data = []) {
        // header('Content-Type: application/json; charset=utf-8');
        // header("Content-type: application/json; charset=utf-8");
        // header("Access-Control-Allow-Origin:*");
        // header("Access-Control-Allow-Methods: POST, GET, OPTIONS, PUT, DELETE");
        // header('Access-Control-Allow-Headers:Origin,Content-Type,Accept,token,x-requested-with,x-request-id,device,authorization');
        // header('Access-Control-Allow-Headers:x-requested-with,content-type');

        header("Content-type: application/json; charset=utf-8");
        header("Access-Control-Allow-Origin:*");
        header("Access-Control-Allow-Methods:OPTIONS, POST, GET");
        header("Access-Control-Allow-Headers:x-requested-with,content-type,authorization");

        $conf = config('response.status');
        $msg = isset($conf[$status]) ? $conf[$status] : $conf[Enu::RESP_SERVICE_UNAVAILABLE];
        $return = ['status' => $status, 'msg' => $msg];
        $return['data'] = $data;

        //操作记录,记录返回结果
        if(OperationLogRepositorie::hasInstance()){
            $ol_obj = OperationLogRepositorie::getInstance();
            $ol_obj->update($status,json_encode($data,JSON_UNESCAPED_UNICODE));
        }
        exit(json_encode($return,JSON_UNESCAPED_UNICODE));
    }
}
