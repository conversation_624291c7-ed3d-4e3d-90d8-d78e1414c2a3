<?php

namespace App\Controllers;

use App\Logics\System\RoleLogic;
use App\Models\System\SystemRoleModel;
use App\Models\System\SystemUserModel;
use App\Repositories\Log\OperationLogRepositorie;
use App\Traits\BaseTrait;
use App\Utils\Helpers\Enu;
use Exception;

class BaseController
{
    use BaseTrait;

    protected $admin = '';
    // protected $roles = [5,90,93,95];
    protected $roles = [];

    /** @var array 登录用户拥有权限的接口列表 */
    private $permission_routes;

    /**
     * @throws Exception
     */
    public function __construct($params = '')
    {
        $this->init();
        $this->check_token($params);
    }


    /**
     * 校验token 权限
     *
     * @throws Exception
     */
    private function check_token($params) {

        $headers = $this->parse_headers();

        $token = key_exists('Authorization', $headers)?substr($headers['Authorization'],7):'';

        //不需要token可以访问的接口
        $method = $params['controller'].'@'.$params['method'];

        $method_white_list = config('without_token_methods');
        if(key_exists($method, $method_white_list)){
            return;
        }

        if(empty($token)){
            $this->response(Enu::RESP_USER_MISSING_TOKEN);
        }

        $where     = [['token', '=', $token]];
        $user_info = SystemUserModel::getOne($where);

        //查询不到用户
        if (false === $user_info) {
            $this->response(Enu::RESP_USER_NOT_FOND);
        }

        //用户被禁用
        if ($user_info['disabled'] == Enu::USER_STATUS_DISENABLE) {
            $this->response(Enu::RESP_USER_STATUS_ERR);
        }

        //用户token过期
        $now_str = date(Enu::TIME_FORMAT);
        if ($user_info['token_expire_at'] < $now_str) {
            $this->response(Enu::RESP_USER_TOKEN_EXPIRED);
        }


        //TODO 判断请求权限
        $method = $params['method'];
        $controller = $params['controller'];

        $this->admin = $user_info['username'];
        $this->roles = RoleLogic::get_user_role($user_info['username']);

        if($this->is_super_user()){
            $roles = SystemRoleModel::getAllRoles();
            $this->roles = array_column($roles, 'id');
        }

        //暂时适用于合同管理
        if($controller == '\App\Controllers\Contract\ContractController'){
            $this->permission_routes = RoleLogic::get_role_permission_routes($this->roles);
            if(!key_exists($params['route_url'], $this->permission_routes)){
                $this->response(Enu::RESP_USER_HAVE_NO_AUTHORIZATION);
            }
        }

        if($_SERVER['REQUEST_METHOD'] == 'POST') {
            $request_params = [];
            $params_keys    = $this->request->keys();
            foreach ($params_keys as $key) {
                if ($key == 's') {
                    continue;
                }
                $request_params[$key] = $this->request->get($key, '', false);
            }

            $ol_obj = OperationLogRepositorie::getInstance();
            $ol_data = [
                'uri'     => $_SERVER['REQUEST_URI'],
                'headers' => json_encode($headers, JSON_UNESCAPED_UNICODE),
                'params'  => json_encode($request_params, JSON_UNESCAPED_UNICODE),
                'admin'   => $this->admin,
                'ip'      => get_client_ip(),
            ];
            $ol_obj->add($ol_data);
        }
    }


    /**
     * 获取请求Header数据
     *
     * @return array
     * <AUTHOR> 2023-09-15 16:44:26
     */
    protected function parse_headers() {
        $headers = [];
        foreach ($_SERVER as $name => $value) {
            if (substr($name, 0, 5) == 'HTTP_') {
                $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
            }
        }
        return $headers;
    }

    /**
     * 判断是否是超级管理员
     *
     * @return bool
     * <AUTHOR> 2023-11-06 18:53:23
     *
     */
    protected function is_super_user() {
        return in_array(Enu::USER_ROLE_TYPE_SUPER,$this->roles);
    }
}