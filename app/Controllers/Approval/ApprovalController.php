<?php

namespace App\Controllers\Approval;

use App\Controllers\BaseController;
use App\Logics\Approval\ApprovalLogic;
use App\Logics\Config\ConfigLogic;
use App\Models\Approval\ApprovalModel;
use App\Models\Customer\AccountModel;
use App\Models\Customer\CustomerModel;
use App\Models\Product\ProductConfigItemModel;
use App\Models\Product\SubProductConfigItemModel;
use App\Repositories\Customer\AccountRepositorie;
use App\Repositories\Customer\CustomerGroupRepositorie;
use App\Repositories\Customer\CustomerRepositorie;
use App\Repositories\OptionsRepositorie;
use App\Repositories\Product\ProductConfigItemRepositorie;
use App\Repositories\Product\ProductRepositorie;
use App\Repositories\Product\SubProductRepositorie;
use App\Repositories\System\UserRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

class ApprovalController extends BaseController
{


    /**
     * 审批通过
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-10-09 20:26:23
     *
     */
    public function pass(){
        $approval_id = $this->request->get('approval_id');  //审批id

        $approval_info = ApprovalLogic::info($approval_id);

        if($approval_info['status'] != Enu::APPROVAL_STATUS_NEED_APPROVAL){
            $this->response(Enu::RESP_APPROVAL_STATUS_WORNG);
        }

        try {
            ApprovalLogic::pass($approval_id, $this->admin);
        } catch (Exception $e) {
            $this->response($e->getCode(),$e->getMessage());
        }

        $this->response(Enu::RESP_OK);
    }


    /**
     * 审批撤销
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-10-09 20:36:28
     *
     */
    public function cancel(){
        $approval_id = $this->request->get('approval_id');  //审批id
        $approval_info = ApprovalLogic::info($approval_id);

        if($approval_info['status'] != Enu::APPROVAL_STATUS_NEED_APPROVAL){
            $this->response(Enu::RESP_APPROVAL_STATUS_WORNG);
        }
        ApprovalLogic::cancel($approval_id);
        $this->response(Enu::RESP_OK);
    }


    /**
     * 审批驳回
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-10-09 20:36:23
     *
     */
    public function reject(){
        $approval_id = $this->request->get('approval_id');  //审批id
        $comments = $this->request->get('comments');  //驳回原因
        $approval_info = ApprovalLogic::info($approval_id);

        if($approval_info['status'] != Enu::APPROVAL_STATUS_NEED_APPROVAL){
            $this->response(Enu::RESP_APPROVAL_STATUS_WORNG);
        }

        ApprovalLogic::reject($approval_id,$comments,$this->admin);
        $this->response(Enu::RESP_OK);
    }


    /**
     * 审批列表
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-10-10 13:47:09
     */
    public function approvalList(){
        $approval_id = $this->request->get('approval_id');                //审批id
        $type        = $this->request->get('type');                       //模块类型
        $action      = $this->request->get('action');                     //操作类型

        $product_id  = intval($this->request->get('product_id'));      //产品id
        $sub_product_id  = intval($this->request->get('sub_product_id'));      //产品id
        $product_info = ProductRepositorie::info($product_id);

        $group_id       = $this->request->get('group_id');                   //主体id
        $customer_id    = $this->request->get('customer_id');                //客户id
        $account_id     = $this->request->get('account_id');                 //账号id
        $config_item_id = $this->request->get('item_id');                    //配置项id

        $created_at = $this->request->get('created_at','',false);
        if(!empty($created_at) && !is_array($created_at)){
            $this->response(Enu::RESP_FAILED,'时间范围错误');
        }

        $page      = intval($this->request->get('page',1));
        $page_size = intval($this->request->get('page_size',20));

        $is_apply = $this->request->get('is_apply'); // 页面类型 true:我的审批 false:待我审批
        $is_apply = $is_apply == 'true';
        $status   = $this->request->get('status');   //状态


        $account_id_arr = [];
        $customer_id_arr = [];
        $group_id_arr = [];
        $where = [];
        if(!empty($approval_id)){
            $where = [
                ['approval_id', '=', $approval_id],
            ];
        }else{
            if(!empty($group_id)){
                $where[] = ['group_id', '=', $group_id];
            }
            if(!empty($customer_id)){
                $where[] = ['customer_id', '=', $customer_id];
            }
            if(!empty($account_id)){
                $where[] = ['account_id', '=', $account_id];
            }
            if(!empty($type)){
                $where[] = ['type', '=', $type];
            }
            if(!empty($action)){
                $where[] = ['action', '=', $action];
            }
            if(!empty($product_id)){
                $where[] = ['product_id', '=', $product_id];
            }
            if(!empty($sub_product_id)){
                $where[] = ['sub_product_id', '=', $sub_product_id];
            }
            if(!empty($status)){
                $where[] = ['status', '=', $status];
            }
        }

        if($is_apply){//我的审批
            $where[] = ['applicant', '=', $this->admin];
        }else{//待我审批
            if(!$this->is_super_user()) {//如果是超级管理员,展示所有的申请数据
                //1 获取当前用户角色
                $where[] = ['approver_role', 'in', $this->roles];
            }
        }

        if(!empty($created_at)){
            $where[] = ['created_at', '>=', date(Enu::TIME_FORMAT,$created_at[0])];
            $where[] = ['created_at', '<=', date(Enu::TIME_FORMAT,$created_at[1])];
        }

        $list  = ApprovalLogic::getApprovalList($where, ['*'],$page_size,$page,['status','asc, id desc']);//多个字段排序,限制一个字段排序,将多个字段拼在第二个元素中
        $count = ApprovalLogic::getApprovalCount($where);

        $admin_arr = [];
        $product_id_arr = [];
        $sub_product_id_arr = [];
        $item_ids = [];
        $sub_item_ids = [];
        foreach($list as $info){
            if(!empty($info['account_id'])) {
                $account_id_arr[$info['account_id']] = $info['account_id'];
            }
            if(!empty($info['customer_id'])) {
                $customer_id_arr[$info['customer_id']] = $info['customer_id'];
            }
            if(!empty($info['group_id'])) {
                $group_id_arr[$info['group_id']] = $info['group_id'];
            }
            if(!empty($info['product_id'])) {
                $product_id_arr[$info['product_id']] = $info['product_id'];
            }
            if(!empty($info['sub_product_id'])) {
                $sub_product_id_arr[$info['sub_product_id']] = $info['sub_product_id'];
            }
            if(!empty($info['approver'])) {
                $admin_arr[$info['approver']] = $info['approver'];
            }
            $admin_arr[$info['applicant']] = $info['applicant'];

            //审批中 用户配置 主产品或子产品只有一个存在
            if($info['sub_product_id'] != 0 && $info['config_item_source'] == Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_SUB && $info['config_item_id'] != 0){//子产品配置项
                $sub_item_ids[] = $info['config_item_id'];
            }

            if($info['sub_product_id'] != 0 && $info['config_item_source'] == Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT && $info['config_item_id'] != 0){//主产品配置项
                $item_ids[] = $info['config_item_id'];
            }

            if($info['product_id'] != 0 && $info['config_item_id'] != 0){//主产品配置项
                $item_ids[] = $info['config_item_id'];
            }
        }
        $sub_item_ids = array_unique($sub_item_ids);
        $item_ids = array_unique($item_ids);

        $account_list = $customer_list = [];
        $_account_list = AccountRepositorie::getListByAccountIds($account_id_arr);
        foreach($_account_list as $account_info){
            $account_list[$account_info['account_id']] = $account_info;
            $customer_id_arr[] = $account_info['customer_id'];
        }

        $_customer_list = CustomerRepositorie::getListByCustomerIds($customer_id_arr);
        foreach($_customer_list as $customer_info){
            $customer_list[$customer_info['customer_id']] = $customer_info;
            $group_id_arr[] = $customer_info['group_id'];
        }

        $group_list = CustomerGroupRepositorie::getCustomerGroupListByGrouupIds($group_id_arr);
        $group_list = array_column($group_list, null, 'group_id');

        $sub_product_list = SubProductRepositorie::getSubProductListByProductIds($sub_product_id_arr);
        $product_id_arr = array_merge($product_id_arr,array_column($sub_product_list, 'father_id'));
        $sub_product_list = array_column($sub_product_list, null, 'product_id');

        $all_product_ids = array_merge($product_id_arr,array_column($sub_product_list, 'product_id'));

        $product_list = ProductRepositorie::getProductListByProductIds($all_product_ids);
        $product_list = array_column($product_list, null, 'product_id');

        $product_config_item_list = ProductConfigItemModel::getAll([['id','in',$item_ids]]);
        $product_config_item_list = array_column($product_config_item_list, null,'id');
        $sub_product_config_item_list = SubProductConfigItemModel::getAll([['id','in',$sub_item_ids]]);
        $sub_product_config_item_list = array_column($sub_product_config_item_list, null,'id');

        $user_list = UserRepositorie::getUserList($admin_arr);
        $user_list = array_column($user_list, null, 'username');

        $approval_options = config('options.approval');
        $item_options     = config('options.product_config');
        $sub_item_options = config('options.sub_product_config');

        foreach($list as &$item){
            $item['account_name']     = '';
            $item['customer_name']    = '';
            $item['group_name']       = '';
            $item['product_name']     = '';
            $item['product_name']     = '';
            $item['sub_product_name'] = '';
            $item['approver_name']    = '';
            $item['applicant_name']   = '';
            $item['type_name']        = $approval_options['type'][$item['type']];
            $item['action_name']      = $approval_options['action'][$item['action']];
            $item['status_name']      = $approval_options['status'][$item['status']];
            $item['approved_at']      = substr($item['approved_at'], 0, - 3);
            $item['created_at']       = substr($item['created_at'], 0, - 3);
            $item['updated_at']       = substr($item['updated_at'], 0, - 3);
            if(!empty($item['account_id'])) {
                $item['account_name'] = $account_list[$item['account_id']]['account_name'];
                $item['customer_id'] = $account_list[$item['account_id']]['customer_id'];
            }
            if(!empty($item['customer_id'])) {
                $item['customer_name'] = $customer_list[$item['customer_id']]['name'];
                $item['group_id'] = $customer_list[$item['customer_id']]['group_id'];
            }
            if(!empty($item['group_id'])) {
                $item['group_name'] = $group_list[$item['group_id']]['group_name'];
            }
            if(!empty($item['product_id'])) {
                $item['product_name'] = $product_list[$item['product_id']]['product_name'];
            }
            if(!empty($item['sub_product_id'])) {
                $item['sub_product_name'] = $sub_product_list[$item['sub_product_id']]['product_name'];
            }
            if(!empty($item['approver'])) {
                $item['approver_name'] = $user_list[$item['approver']]['realname'];
            }
            $item['applicant_name'] = $user_list[$item['applicant']]['realname'];

            // TODO 展示配置项名称
            if($item['sub_product_id'] != 0 && $item['config_item_source'] == Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_SUB){//子产品配置项
                $item['item_name'] = !empty($item['config_item_id'])?$sub_product_config_item_list[$item['config_item_id']]['item_name']:'';
                $item['item_key'] = !empty($item['config_item_id'])?$sub_product_config_item_list[$item['config_item_id']]['item_key']:'';
                $item['item_type'] = !empty($item['config_item_id'])?$sub_product_config_item_list[$item['config_item_id']]['item_type']:'';
                $item_options_map = !empty($item['config_item_id'])?$sub_product_config_item_list[$item['config_item_id']]['item_options']:'';
            }else{//主产品配置项
                $item['item_name'] = !empty($item['config_item_id'])?$product_config_item_list[$item['config_item_id']]['item_name']:'';
                $item['item_key'] = !empty($item['config_item_id'])?$product_config_item_list[$item['config_item_id']]['item_key']:'';
                $item['item_type'] = !empty($item['config_item_id'])?$product_config_item_list[$item['config_item_id']]['item_type']:'';
                $item_options_map = !empty($item['config_item_id'])?$product_config_item_list[$item['config_item_id']]['item_options']:'';
            }

            $item['is_add_item'] = false;
            $item['is_edit_item'] = false;
            if($item['type'] == Enu::APPROVAL_TYPE_PRODUCT_CONFIG_ITEM){
                $item['is_add_item'] = true;
                $item['new_value'] = json_decode($item['new_value'], true);
                $item['new_value']['role'] = $item_options['role'][$item['new_value']['role']];
                $item['new_value']['modify'] = $item_options['modify'][$item['new_value']['modify']];
                $item['new_value']['config_scope'] = $item_options['config_scope'][$item['new_value']['config_scope']];
                $item['new_value']['item_default'] = ConfigLogic::get_value($item['new_value']['item_type'], $item['new_value']['item_options'], $item['new_value']['item_default']);

                if(ConfigLogic::is_checkbox_or_radio($item['new_value']['item_type'])) {
                    $new_value_item_options = json_decode($item['new_value']['item_options'], true);
                    $item['new_value']['item_options'] = implode(',',array_column($new_value_item_options, 'label'));
                }

                $item['new_value']['item_type'] = $item_options['item_type'][$item['new_value']['item_type']];

                if(!empty($item['product_id'])) {
                    $item['new_value']['product_id'] = $product_list[$item['new_value']['product_id']]['product_name'];
                    $item['new_value']['extend'] = isset($item['new_value']['extend'])?$item_options['extend'][$item['new_value']['extend']]:'';
                }else{
                    $item['new_value']['product_id'] = $sub_product_list[$item['new_value']['product_id']]['product_name'];
                }

            }else if(in_array($item['type'],  [Enu::APPROVAL_TYPE_PRODUCT_CONFIG, Enu::APPROVAL_TYPE_CUSTOMER_PRODUCT_CONFIG]) &&
                $item['action'] == Enu::APPROVAL_ACTION_EDIT &&
                $item['item_type'] == Enu::PRODUCT_CONFIG_ITEM_TYPE_CHECKBOX)
            {
                $item['is_edit_item'] = true;
                //如果配置项是多选类型的话 更新前后差异进行比较 方便前端区分显示
                $item['diff_item'] = ApprovalLogic::compareOptions($item['value'], $item['new_value'], $item_options_map);
            }

            $item['new_value'] = ConfigLogic::get_value($item['item_type'], $item_options_map, $item['new_value']);
            $item['value']     = ConfigLogic::get_value($item['item_type'], $item_options_map, $item['value']);
        }

        $res = [
            'list'                  => $list,
            'count'                 => $count,
            'show_need_my_approval' => count(array_intersect($this->roles, Enu::APPROVAL_ROLES)) > 0,
            'page'                  => $page,
            'page_size'             => $page_size,
        ];

        $this->response(Enu::RESP_OK, $res);
    }


    /**
     *
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-10-10 16:07:08
     *
     */
    public function options(){
        $product_id     = $this->request->get('product_id');
        $res = [
            'group'    => OptionsRepositorie::group_options(),
            'customer' => OptionsRepositorie::customer_options(),
            'account'  => OptionsRepositorie::account_options(),
            'product'  => OptionsRepositorie::product_options($product_id),
        ];

        $approval_options = config('options.approval');
        foreach($approval_options as $field => $options){
            foreach($options as $value => $label){
                $res[$field][] = [
                    'value' => $value,
                    'label' => $label
                ];
            }
        }

        $this->response(Enu::RESP_OK, $res);
    }
}