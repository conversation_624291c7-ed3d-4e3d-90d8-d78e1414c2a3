<?php

namespace App\Controllers\Product;

use App\Controllers\BaseController;
use App\Logics\Approval\ApprovalLogic;
use App\Logics\Config\ConfigLogic;
use App\Logics\Config\ProductConfigItemLogic;
use App\Logics\Config\SubProductConfigItemLogic;
use App\Logics\System\RoleLogic;
use App\Models\Product\ProductModel;
use App\Repositories\Product\ProductConfigItemRepositorie;
use App\Repositories\Product\ProductRepositorie;
use App\Repositories\Product\SubProductConfigItemRepositorie;
use App\Repositories\Product\SubProductConfigRepositorie;
use App\Repositories\Product\SubProductRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

/**
 * 子产品
 */
class SubProductController extends BaseController
{
    /**
     * 子产品列表
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-27 14:04:09
     */
    public function subProductList(){
        $page       = intval($this->request->get('page', 1));
        $page_size  = intval($this->request->get('page_size', 20));
        $father_id  = intval($this->request->get('father_id'));
        $product_id = intval($this->request->get('product_id'));

        if(empty($father_id)){
            $this->response(Enu::RESP_SUB_PRODUCT_MISSING_FATHER_ID);
        }

        //处理参数
        $where = [];
        $where[] = ['father_id','=',$father_id];
        $where[] = ['father_id','not in',[0,401]];
        if(!empty($product_id)) {
            $where[] = ['product_id', '=', $product_id];
        }

        $fields = ['product_id','product_name','product_key','admin','father_id'];
        $list = SubProductRepositorie::getSubProductList($where,$fields,$page_size,$page);
        $count = SubProductRepositorie::getSubProductCount($where);

        $father_product_info = ProductRepositorie::info($father_id,$fields);

        $res = [
            'list'                => $list,
            'count'               => $count,
            'father_product_info' => $father_product_info,
            'page'                => $page,
            'page_size'           => $page_size,
        ];

        $this->response(Enu::RESP_OK, $res);
    }


    /**
     * 全部子产品的下拉列表数据
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-25 17:28:11
     *
     */

    public function options(){
        $father_id = intval($this->request->get('father_id'));

        if(empty($father_id)){
            $this->response(Enu::RESP_SUB_PRODUCT_MISSING_FATHER_ID);
        }

        $_list = SubProductRepositorie::getAllSubProduct($father_id);

        $res = [];
        foreach($_list as $info){
            $res[] = [
                'value' => $info['product_id'],
                'label' => $info['product_name']
            ];
        }

        $this->response(Enu::RESP_OK, $res);
    }


    /**
     * 添加子产品
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-27 14:24:14
     *
     */
    public function add(){
        $product_id = intval($this->request->get('product_id'));
        $product_name = $this->request->get('product_name');
        $product_key = $this->request->get('product_key');
        $father_id = intval($this->request->get('father_id'));

        if(empty($father_id)){
            $this->response(Enu::RESP_SUB_PRODUCT_MISSING_FATHER_ID);
        }
        if(empty($product_name)){
            $this->response(Enu::RESP_SUB_PRODUCT_MISSING_PRODUCT_NAME);
        }
        if(empty($product_id)){
            $this->response(Enu::RESP_SUB_PRODUCT_MISSING_PRODUCT_ID);
        }
        if(empty($product_key)){
            $this->response(Enu::RESP_SUB_PRODUCT_MISSING_PRODUCT_KEY);
        }

        $product_count = ProductRepositorie::getProductCount([['product_id','=',$product_id]]);
        if($product_count > 0) {
            $this->response(Enu::RESP_PRODUCT_DUPLICATION_PRODUCT_ID);
        }

        $product_count = SubProductRepositorie::getSubProductCount([['father_id','=',$father_id],['product_id','=',$product_id]]);
        if($product_count > 0) {
            $this->response(Enu::RESP_SUB_PRODUCT_DUPLICATION_PRODUCT_ID);
        }

        $product_count = ProductRepositorie::getProductCount([['product_name','=',$product_name]]);
        if($product_count > 0) {
            $this->response(Enu::RESP_PRODUCT_DUPLICATION_PRODUCT_NAME);
        }

        $product_count = SubProductRepositorie::getSubProductCount([['product_name','=',$product_name]]);
        if($product_count > 0) {
            $this->response(Enu::RESP_SUB_PRODUCT_DUPLICATION_PRODUCT_NAME);
        }

        $id = SubProductRepositorie::createProduct($father_id,$product_id, $product_name, $product_key, $this->admin);
        if($id){
            $this->response(Enu::RESP_OK);
        }else{
            $this->response(Enu::RESP_FAILED);
        }
    }

    /**
     * 编辑子产品
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-27 14:43:59
     *
     */
    public function edit(){
        $product_id   = intval($this->request->get('product_id')); //产品id
        $product_name = $this->request->get('product_name');

        $product_info = SubProductRepositorie::info($product_id);

        if(empty($product_id)){
            $this->response(Enu::RESP_SUB_PRODUCT_MISSING_PRODUCT_ID);
        }
        if(empty($product_name)){
            $this->response(Enu::RESP_SUB_PRODUCT_MISSING_PRODUCT_NAME);
        }

        $product_count = ProductRepositorie::getProductCount([['product_id','=',$product_info['father_id']]]);
        if($product_count < 1){
            $this->response(Enu::RESP_SUB_PRODUCT_NONE_FATHER_PRODUCT);
        }

        $product_count = ProductRepositorie::getProductCount([['product_name','=',$product_name]]);
        if($product_count > 0) {
            $this->response(Enu::RESP_PRODUCT_DUPLICATION_PRODUCT_NAME);
        }

        $product_count = SubProductRepositorie::getSubProductCount([['product_name','=',$product_name]]);
        if($product_count > 0) {
            $this->response(Enu::RESP_SUB_PRODUCT_DUPLICATION_PRODUCT_NAME);
        }

        $rows = SubProductRepositorie::edit($product_id,$product_name,$this->admin);
        if($rows){
            $this->response(Enu::RESP_OK);
        }else{
            $this->response(Enu::RESP_FAILED);
        }
    }

    /**
     * 获取添加配置项相关值
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-25 18:54:40
     */
    public function itemOptions(){
        $_product_config_options = config('options.sub_product_config');
        $product_config_options = [];
        //整理为前端使用的结构
        foreach($_product_config_options as $field => $options){
            foreach($options as $value => $label){
                $product_config_options[$field][] = [
                    'value' => $value,
                    'label' => $label
                ];
            }
        }

        $this->response(Enu::RESP_OK, $product_config_options);
    }

    /**
     * 子产品信息和产品配置项的列表
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-27 16:45:30
     *
     */
    public function info() {
        $product_id = intval($this->request->get('product_id')); //产品id
        if(empty($product_id)){
            $this->response(Enu::RESP_PRODUCT_MISSING_PRODUCT_ID);
        }

        $sub_product_info = SubProductRepositorie::info($product_id);
        $father_id = $sub_product_info['father_id'];
        $product_info = ProductRepositorie::info($father_id);
        $sub_item_list = SubProductConfigItemRepositorie::getAllSubProductConfigItem($product_id);
        // $item_list = ProductConfigItemModel::getAllProductConfigItem($father_id);


        $sub_product_config_options = config('options.sub_product_config');

        $all_item_arr = [];
        foreach($sub_item_list as $info){
            $info['item_options'] = json_decode($info['item_options'],JSON_UNESCAPED_UNICODE);
            $info['role'] = $sub_product_config_options['role'][$info['role']];
            $info['modify'] = $sub_product_config_options['modify'][$info['modify']];
            $info['config_scope'] = $sub_product_config_options['config_scope'][$info['config_scope']];
            $info['extend'] = '';
            $info['item_type_text'] = $sub_product_config_options['item_type'][$info['item_type']];
            if(ConfigLogic::is_checkbox_or_radio($info['item_type'])){
                $_options = array_column($info['item_options'], 'label','value');
                if($info['item_type'] == Enu::PRODUCT_CONFIG_ITEM_TYPE_CHECKBOX){
                    $_defaults = explode(',',$info['item_default']);
                    $info['item_default'] = [];
                    foreach($_defaults as $d){
                        $info['item_default'][] = $_options[$d];
                    }
                    $info['item_default'] = implode(',',$info['item_default']);
                }else {
                    $info['item_default'] = $_options[$info['item_default']];
                }
            }
            $info['config_item_source'] = Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_SUB;
            $all_item_arr[] = $info;
        }

        // 主产品配置项
        // $product_config_options = config('options.product_config');
        // foreach($item_list as $info){
        //     if($info['extend'] == Enu::PRODUCT_CONFIG_ITEM_SUB_CAN_NOT_EXTEND){
        //         continue;
        //     }
        //     $info['item_options'] = json_decode($info['item_options'],JSON_UNESCAPED_UNICODE);
        //     $info['role'] = $product_config_options['role'][$info['role']];
        //     $info['modify'] = $product_config_options['modify'][$info['modify']];
        //     $info['extend'] = $product_config_options['extend'][$info['extend']];
        //     if(in_array($info['item_type'], $this->has_options_type)){
        //         $_options = array_column($info['item_options'], 'label','value');
        //         if($info['item_type'] == Enu::PRODUCT_CONFIG_ITEM_TYPE_CHECKBOX){
        //             $_defaults = explode(',',$info['item_default']);
        //             $info['item_default'] = [];
        //             foreach($_defaults as $d){
        //                 $info['item_default'][] = $_options[$d];
        //             }
        //             $info['item_default'] = implode(',',$info['item_default']);
        //         }else {
        //             $info['item_default'] = $_options[$info['item_default']];
        //         }
        //     }
        //     $info['config_item_source'] = Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT;
        //     $all_item_arr[] = $info;
        // }
        $res = [];
        $res['info'] = $sub_product_info;
        $res['item_list'] = $all_item_arr;

        $this->response(Enu::RESP_OK,$res);
    }


    /**
     * 添加产品配置项
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-25 17:05:02
     *
     */
    public function addConfigItem(){
        $product_id   = intval($this->request->get('product_id'));            //产品id
        $item_key     = $this->request->get('item_key');                      //配置项key
        $item_name    = $this->request->get('item_name');                     //配置项名称
        $role         = $this->request->get('role');                          //所属角色
        $modify       = $this->request->get('modify');                        //是否可被客户编辑
        $config_scope = $this->request->get('config_scope');                  //是否可在产品中进行配置 配置范围
        $item_type    = $this->request->get('item_type');                     //配置项类型
        $item_options = $this->request->get('item_options', '', false);       //配置项选项
        $item_default = $this->request->get('item_default', '', false);       //配置项默认值

        try {
            $res = SubProductConfigItemLogic::add_item($product_id,$item_key,$item_name,$role,$modify,$config_scope,$item_type,$item_options,$item_default,$this->admin);
            if($res <= 0){
                $this->response(Enu::RESP_FAILED);
            }
        }catch(Exception $e){
            $this->response($e->getCode());
        }

        $this->response(Enu::RESP_OK, '已提交成功,等待审核!');
    }



    /**
     * 编辑配置
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-27 14:53:49
     *
     */
    public function editConfig(){
        $product_id         = intval($this->request->get('product_id'));             //产品id
        $item_id            = $this->request->get('item_id');                        //配置项id
        $item_value         = $this->request->get('item_value', '', false);          //配置项值
        $config_item_source = $this->request->get('config_item_source');             //配置来源


        if(empty($product_id)){
            $this->response(Enu::RESP_PRODUCT_MISSING_PRODUCT_ID);
        }
        if(empty($item_id)){
            $this->response(Enu::RESP_PRODUCT_CONFIG_ITEM_MISSING_ID);
        }
        if('' == $item_value){
            $this->response(Enu::RESP_PRODUCT_CONFIG_MISSING_VALUE);
        }

        if($config_item_source == Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_SUB){
            $config_item_info = SubProductConfigItemRepositorie::getSubProductConfigItemById($item_id);
        }else{
            $config_item_info = ProductConfigItemRepositorie::getProductConfigItemById($item_id);
        }

        //TODO 校验权限

        //检查配置值是否合法
        if(ConfigLogic::is_checkbox_or_radio($config_item_info['item_type'])) {
            $config_item_info['item_options'] = json_decode($config_item_info['item_options'],JSON_UNESCAPED_UNICODE);
            $_options = array_column($config_item_info['item_options'], 'value');
            if($config_item_info['item_type'] == Enu::PRODUCT_CONFIG_ITEM_TYPE_CHECKBOX){
                if(count(array_intersect($_options,$item_value)) != count($item_value)){
                    $this->response(Enu::RESP_PRODUCT_CONFIG_UNEXPECTED_VALUE);
                }
                $item_value = implode(',',$item_value);
            }else{
                if(!in_array($item_value,$_options)){
                    $this->response(Enu::RESP_PRODUCT_CONFIG_UNEXPECTED_VALUE);
                }
            }
        }


        $father_id = ProductModel::getFatherId($product_id);
        $config_info = SubProductConfigRepositorie::getSubProductConfigByProductIdAndItemId($product_id,$item_id,$config_item_source);

        $approval_type = Enu::APPROVAL_ACTION_ADD;//配置添加
        if(!empty($config_info)){
            $approval_type = Enu::APPROVAL_ACTION_EDIT;//配置编辑

            if($config_info['item_value'] === $item_value){//配置值相同 没有修改
                $this->response(Enu::RESP_PRODUCT_CONFIG_HAVE_NO_MODIFY);
            }
        }

        $data = [
            'approval_id'        => create_uuid('CA'),
            'type'               => Enu::APPROVAL_TYPE_PRODUCT_CONFIG,//产品配置
            'action'             => $approval_type,
            'group_id'           => '',
            'customer_id'        => '',
            'account_id'         => '',
            'product_id'         => $father_id,
            'sub_product_id'     => $product_id,
            'config_item_id'     => $item_id,
            'config_item_source' => $config_item_source,
            'value'              => $config_info['item_value'],
            'new_value'          => $item_value,
            'status'             => Enu::APPROVAL_STATUS_NEED_APPROVAL,//待审批
            'applicant'          => $this->admin,
        ];

        try {
            $res = ApprovalLogic::add($data);
        }catch(Exception $e){
            $this->response($e->getCode());
        }

        if($res > 0){
            $this->response(Enu::RESP_OK, '已提交成功,等待审核!');
        }else{
            $this->response(Enu::RESP_FAILED);
        }
    }




    /**
     * 删除配置项
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-25 17:05:28
     */
    public function delConfig(){
        $sub_product_id     = intval($this->request->get('sub_product_id'));                            //产品id
        $item_id            = intval($this->request->get('item_id'));                                   //配置项id
        $config_item_source = intval($this->request->get('config_item_source'));                        //配置来源


        if(empty($sub_product_id)){
            $this->response(Enu::RESP_PRODUCT_MISSING_PRODUCT_ID);
        }
        if(empty($item_id)){
            $this->response(Enu::RESP_PRODUCT_CONFIG_ITEM_MISSING_ID);
        }

        //TODO 校验角色
        if($config_item_source == Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT){
            // $config_item_info = SubProductConfigItemRepositorie::getSubProductConfigItemById($item_id);
            $config_item_info = ProductConfigItemRepositorie::getProductConfigItemById($item_id);
            if(empty($config_item_info)){
                $this->response(Enu::RESP_SUB_PRODUCT_CONFIG_WORNG_ITEM_ID);
            }
        }else{
            $this->response(Enu::RESP_SUB_PRODUCT_CONFIG_WORNG_SOURCE);
        }

        $father_id = ProductModel::getFatherId($sub_product_id);
        $config_info = SubProductConfigRepositorie::getSubProductConfigByProductIdAndItemId($sub_product_id,$item_id,$config_item_source);

        $data = [
            'approval_id'        => create_uuid('CA'),
            'type'               => Enu::APPROVAL_TYPE_PRODUCT_CONFIG,//产品配置
            'action'             => Enu::APPROVAL_ACTION_DELETE,
            'group_id'           => '',
            'customer_id'        => '',
            'account_id'         => '',
            'product_id'         => $father_id,
            'sub_product_id'     => $sub_product_id,
            'config_item_id'     => $item_id,
            'config_item_source' => Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT,
            'value'              => $config_info['item_value'],
            'new_value'          => '',
            'status'             => Enu::APPROVAL_STATUS_NEED_APPROVAL,//待审批
            'applicant'          => $this->admin,
        ];

        try {
            $res = ApprovalLogic::add($data);
        }catch(Exception $e){
            $this->response($e->getCode());
        }

        if($res > 0){
            $this->response(Enu::RESP_OK, '已提交成功,等待审核!');
        }else{
            $this->response(Enu::RESP_FAILED);
        }
    }




    /**
     * 子产品配置页面所需要的信息
     * 1. 仅展示子产品现有的配置,不需要展示所有的配置项
     * 2. 区分配置项来源是子产品还是主产品
     * 3. 配置值覆盖配置项值
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-27 18:04:12
     *
     */
    public function configInfo(){
        $product_id = intval($this->request->get('product_id')); //产品id
        if(empty($product_id)){
            $this->response(Enu::RESP_SUB_PRODUCT_MISSING_PRODUCT_ID);
        }

        //获取产品详情
        $sub_product_info = SubProductRepositorie::info($product_id);
        $product_info = ProductRepositorie::info($sub_product_info['father_id']);
        $sub_product_info['father_name'] = $product_info['product_name'];

        //获取配置项
        $_sub_item_list = [];//子产品配置项
        $_item_list = [];//主产品配置项

        $sub_item_list = SubProductConfigItemRepositorie::getAllSubProductConfigItem($product_id);
        foreach($sub_item_list as &$sub_info){
            $sub_info['item_options'] = json_decode($sub_info['item_options'],JSON_UNESCAPED_UNICODE);
            $sub_info['item_value'] = $sub_info['item_default'];
            $sub_info['config_item_source'] = Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_SUB;
            $sub_info['is_config'] = false;
            $_sub_item_list[Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_SUB.'_'.$sub_info['id']] = $sub_info;
        }

        //主产品配置项
        $item_list = ProductConfigItemRepositorie::getAllProductConfigItem($sub_product_info['father_id']);
        foreach($item_list as &$info){
            $info['item_options'] = json_decode($info['item_options'],JSON_UNESCAPED_UNICODE);
            $info['item_value'] = $info['item_default'];
            $info['config_item_source'] = Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT;
            $info['is_config'] = false;
            $_item_list[Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT.'_'.$info['id']] = $info;
        }


        //子产品配置
        $item_list = [];
        $sub_config_list = SubProductConfigRepositorie::getSubProductConfigList($product_id);
        foreach ($sub_config_list as $scl_info){
            $key = $scl_info['config_item_source'].'_'.$scl_info['config_item_id'];
            if(key_exists($key, $_sub_item_list)){
                $_sub_item_list[$key]['is_config'] = true;
                $_sub_item_list[$key]['item_value'] = $scl_info['item_value'];
            }
            if(key_exists($key, $_item_list)){
                //只展示子产品配置的配置项
                $_item_list[$key]['is_config'] = true;
                $_item_list[$key]['item_value'] = $scl_info['item_value'];
                $item_list[$key] = $_item_list[$key];
            }
        }

        $_config_list = [];//主产配置
        $_tmp_item_arr = [
            'parent_configs' => $item_list,
            'child_configs' => $_sub_item_list,
        ];
        foreach($_tmp_item_arr as $k => $__item_list) {
            foreach ($__item_list as $config_info) {
                //不可在产品中进行配置的配置项不进行展示
                if($config_info['config_scope'] == Enu::PRODUCT_CONFIG_ITEM_PRODUCT_CAN_NOT_EDIT){
                    continue;
                }
                if ($config_info['item_type'] == Enu::PRODUCT_CONFIG_ITEM_TYPE_CHECKBOX) {
                    $_defaults                 = explode(',', $config_info['item_value']);
                    $config_info['item_value'] = [];
                    foreach ($_defaults as $d) {
                        $config_info['item_value'][] = $d;
                    }
                }

                $_info = [
                    'product_id'         => $product_id,
                    'config_item_id'     => $config_info['id'],
                    'config_item_name'   => $config_info['item_name'],
                    'is_config'          => $config_info['is_config'],
                    'config_item_key'    => $config_info['item_key'],
                    'item_type'          => $config_info['item_type'],
                    'item_options'       => $config_info['item_options'],
                    'item_value'         => $config_info['item_value'],
                    'config_item_source' => $config_info['config_item_source'],
                    'config_scope' => $config_info['config_scope'],
                    'can_del'            => $k == 'parent_configs',//是否可以删除
                ];

                $_config_list[$k][$config_info['role']][] = $_info;
            }
        }
        $role_map = ProductConfigItemLogic::$role_map;
        $roles = RoleLogic::get_user_role($this->admin);
        $role = 0;
        foreach($roles as $_role){
            if(in_array($_role,$role_map)){
                $role = $_role;
            }
        }

        $config_list = [
            "te" => [
                "role_id"        => Enu::USER_ROLE_TYPE_TECHNICIAN,
                "role_name"      => "技术",
                "can_edit"       => in_array(Enu::USER_ROLE_TYPE_TECHNICIAN,$this->roles),
                "parent_configs" => isset($_config_list['parent_configs'][Enu::USER_ROLE_TYPE_TECHNICIAN]) ? $_config_list['parent_configs'][Enu::USER_ROLE_TYPE_TECHNICIAN] : [],
                "child_configs"  => isset($_config_list['child_configs'][Enu::USER_ROLE_TYPE_TECHNICIAN]) ? $_config_list['child_configs'][Enu::USER_ROLE_TYPE_TECHNICIAN] : [],
            ],
            "pm" => [
                "role_id"        => Enu::USER_ROLE_TYPE_PRODUCT_MANAGER,
                "role_name"      => "产品",
                "can_edit"       => in_array(Enu::USER_ROLE_TYPE_PRODUCT_MANAGER,$this->roles),
                "parent_configs" => isset($_config_list['parent_configs'][Enu::USER_ROLE_TYPE_PRODUCT_MANAGER]) ? $_config_list['parent_configs'][Enu::USER_ROLE_TYPE_PRODUCT_MANAGER] : [],
                "child_configs"  => isset($_config_list['child_configs'][Enu::USER_ROLE_TYPE_PRODUCT_MANAGER]) ? $_config_list['child_configs'][Enu::USER_ROLE_TYPE_PRODUCT_MANAGER] : [],
            ],
            "op" => [
                "role_id"        => Enu::USER_ROLE_TYPE_OPERATOR,
                "role_name"      => "运营",
                "can_edit"       => in_array(Enu::USER_ROLE_TYPE_OPERATOR,$this->roles),
                "parent_configs" => isset($_config_list['parent_configs'][Enu::USER_ROLE_TYPE_OPERATOR]) ? $_config_list['parent_configs'][Enu::USER_ROLE_TYPE_OPERATOR] : [],
                "child_configs"  => isset($_config_list['child_configs'][Enu::USER_ROLE_TYPE_OPERATOR]) ? $_config_list['child_configs'][Enu::USER_ROLE_TYPE_OPERATOR] : [],
            ],
        ];

        $res = [];
        $role_map = array_flip($role_map);
        $sub_product_info['config_path'] = 'sub_product';
        $res['info'] = $sub_product_info;
        $res['item_list'] = $config_list;
        $res['default_tag'] = isset($role_map[$role])?$role_map[$role]:'';

        $this->response(Enu::RESP_OK,$res);
    }
}