<?php

namespace App\Controllers\Product;

use App\Controllers\BaseController;
use App\Logics\Approval\ApprovalLogic;
use App\Logics\Config\ConfigLogic;
use App\Logics\Config\ProductConfigItemLogic;
use App\Logics\Config\SubProductConfigItemLogic;
use App\Logics\Product\ProductLogic;
use App\Logics\System\RoleLogic;
use App\Models\Product\ProductConfigItemModel;
use App\Models\Product\ProductModel;
use App\Repositories\Product\ProductConfigItemRepositorie;
use App\Repositories\Product\ProductConfigRepositorie;
use App\Repositories\Product\ProductRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

/**
 * 主产品
 */
class ProductController extends BaseController
{
    /**
     * 主产品列表
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-25 17:04:09
     */
    public function productList(){
        $page       = intval($this->request->get('page', 1));
        $page_size  = intval($this->request->get('page_size', 20));
        $product_id = $this->request->get('product_id');
        $sub_product_id = $this->request->get('sub_product_id');

        //默认展示邦秒验产品
        if(empty($product_id) && empty($sub_product_id)){
            $product_id = [Enu::PRODUCT_BMY_PRODUCT_ID];
        }else {
            if (empty($product_id)) {
                $product_id = ProductModel::getFatherId($sub_product_id);
            }
            $product_id = [$product_id, $sub_product_id];
        }

        $res = ProductLogic::product_list($product_id, $page, $page_size,$this->admin);
        $this->response($res['code'], $res['data']);
    }


    /**
     * 全部产品的下拉列表数据
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-25 17:28:11
     *
     */
    public function options(){
        $_list = ProductRepositorie::getAllProduct();

        $res = [];
        foreach($_list as $info){
            $res[] = [
                'value' => $info['product_id'],
                'label' => $info['product_name']
            ];
        }

        $this->response(Enu::RESP_OK, $res);
    }


    /**
     * 获取添加配置项相关值
     *
     * @return void
     * <AUTHOR> 2023-09-25 18:54:40
     *
     */
    public function itemOptions(){
        $_product_config_options = config('options.product_config');
        $product_config_options = [];
        //整理为前端使用的结构
        foreach($_product_config_options as $field => $options){
            foreach($options as $value => $label){
                $product_config_options[$field][] = [
                    'value' => $value,
                    'label' => $label
                ];
            }
        }

        $this->response(Enu::RESP_OK, $product_config_options);
    }


    /**
     * 添加主产品
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-25 17:04:24
     *
     */
    public function add(){
        $product_id = intval($this->request->get('product_id'));
        $product_name = $this->request->get('product_name');
        $product_key = $this->request->get('product_key');

        if(empty($product_name)){
            $this->response(Enu::RESP_PRODUCT_MISSING_PRODUCT_NAME);
        }
        if(empty($product_id)){
            $this->response(Enu::RESP_PRODUCT_MISSING_PRODUCT_ID);
        }
        if(empty($product_key)){
            $this->response(Enu::RESP_PRODUCT_MISSING_PRODUCT_KEY);
        }

        $product_count = ProductRepositorie::getProductCount([['product_id','=',$product_id]]);
        if($product_count > 0) {
            $this->response(Enu::RESP_PRODUCT_DUPLICATION_PRODUCT_ID);
        }

        $product_count = ProductRepositorie::getProductCount([['product_name','=',$product_name]]);
        if($product_count > 0) {
            $this->response(Enu::RESP_PRODUCT_DUPLICATION_PRODUCT_NAME);
        }

        $id = ProductRepositorie::createProduct($product_id, $product_name, $product_key, $this->admin);
        if($id){
            $this->response(Enu::RESP_OK);
        }else{
            $this->response(Enu::RESP_FAILED);
        }
    }

    /**
     * 添加产品配置项
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-25 17:05:02
     *
     */
    public function addConfigItem(){
        $product_id   = intval($this->request->get('product_id'));                       //产品id
        $item_key     = $this->request->get('item_key');                                 //配置项key
        $item_name    = $this->request->get('item_name');                                //配置项名称
        $role         = $this->request->get('role');                                     //所属角色
        $modify       = $this->request->get('modify');                                   //是否可被客户编辑
        $extend       = $this->request->get('extend');                                   //是否可被子产品继承
        $config_scope = $this->request->get('config_scope');                             //是否可在产品中进行配置 配置范围
        $item_type    = $this->request->get('item_type');                                //配置项类型
        $item_options = $this->request->get('item_options', '', false);  //配置项选项
        $item_default = $this->request->get('item_default', '', false);  //配置项默认值

        try {
            $res = ProductConfigItemLogic::add_item($product_id,$item_key,$item_name,$role,$modify,$extend,$config_scope,$item_type,$item_options,$item_default,$this->admin);
            if($res <= 0){
                $this->response(Enu::RESP_FAILED);
            }
        }catch(Exception $e){
            $this->response($e->getCode());
        }

        $this->response(Enu::RESP_OK, '已提交成功,等待审核!');
    }

    /**
     * 编辑配置
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-27 00:03:02
     *
     */
    public function editConfig(){
        $product_id = intval($this->request->get('product_id'));                    //产品id
        $item_id    = $this->request->get('item_id');                               //配置项id
        $item_value = $this->request->get('item_value', '', false); //配置项值

        if(empty($product_id)){
            $this->response(Enu::RESP_PRODUCT_MISSING_PRODUCT_ID);
        }
        if(empty($item_id)){
            $this->response(Enu::RESP_PRODUCT_CONFIG_ITEM_MISSING_ID);
        }
        if('' == $item_value){
            $this->response(Enu::RESP_PRODUCT_CONFIG_MISSING_VALUE);
        }

        //校验角色

        $config_item_info = ProductConfigItemRepositorie::getProductConfigItemById($item_id);

        //检查配置值是否合法
        if(ConfigLogic::is_checkbox_or_radio($config_item_info['item_type'])) {
            $config_item_info['item_options'] = json_decode($config_item_info['item_options'],JSON_UNESCAPED_UNICODE);
            $_options = array_column($config_item_info['item_options'], 'value');
            if($config_item_info['item_type'] == Enu::PRODUCT_CONFIG_ITEM_TYPE_CHECKBOX){
                if(count(array_intersect($_options,$item_value)) != count($item_value)){
                    $this->response(Enu::RESP_PRODUCT_CONFIG_UNEXPECTED_VALUE);
                }
                $item_value = implode(',',$item_value);
            }else{
                if(!in_array($item_value,$_options)){
                    $this->response(Enu::RESP_PRODUCT_CONFIG_UNEXPECTED_VALUE);
                }
            }
        }

        $config_info = ProductConfigRepositorie::getProductConfigByProductIdAndItemId($product_id,$item_id);

        $approval_type = Enu::APPROVAL_ACTION_ADD;//配置添加
        if(!empty($config_info)){
            $approval_type = Enu::APPROVAL_ACTION_EDIT;//配置编辑

            if($config_info['item_value'] === $item_value){//配置值相同 没有修改
                $this->response(Enu::RESP_PRODUCT_CONFIG_HAVE_NO_MODIFY);
            }
        }

        $data = [
            'approval_id'        => create_uuid('CA'),
            'type'               => Enu::APPROVAL_TYPE_PRODUCT_CONFIG,//产品配置
            'action'             => $approval_type,
            'group_id'           => '',
            'customer_id'        => '',
            'account_id'         => '',
            'product_id'         => $product_id,
            'sub_product_id'     => 0,
            'config_item_id'     => $item_id,
            'config_item_source' => Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT,
            'value'              => $config_info['item_value'],
            'new_value'          => $item_value,
            'status'             => Enu::APPROVAL_STATUS_NEED_APPROVAL,//待审批
            'applicant'          => $this->admin,
        ];
        try {
            $res = ApprovalLogic::add($data);
        }catch(Exception $e){
            $this->response($e->getCode());
        }

        if($res > 0){
            $this->response(Enu::RESP_OK, '已提交成功,等待审核!');
        }else{
            $this->response(Enu::RESP_FAILED);
        }
    }


    /**
     * 编辑产品
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-25 17:04:41
     *
     */
    public function edit(){
        $product_id   = intval($this->request->get('product_id')); //产品id
        $product_name = $this->request->get('product_name');

        if(empty($product_id)){
            $this->response(Enu::RESP_PRODUCT_MISSING_PRODUCT_ID);
        }
        if(empty($product_name)){
            $this->response(Enu::RESP_PRODUCT_MISSING_PRODUCT_NAME);
        }

        $product_count = ProductRepositorie::getProductCount([['product_name','=',$product_name]]);
        if($product_count > 0) {
            $this->response(Enu::RESP_PRODUCT_DUPLICATION_PRODUCT_NAME);
        }

        $rows = ProductRepositorie::edit($product_id,$product_name,$this->admin);
        if($rows){
            $this->response(Enu::RESP_OK);
        }else{
            $this->response(Enu::RESP_FAILED);
        }
    }

    /**
     * 主产品信息和产品配置项的列表
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-26 16:47:54
     *
     */
    public function info() {
        $product_id = intval($this->request->get('product_id')); //产品id
        if(empty($product_id)){
            $this->response(Enu::RESP_PRODUCT_MISSING_PRODUCT_ID);
        }

        $product_info = ProductRepositorie::info($product_id);
        // $item_list = ProductConfigItemRepositorie::getAllProductConfigItem($product_id);
        $item_list = ProductConfigItemModel::getAllProductConfigItem($product_id);

        $product_config_options = config('options.product_config');

        foreach($item_list as &$info){
            $info['item_options'] = json_decode($info['item_options'],JSON_UNESCAPED_UNICODE);
            $info['role'] = $product_config_options['role'][$info['role']];
            $info['modify'] = $product_config_options['modify'][$info['modify']];
            $info['extend'] = $product_config_options['extend'][$info['extend']];
            $info['config_scope'] = $product_config_options['config_scope'][$info['config_scope']];
            $info['item_type_text'] = $product_config_options['item_type'][$info['item_type']];
            if(ConfigLogic::is_checkbox_or_radio($info['item_type'])) {
                $_options = array_column($info['item_options'], 'label','value');
                if($info['item_type'] == Enu::PRODUCT_CONFIG_ITEM_TYPE_CHECKBOX){
                    $_defaults = explode(',',$info['item_default']);
                    $info['item_default'] = [];
                    foreach($_defaults as $d){
                        $info['item_default'][] = $_options[$d];
                    }
                    $info['item_default'] = implode(',',$info['item_default']);
                }else {
                    $info['item_default'] = $_options[$info['item_default']];
                }
            }
        }
        $res = [];
        $res['info'] = $product_info;
        $res['item_list'] = $item_list;

        $this->response(Enu::RESP_OK,$res);
    }


    /**
     * 产品配置页面所需要的信息
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-26 17:34:48
     *
     */
    public function configInfo(){
        $product_id = intval($this->request->get('product_id')); //产品id
        if(empty($product_id)){
            $this->response(Enu::RESP_PRODUCT_MISSING_PRODUCT_ID);
        }

        $product_info = ProductRepositorie::info($product_id,['product_id','product_name','product_key']);
        $item_list = ProductConfigItemRepositorie::getAllProductConfigItem($product_id);
        $item_list = array_column($item_list,null,'id');
        foreach($item_list as &$info){
            $info['item_options'] = json_decode($info['item_options'],JSON_UNESCAPED_UNICODE);
            $info['item_value'] = $info['item_default'];
            $info['is_config'] = false;
        }

        $config_list = ProductConfigRepositorie::getProductConfigList($product_id);
        foreach($config_list as $config_info){
            $item_list[$config_info['config_item_id']]['item_value'] = $config_info['item_value'];
            $item_list[$config_info['config_item_id']]['is_config'] = true;
        }

        $item_list = array_values($item_list);
        $_item_list = [];
        foreach($item_list as $iinfo){
            //不可在产品中进行配置的配置项不进行展示
            if($iinfo['config_scope'] == Enu::PRODUCT_CONFIG_ITEM_PRODUCT_CAN_NOT_EDIT){
                continue;
            }
            if($iinfo['item_type'] == Enu::PRODUCT_CONFIG_ITEM_TYPE_CHECKBOX){
                $iinfo['item_value'] = explode(',', $iinfo['item_value']);
            }
            $_info = [
                'product_id'       => $product_id,
                'config_item_id'   => $iinfo['id'],
                'config_item_name' => $iinfo['item_name'],
                'is_config'        => $iinfo['is_config'],
                'config_item_key'  => $iinfo['item_key'],
                'item_type'        => $iinfo['item_type'],
                'item_options'     => $iinfo['item_options'],
                'item_value'       => $iinfo['item_value'],
                'can_del'          => false,//是否可以删除
            ];

            $_item_list[$iinfo['role']][] = $_info;
        }

        $role_map = ProductConfigItemLogic::$role_map;
        $roles = RoleLogic::get_user_role($this->admin);
        $role = 0;
        foreach($roles as $_role){
            if(in_array($_role,$role_map)){
                $role = $_role;
            }
        }

        $config_list = [
            "te" => [
                "role_id"   => Enu::USER_ROLE_TYPE_TECHNICIAN,
                "role_name" => "技术",
                "can_edit"  => in_array(Enu::USER_ROLE_TYPE_TECHNICIAN,$this->roles),
                "configs"   => key_exists(Enu::USER_ROLE_TYPE_TECHNICIAN, $_item_list)?$_item_list[Enu::USER_ROLE_TYPE_TECHNICIAN]:[],
            ],
            "pm" => [
                "role_id"   => Enu::USER_ROLE_TYPE_PRODUCT_MANAGER,
                "role_name" => "产品",
                "can_edit"  => in_array(Enu::USER_ROLE_TYPE_PRODUCT_MANAGER,$this->roles),
                "configs"   => key_exists(Enu::USER_ROLE_TYPE_PRODUCT_MANAGER, $_item_list)?$_item_list[Enu::USER_ROLE_TYPE_PRODUCT_MANAGER]:[],
            ],
            "op" => [
                "role_id"   => Enu::USER_ROLE_TYPE_OPERATOR,
                "role_name" => "运营",
                "can_edit"  => in_array(Enu::USER_ROLE_TYPE_OPERATOR,$this->roles),
                "configs"   => key_exists(Enu::USER_ROLE_TYPE_OPERATOR, $_item_list)?$_item_list[Enu::USER_ROLE_TYPE_OPERATOR]:[],
            ],
        ];

        $res = [];
        $role_map = array_flip($role_map);

        $product_info['config_path'] = 'product';
        $res['info'] = $product_info;
        $res['config_list'] = $config_list;
        $res['default_tag'] = isset($role_map[$role])?$role_map[$role]:'';
        $this->response(Enu::RESP_OK,$res);
    }


    /**
     * 获取主产品可继承配置项
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-10-26 11:09:02
     *
     */
    public function extendConfigItems(){
        $product_id = intval($this->request->get('product_id')); //产品id
        $role = $this->request->get('role'); //产品id
        $modify = $this->request->get('modify'); //是否可被客户修改
        $customer_can_modify = 0;

        $config_path = $this->request->get('config_path');

        $group_id    = $this->request->get('group_id');
        $customer_id = $this->request->get('customer_id');
        $account_id  = $this->request->get('account_id');

        $role_map = ProductConfigItemLogic::$role_map;
        $role = isset($role_map[$role])?$role_map[$role]:0;

        if(empty($product_id)){
            $this->response(Enu::RESP_PRODUCT_MISSING_PRODUCT_ID);
        }
        if(empty($role)){
            $this->response(Enu::RESP_PRODUCT_CONFIG_ITEM_MISSING_ROLE);
        }
        if(empty($config_path)){
            $this->response(Enu::RESP_PRODUCT_CONFIG_ITEM_MISSING_CONFIG_PATH);
        }

        if($modify == Enu::PRODUCT_CONFIG_ITEM_CUSTOMER_CAN_MODIFY){
            $customer_can_modify = $modify;
        }

        $config_list = ProductConfigItemLogic::get_extend_config_items($product_id,$group_id,$customer_id,$account_id,$config_path,$role,$customer_can_modify);

        $this->response(Enu::RESP_OK,$config_list);
    }
}