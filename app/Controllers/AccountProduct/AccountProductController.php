<?php

namespace App\Controllers\AccountProduct;

use App\Controllers\BaseController;
use App\Repositories\Product\AccountProductRepositorie;
use App\Repositories\Product\ProductRepositorie;
use App\Repositories\Product\SubProductRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

/**
 * 主产品
 */
class AccountProductController extends BaseController
{

    /**
     * 账户开通产品
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-28 16:21:13
     *
     */
    public function add(){
        $account_id       = $this->request->get('account_id');            //客户id
        $product_id       = $this->request->get('product_id');            //产品id
        $contract_status  = $this->request->get('contract_status', -1);    //签约状态
        $end_time         = $this->request->get('end_time');              //截至日期
        $daily_limit      = $this->request->get('daily_limit');      //日限额用量
        $month_limit      = $this->request->get('month_limit');      //月限额用量
        $year_limit       = $this->request->get('year_limit');       //年限额量
        $total_limit      = $this->request->get('total_limit');      //总限额量
        $concurrency      = $this->request->get('concurrency');      //秒并发
        $limit_start_date = $this->request->get('limit_start_date');      //总限量预警开始时间

        $daily_limit = $daily_limit === false ? - 1 : $daily_limit;
        $month_limit = $month_limit === false ? - 1 : $month_limit;
        $year_limit  = $year_limit === false ? - 1 : $year_limit;
        $total_limit = $total_limit === false ? - 1 : $total_limit;
        $concurrency = $concurrency === false ? - 1 : $concurrency;

        if(empty($account_id)){
            $this->response(Enu::RESP_CUSTOMER_PRODUCT_CONFIG_MISSING_ACCOUNT_ID);
        }
        if(empty($product_id)){
            $this->response(Enu::RESP_PRODUCT_MISSING_PRODUCT_ID);
        }
        // if(empty($daily_limit) || empty($month_limit) || empty($year_limit) || empty($total_limit) || empty($concurrency) || empty($limit_start_date)){
        //     $this->response(Enu::RESP_SERVICE_UNAVAILABLE);
        // }
        if(empty($end_time) || empty($limit_start_date)){
            //TODO 修改返回信息
            $this->response(Enu::RESP_SERVICE_UNAVAILABLE);
        }

        $sub_product_info = SubProductRepositorie::info($product_id);
        if(empty($sub_product_info)){
            //TODO 修改返回信息
            $this->response(Enu::RESP_SERVICE_UNAVAILABLE);
        }
        $father_id = $sub_product_info['father_id'];

        $now = time();//date(Enu::TIME_FORMAT);
        $data = [
            'account_id'       => $account_id,
            'product_id'       => $father_id,
            'status'           => 1,
            'end_time'         => $end_time,
            'contract_status'  => $contract_status,
            'daily_limit'      => $daily_limit,
            'month_limit'      => $month_limit,
            'year_limit'       => $year_limit,
            'total_limit'      => $total_limit,
            'concurrency'      => $concurrency,
            'limit_start_date' => $limit_start_date,
            'create_at'        => $now,
            'update_at'        => $now,
        ];
        $res = AccountProductRepositorie::add($data);
        if($res > 0) {
            $data['product_id'] = $product_id;
            $res = AccountProductRepositorie::add($data);
            if($res > 0) {
                $this->response(Enu::RESP_OK, $res);
            }else{
                //TODO 修改返回信息
                $this->response(Enu::RESP_SERVICE_UNAVAILABLE, $res);
            }
        }else{
            //TODO 修改返回信息
            $this->response(Enu::RESP_SERVICE_UNAVAILABLE, $res);
        }
    }


    /**
     *
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-10-07 16:58:53
     *
     */
    public function options(){
        $res['status'] = [[
                'value' => '1',
                'label' => '可用',
            ], [
                'value' => '0',
                'label' => '禁用',
        ],];

        $res['contract_status'] = [[
                'value' => '1',
                'label' => '已签约已付款',
            ], [
                'value' => '2',
                'label' => '已签约未付费',
            ], [
                'value' => '3',
                'label' => '未签约',
            ], [
                'value' => '4',
                'label' => '其他',
            ], [
                'value' => '5',
                'label' => '特殊客户',
        ],];

        $products = [];

        //产品列表
        $product_list = ProductRepositorie::getAllProduct();
        $product_list = array_column($product_list, 'product_name','product_id');

        foreach($product_list as $k => $v){
             $products[] = [
                'value' => ''.($k),
                'label' => $v,
            ];
        }

        $sub_product_list = SubProductRepositorie::getAllSubProduct();
        foreach($sub_product_list as $info){
            $products[] = [
                'value' => ''.$info['product_id'],
                'label' => (isset($product_list[$info['father_id']])?$product_list[$info['father_id']]:$info['father_id']).' / '.$info['product_name'],
            ];
        }

        $res['product'] = $products;

        $this->response(Enu::RESP_OK,$res);
    }
}