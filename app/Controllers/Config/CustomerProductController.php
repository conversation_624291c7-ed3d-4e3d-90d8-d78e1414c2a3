<?php

namespace App\Controllers\Config;

use App\Controllers\BaseController;
use App\Logics\Approval\ApprovalLogic;
use App\Logics\Config\ConfigLogic;
use App\Logics\Config\ProductConfigItemLogic;
use App\Logics\System\RoleLogic;
use App\Models\Product\ProductConfigItemModel;
use App\Models\Product\SubProductConfigItemModel;
use App\Repositories\Customer\AccountRepositorie;
use App\Repositories\Customer\CustomerGroupRepositorie;
use App\Repositories\Customer\CustomerRepositorie;
use App\Repositories\OptionsRepositorie;
use App\Repositories\Product\AccountProductRepositorie;
use App\Repositories\Product\ProductConfigItemRepositorie;
use App\Repositories\Product\ProductConfigRepositorie;
use App\Repositories\Product\ProductRepositorie;
use App\Repositories\Product\SubProductConfigItemRepositorie;
use App\Repositories\Product\SubProductConfigRepositorie;
use App\Repositories\Product\SubProductRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

/**
 * 主产品
 */
class CustomerProductController extends BaseController
{

    /**
     * 获取客户产品列表
     * TODO 删除
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-27 20:06:35
     *
     */
    public function customerProductList(){
        $page           = intval($this->request->get('page', 1));
        $page_size      = intval($this->request->get('page_size', 20));
        $product_id     = intval($this->request->get('product_id'));     //产品id
        $sub_product_id = intval($this->request->get('sub_product_id')); //子产品id
        $group_id       = $this->request->get('group_id');               //主体id
        $customer_id    = $this->request->get('customer_id');            //客户id
        $account_id     = $this->request->get('account_id');             //账号id
        $res = [
            'page'      => $page,
            'page_size' => $page_size,
            'count'     => 0,
            'list'      => [],
        ];

        $customer_id_arr = [];
        if(!empty($product_id) || !empty($sub_product_id) || !empty($group_id) || !empty($customer_id) || !empty($account_id)){
            $customer_id_arr = $this->get_customer_ids($product_id,$sub_product_id,$group_id,$customer_id,$account_id);
            if(empty($customer_id_arr)){
                //返回空
                $this->response(Enu::RESP_OK,$res);
            }
        }

        $where = [];
        if(!empty($customer_id_arr)) {
            $where[] = ['customer_id', 'in', $customer_id_arr];
        }
        $custoemr_list  = CustomerRepositorie::getCustomerList($where,['*'],$page_size,$page,['group_id','desc']);
        $custoemr_count = CustomerRepositorie::getCustomerCount($where);
        $res['count'] = $custoemr_count;

        $group_ids = array_column($custoemr_list, 'group_id');
        $group_list = CustomerGroupRepositorie::getCustomerGroupListByGrouupIds($group_ids);
        $group_list = array_column($group_list, null, 'group_id');

        $customer_ids = array_column($custoemr_list, 'customer_id');
        //2 获取客户的账号
        $_account_list = AccountRepositorie::getListWithCustomers($customer_ids);
        $account_names = [];
        $account_list = [];
        $account_ids = [];
        foreach($_account_list as $account_info){
            $_customer_id = $account_info['customer_id'];
            $account_ids[] = $account_info['account_id'];
            $account_names[$account_info['account_id']] = $account_info['account_name'];
            if(!key_exists($_customer_id, $account_list)){
                $account_list[$_customer_id] = [];
            }
            $account_list[$_customer_id][] = $account_info;
        }

        //3 获取账号关联的产品 子产品
        $_account_product_list = AccountProductRepositorie::getListWithAccounts($account_ids);
        $product_ids = [];
        $account_product_list = [];
        foreach($_account_product_list as $ap_info){
            $product_ids[] = $ap_info['product_id'];
            $_account_id = $ap_info['account_id'];
            if(!key_exists($_account_id, $account_product_list)){
                $account_product_list[$_account_id] = [];
            }
            $account_product_list[$_account_id][] = $ap_info;
        }
        //5 子产品信息
        $sub_product_list = SubProductRepositorie::getSubProductListByProductIds($product_ids);
        $father_ids = array_column($sub_product_list, 'father_id');
        $product_ids = array_merge($product_ids,$father_ids);
        $sub_product_list = array_column($sub_product_list, null, 'product_id');

        //4 主产品信息
        $product_list = ProductRepositorie::getMainProductListByProductIds($product_ids);
        $product_list = array_column($product_list, null, 'product_id');

        $res_list = [];
        foreach ($custoemr_list as $c_info){
            $customer_id = $c_info['customer_id'];
            $_info = [
                'customer_id'   => $c_info['customer_id'],
                'customer_name' => $c_info['name'],
                'group_id'      => $c_info['group_id'],
                'group_name'    => empty($c_info['group_id'])?'':$group_list[$c_info['group_id']]['group_name'],
            ];
            if(!key_exists($customer_id,$account_list)){
                $_info['account_id'] = '';
                $_info['account_name'] = '';
                $_info['product_list'] = [];
                $res_list[] = $_info;
            }else{
                foreach($account_list[$customer_id] as $account_info){//客户
                    $account_id = $account_info['account_id'];
                    $_info['account_id']   = $account_id;
                    $_info['account_name'] = $account_names[$account_id];
                    if(!key_exists($account_id, $account_product_list)){
                        $_info['product_list'] = [];
                    }else{
                        $c_info_product_list = [];
                        foreach ($account_product_list[$account_id] as $ap_info){//产品
                            $product_id = $ap_info['product_id'];
                            if(key_exists($product_id, $product_list)){
                                $c_info_product_name = $product_list[$product_id]['product_name'];
                                $c_info_product_wt = "wt".$product_id;
                                $c_info_product_is_father = true;

                            }else {
                                $c_info_product_name = $sub_product_list[$product_id]['product_name'];
                                $c_info_product_father_id = $sub_product_list[$product_id]['father_id'];
                                $c_info_product_wt = "wt".$c_info_product_father_id.$product_id;
                                $c_info_product_is_father = false;
                            }
                            $c_info_product_list[] = [
                                'product_id'        => $ap_info['product_id'],
                                'product_name'      => $c_info_product_name,
                                'is_father_product' => $c_info_product_is_father,
                                'product_wt'        => $c_info_product_wt,
                            ];
                        }
                        usort($c_info_product_list,function($a,$b){
                            return $a['product_wt'] > $b['product_wt'];
                        });
                        $_info['product_list'] = $c_info_product_list;
                    }
                    $res_list[] = $_info;
                }
            }
        }

        usort($res_list, function($a,$b){
            $cmp = strcmp($a['group_id'], $b['group_id']);
            if ($cmp == 0) {
                $_cmp = strcmp($a['customer_id'], $b['customer_id']);
                if ($_cmp == 0) {
                    $__cmp = strcmp($a['account_id'], $b['account_id']);
                    if ($__cmp == 0) {
                        return strcmp($b['product_wt'], $a['product_wt']);
                    }
                }
                return $_cmp;
            }
            return $cmp;
        });

        //主体合并
        $group_count = [];
        //客户合并
        $customer_count = [];
        foreach($res_list as $rl_info){
            if(!empty($rl_info['group_id'])) {
                $group_count[$rl_info['group_id']] = key_exists($rl_info['group_id'], $group_count) ? $group_count[$rl_info['group_id']] + 1 : 1;
            }
            $customer_count[$rl_info['customer_id']] = key_exists($rl_info['customer_id'], $customer_count) ? $customer_count[$rl_info['customer_id']] + 1 : 1;
        }

        foreach($res_list as &$rl_info){
            if (!empty($rl_info['group_id'])) {
                $rl_info['group_merge_count'] = $group_count[$rl_info['group_id']];
                if ($group_count[$rl_info['group_id']] > 0) {
                    $group_count[$rl_info['group_id']] = 0;
                }
            }else{
                $rl_info['group_merge_count'] = 1;
            }
            $rl_info['customer_merge_count'] = $customer_count[$rl_info['customer_id']];
            if ($customer_count[$rl_info['customer_id']] > 0) {
                $customer_count[$rl_info['customer_id']] = 0;
            }
        }

        $res['list'] = $res_list;
        // $res['list'] = $tmp_list;
        $this->response(Enu::RESP_OK,$res);
    }


    /**
     * 根据条件获取客户id
     * TODO 删除
     *
     * @param $product_id
     * @param $sub_product_id
     * @param $group_id
     * @param $customer_id
     * @param $account_id
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2023-09-27 20:11:35
     */
    private function get_customer_ids($product_id,$sub_product_id,$group_id,$customer_id,$account_id){
        $customer_id_arr = [];
        if(!empty($customer_id)){
            return [$customer_id];
        }

        if(!empty($account_id)){
            $account_list = AccountRepositorie::getListByAccountId($account_id);
            $customer_id_arr = array_column($account_list, 'customer_id');
        }

        if(!empty($group_id)) {
            $group_list = CustomerRepositorie::getCustomerWithGroup([$group_id]);
            $_customer_ids = array_column($group_list, 'customer_id');
            $customer_id_arr = empty($customer_id_arr) ? $_customer_ids : array_intersect($customer_id_arr,$_customer_ids);
        }

        $sub_product_id_arr = [];
        if(!empty($sub_product_id)){
            $sub_product_id_arr = [$sub_product_id];
        }
        if(!empty($product_id)){
            if(empty($sub_product_id)) {
                // $sub_product_id_arr   = SubProductRepositorie::getAllSubProduct($product_id);
                // $sub_product_id_arr   = array_column($sub_product_id_arr, 'product_id');
                $sub_product_id_arr[] = $product_id;
            }
        }
        if(!empty($sub_product_id_arr)){
            $account_list = AccountProductRepositorie::getListWithProductIds($sub_product_id_arr);
            $account_list = AccountRepositorie::getListByAccountIds(array_column($account_list, 'account_id'));
            $_customer_ids = array_column($account_list, 'customer_id');
            $customer_id_arr = empty($customer_id_arr) ? $_customer_ids : array_intersect($customer_id_arr,$_customer_ids);
        }

        return $customer_id_arr;
    }


    /**
     * 客户产品列表中需要的下拉筛选条件
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-28 04:36:09
     */
    public function customerProductOptions(){
        $product_id     = $this->request->get('product_id');

        $res = [];
        $_customer_id_arr = CustomerRepositorie::getAllCustomer();
        $customer_id_arr = [];
        foreach($_customer_id_arr as $customer_info){
            $customer_id_arr[] = [
                'value' => $customer_info['customer_id'],
                'label' => $customer_info['name'],
            ];
        }
        $res['customer'] = $customer_id_arr;

        $_group_id_arr = CustomerGroupRepositorie::getAllCustomerGroup();
        $group_id_arr = [];
        foreach($_group_id_arr as $group_info){
            $group_id_arr[] = [
                'value' => $group_info['group_id'],
                'label' => $group_info['group_name'],
            ];
        }
        $res['group'] = $group_id_arr;

        $_account_id_arr = AccountRepositorie::getAllAccount();
        $account_id_arr = [];
        foreach($_account_id_arr as $account_info){
            $account_id_arr[] = [
                'value' => $account_info['account_id'],
                'label' => $account_info['account_name'],
            ];
        }
        $res['account'] = $account_id_arr;

        $_item_id_arr = ProductConfigItemModel::getAll([]);
        $item = [];
        $item_id_arr = [];
        foreach($_item_id_arr as $item_info){
            if(isset($item[$item_info['item_name']])){
                continue;
            }
            $item_id_arr[] = [
                'value' => $item_info['item_name'],
                'label' => $item_info['item_name'],
            ];
            $item[$item_info['item_name']] = $item_info['item_name'];
        }
        $_item_id_arr = SubProductConfigItemModel::getAll([]);
        foreach($_item_id_arr as $item_info){
            if(isset($item[$item_info['item_name']])){
                continue;
            }
            $item_id_arr[] = [
                'value' => $item_info['item_name'],
                'label' => $item_info['item_name'],
            ];
            $item[$item_info['item_name']] = $item_info['item_name'];
        }
        $res['item'] = $item_id_arr;

        // $_product_id_arr = ProductRepositorie::getAllProduct();
        // $product_id_arr = [];
        // foreach($_product_id_arr as $product_info){
        //     $product_id_arr[] = [
        //         'value' => $product_info['product_id'],
        //         'label' => $product_info['product_name'],
        //     ];
        // }
        $product_id     = $this->request->get('product_id');
        $res['product'] = OptionsRepositorie::product_options($product_id);

        $config_path_arr = [];
        $_config_path = config('options.config_path');
        foreach($_config_path as $key => $value){
            $config_path_arr[] = [
                'value' => $key,
                'label' => $value,
            ];
        }
        $res['config_path'] = $config_path_arr;



        $this->response(Enu::RESP_OK,$res);
    }


    /**
     * 客户产品配置详情页面 在该页面进行配置编辑
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-28 04:49:38
     *
     */
    public function info(){
        $product_id     = intval($this->request->get('product_id'));     //产品id
        $sub_product_id = intval($this->request->get('sub_product_id')); //子产品id
        $group_id       = $this->request->get('group_id');               //主体id
        $customer_id    = $this->request->get('customer_id');            //客户id
        $account_id     = $this->request->get('account_id');             //账号id

        $res = [];

        $flag = empty($group_id) ? '_' : 'g';
        $flag .= empty($customer_id) ? '_' : 'c';
        $flag .= empty($account_id) ? '_' : 'a';
        $flag .= empty($product_id) ? '_' : 'p';
        $flag .= empty($sub_product_id) ? '_' : 's';

        $dimensional_map = [
            "g__p_" => '主体-主产品',
            "g___s" => '主体-子产品',
            "_c_p_" => '客户-主产品',
            "_c__s" => '客户-子产品',
            "__ap_" => '账号-主产品',
            "__a_s" => '账号-子产品',
        ];
        $config_path = [
            "g__p_" => 'group_product',
            "g___s" => 'group_sub_product',
            "_c_p_" => 'customer_product',
            "_c__s" => 'customer_sub_product',
            "__ap_" => 'account_product',
            "__a_s" => 'account_sub_product',
        ];

        $role = [
            Enu::USER_ROLE_TYPE_OPERATOR,//运营
            Enu::USER_ROLE_TYPE_TECHNICIAN,//技术
            Enu::USER_ROLE_TYPE_PRODUCT_MANAGER,//产品
        ];

        $list = [];
        switch ($flag){
            case "g__p_":
                $list = $this->g__p_($group_id,$product_id,$role);//主体-主产品
                break;
            case "g___s":
                $list = $this->g___s($group_id,$sub_product_id,$role);//主体-子产品
                break;
            case "_c_p_":
                $list = $this->_c_p_($customer_id,$product_id,$role);//客户-主产品
                break;
            case "_c__s":
                $list = $this->_c__s($customer_id,$sub_product_id,$role);//客户-子产品
                break;
            case "__ap_":
                $list = $this->__ap_($account_id,$product_id,$role);//账户-主产品
                break;
            case "__a_s":
                $list = $this->__a_s($account_id,$sub_product_id,$role);//账户-子产品
                break;
            default:
                $this->response(Enu::RESP_CUSTOMER_PRODUCT_CONFIG_WORNG_PARA);
                break;
        }

        $_config_list = [];
        foreach ($list as $config_info) {
            if(ConfigLogic::is_checkbox_or_radio($config_info['item_type'])){
                $config_info['item_options'] = json_decode($config_info['item_options'],JSON_UNESCAPED_UNICODE);
            }
            if($config_info['item_type'] == Enu::PRODUCT_CONFIG_ITEM_TYPE_CHECKBOX){
                $_defaults = explode(',',$config_info['item_value']);
                $config_info['item_value'] = [];
                foreach($_defaults as $d){
                    $config_info['item_value'][] = $d;
                }
            }

            $_info = [
                'product_id'         => $product_id,
                'config_item_id'     => $config_info['id'],
                'config_item_name'   => $config_info['item_name'],
                'config_item_key'    => $config_info['item_key'],
                'item_type'          => $config_info['item_type'],
                'item_options'       => $config_info['item_options'],
                'item_value'         => $config_info['item_value'],
                'is_config'          => $config_info['is_config'],
                'role'               => $config_info['role'],
                'config_item_source' => isset($config_info['config_item_source'])?$config_info['config_item_source']:'',
                'can_del'            => $config_info['is_config'],//是否可以删除
            ];
            if(isset($config_info['config_item_source'])) {
                $cp_key = $config_info['config_item_source'] == 1 ? 'parent_configs' : 'child_configs';//区分主产品,子产品配置项
            }else{
                $cp_key = 'parent_configs';
            }
            if(in_array($flag,['g___s','_c__s','__a_s'])){
                if ($cp_key == 'parent_configs' && $config_info['is_config'] == false) {
                    continue;//仅展示子产品已经配置的配置项
                }
            }
            $_config_list[$cp_key][$config_info['role']][] = $_info;
        }

        $role_map = ProductConfigItemLogic::$role_map;
        $roles = RoleLogic::get_user_role($this->admin);
        $role = 0;
        foreach($roles as $_role){
            if(in_array($_role,$role_map)){
                $role = $_role;
            }
        }

        $config_list = [
            "te" => [
                "role_id"   => Enu::USER_ROLE_TYPE_TECHNICIAN,
                "role_name" => "技术",
                "can_edit"  => in_array(Enu::USER_ROLE_TYPE_TECHNICIAN,$this->roles),
                "parent_configs" => isset($_config_list['parent_configs'][Enu::USER_ROLE_TYPE_TECHNICIAN]) ? $_config_list['parent_configs'][Enu::USER_ROLE_TYPE_TECHNICIAN] : [],
                "child_configs"  => isset($_config_list['child_configs'][Enu::USER_ROLE_TYPE_TECHNICIAN]) ? $_config_list['child_configs'][Enu::USER_ROLE_TYPE_TECHNICIAN] : [],
            ],
            "pm" => [
                "role_id"   => Enu::USER_ROLE_TYPE_PRODUCT_MANAGER,
                "role_name" => "产品",
                "can_edit"  => in_array(Enu::USER_ROLE_TYPE_PRODUCT_MANAGER,$this->roles),
                "parent_configs" => isset($_config_list['parent_configs'][Enu::USER_ROLE_TYPE_PRODUCT_MANAGER]) ? $_config_list['parent_configs'][Enu::USER_ROLE_TYPE_PRODUCT_MANAGER] : [],
                "child_configs"  => isset($_config_list['child_configs'][Enu::USER_ROLE_TYPE_PRODUCT_MANAGER]) ? $_config_list['child_configs'][Enu::USER_ROLE_TYPE_PRODUCT_MANAGER] : [],
            ],
            "op" => [
                "role_id"   => Enu::USER_ROLE_TYPE_OPERATOR,
                "role_name" => "运营",
                "can_edit"  => in_array(Enu::USER_ROLE_TYPE_OPERATOR,$this->roles),
                "parent_configs" => isset($_config_list['parent_configs'][Enu::USER_ROLE_TYPE_OPERATOR]) ? $_config_list['parent_configs'][Enu::USER_ROLE_TYPE_OPERATOR] : [],
                "child_configs"  => isset($_config_list['child_configs'][Enu::USER_ROLE_TYPE_OPERATOR]) ? $_config_list['child_configs'][Enu::USER_ROLE_TYPE_OPERATOR] : [],
            ],
        ];


        if(!empty($account_id)){
            $account_info = AccountRepositorie::getAccountInfo($account_id);
            $customer_id = $account_info['customer_id'];
        }
        if(!empty($customer_id)) {
            $customer_info = CustomerRepositorie::getCustomerInfo($customer_id);
            $group_id = $customer_info['group_id'];
        }
        $group_info = CustomerGroupRepositorie::getCustomerGroupInfo($group_id);

        if(!empty($sub_product_id)){
            $sub_product_info = SubProductRepositorie::info($sub_product_id);
            $product_id = $sub_product_info['father_id'];
        }
        $product_info = ProductRepositorie::info($product_id);

        $information = [
            'dimensional'      => $dimensional_map[$flag],
            'config_path'      => $config_path[$flag],
            'group_id'         => $group_id,
            'customer_id'      => $customer_id,
            'account_id'       => $account_id,
            'product_id'       => $product_id,
            'sub_product_id'   => $sub_product_id,
            'group_name'       => isset($group_info['group_name']) ? $group_info['group_name'] : '',
            'customer_name'    => isset($customer_info['name']) ? $customer_info['name'] : '',
            'account_name'     => isset($account_info['account_name']) ? $account_info['account_name'] : '',
            'product_name'     => isset($product_info['product_name']) ? $product_info['product_name'] : '',
            'sub_product_name' => isset($sub_product_info['product_name']) ? $sub_product_info['product_name'] : '',
        ];
        // $res['flag'] = $flag;
        $role_map = array_flip($role_map);
        $res['config_list'] = $config_list;
        $res['information'] = $information;
        $res['default_tag'] = isset($role_map[$role])?$role_map[$role]:'';
        $this->response(Enu::RESP_OK,$res);
    }

    /**
     * 获取主体-主产品配置信息
     *
     * @param $group_id
     * @param $product_id
     * @param $role
     *
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-28 13:38:21
     */
    private function g__p_($group_id,$product_id,$role){
        //获取主产品配置项 可被客户修改的配置项 不能被继承的
        $product_config_items = ProductConfigItemRepositorie::getProductConfigItemsCanModify($product_id,$role);
        $product_config_item_ids = array_column($product_config_items, 'id');

        //获取主产品配置 主体配置
        $group_product_configs = ProductConfigRepositorie::getProductGroupConfigList($product_id,$group_id,$product_config_item_ids);
        $group_product_configs = array_column($group_product_configs, null, 'config_item_id');
        //合并配置值,默认值
        foreach($product_config_items as &$item){
            if(key_exists($item['id'], $group_product_configs)){
                $item['is_config'] = true;
                $item['item_value'] = $group_product_configs[$item['id']]['item_value'];
            }else{
                $item['is_config'] = false;
                $item['item_value'] = $item['item_default'];
            }
        }
        return $product_config_items;
    }

    /**
     * 获取主体-子主产品配置信息
     *
     * @param $group_id
     * @param $sub_product_id
     * @param $role
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2023-09-28 14:48:11
     */
    private function g___s($group_id,$sub_product_id,$role){
        //获取子产品配置项
        $sub_product_config_items = SubProductConfigItemRepositorie::getSubProductConfigItemCanModify($sub_product_id);
        $sub_product_config_item_ids = array_column($sub_product_config_items, 'id');
        //获取主产品配置项 可被客户修改的配置项 可以继承
        $sub_product_info = SubProductRepositorie::info($sub_product_id);
        $product_config_items = ProductConfigItemRepositorie::getProductConfigItemsCanModifyCanExtend($sub_product_info['father_id'],$role);
        $product_config_item_ids = array_column($product_config_items, 'id');

        //获取子产品配置值 继承自主产品
        $group_sub_product_configs = SubProductConfigRepositorie::getSubProductGroupConfigList($group_id,$sub_product_id,$product_config_item_ids);
        $group_sub_product_configs = array_column($group_sub_product_configs, null, 'config_item_id');

        //获取子产品配置值 子产品自身
        $group_sub_product_configs_sub = SubProductConfigRepositorie::getSubProductGroupConfigList($group_id,$sub_product_id,$sub_product_config_item_ids);
        $group_sub_product_configs_sub = array_column($group_sub_product_configs_sub,null, 'config_item_id');

        //合并配置值 子产品继承主产品配置值 覆盖 主产品配置值
        foreach($product_config_items as &$item){
            if(key_exists($item['id'], $group_sub_product_configs)){
                $item['is_config'] = true;
                $item['item_value'] = $group_sub_product_configs[$item['id']]['item_value'];
            }else{
                $item['is_config'] = false;
                $item['item_value'] = $item['item_default'];
            }
            $item['config_item_source'] = Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT;
        }

        //合并配置值 子产品配置值 覆盖 资产配置项默认值
        foreach($sub_product_config_items as &$item){
            if(key_exists($item['id'], $group_sub_product_configs_sub)){
                $item['is_config'] = true;
                $item['item_value'] = $group_sub_product_configs_sub[$item['id']]['item_value'];
            }else{
                $item['is_config'] = false;
                $item['item_value'] = $item['item_default'];
            }
            $item['config_item_source'] = Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_SUB;
        }

        return array_merge($product_config_items,$sub_product_config_items);
        // return $sub_product_config_items;
    }


    /**
     * 获取客户-主产品配置
     *
     * @param $customer_id
     * @param $product_id
     * @param $role
     *
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-28 14:53:45
     */
    private function _c_p_($customer_id,$product_id,$role){
        //获取主产品配置项 可被客户修改的配置项 不能被继承的
        $product_config_items = ProductConfigItemRepositorie::getProductConfigItemsCanModify($product_id,$role);
        $product_config_item_ids = array_column($product_config_items, 'id');

        //获取主产品配置 主体配置
        $group_product_configs = ProductConfigRepositorie::getProductCustomerConfigList($product_id,$customer_id,$product_config_item_ids);
        $group_product_configs = array_column($group_product_configs, null, 'config_item_id');

        //合并配置值,默认值
        foreach($product_config_items as &$item){
            if(key_exists($item['id'], $group_product_configs)){
                $item['is_config'] = true;
                $item['item_value'] = $group_product_configs[$item['id']]['item_value'];
            }else{
                $item['is_config'] = false;
                $item['item_value'] = $item['item_default'];
            }
        }
        return $product_config_items;
    }

    /**
     * 获取客户-子产品配置
     *
     * @param $customer_id
     * @param $sub_product_id
     * @param $role
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2023-09-28 15:00:09
     */
    private function _c__s($customer_id,$sub_product_id,$role){
        //获取子产品配置项
        $sub_product_config_items = SubProductConfigItemRepositorie::getSubProductConfigItemCanModify($sub_product_id);
        $sub_product_config_item_ids = array_column($sub_product_config_items, 'id');
        //获取主产品配置项 可被客户修改的配置项 可以继承
        $sub_product_info = SubProductRepositorie::info($sub_product_id);
        $product_config_items = ProductConfigItemRepositorie::getProductConfigItemsCanModifyCanExtend($sub_product_info['father_id'],$role);
        $product_config_item_ids = array_column($product_config_items, 'id');

        //获取子产品配置值 继承自主产品
        $customer_sub_product_configs = SubProductConfigRepositorie::getSubProductCustomerConfigList($customer_id,$sub_product_id,$product_config_item_ids,Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT);
        $customer_sub_product_configs = array_column($customer_sub_product_configs,null, 'config_item_id');

        //获取子产品配置值 子产品自身
        $customer_sub_product_configs_sub = SubProductConfigRepositorie::getSubProductCustomerConfigList($customer_id,$sub_product_id,$sub_product_config_item_ids,Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_SUB);
        $customer_sub_product_configs_sub = array_column($customer_sub_product_configs_sub, null,'config_item_id');

        //合并配置值 子产品继承主产品配置值 覆盖 主产品配置值
        foreach($product_config_items as &$item){
            if(key_exists($item['id'], $customer_sub_product_configs)){
                $item['is_config'] = true;
                $item['item_value'] = $customer_sub_product_configs[$item['id']]['item_value'];
            }else{
                $item['is_config'] = false;
                $item['item_value'] = $item['item_default'];
            }
            $item['config_item_source'] = Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT;
        }

        //合并配置值 子产品配置值 覆盖 资产配置项默认值
        foreach($sub_product_config_items as &$item){
            if(key_exists($item['id'], $customer_sub_product_configs_sub)){
                $item['is_config'] = true;
                $item['item_value'] = $customer_sub_product_configs_sub[$item['id']]['item_value'];
            }else{
                $item['is_config'] = false;
                $item['item_value'] = $item['item_default'];
            }
            $item['config_item_source'] = Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_SUB;
        }

        return array_merge($sub_product_config_items,$product_config_items);
    }


    /**
     * 获取账户-主产品配置
     *
     * @param $account_id
     * @param $product_id
     * @param $role
     *
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-09-28 15:01:09
     */
    private function __ap_($account_id,$product_id,$role){
        //获取主产品配置项 可被客户修改的配置项 不能被继承的
        $product_config_items = ProductConfigItemRepositorie::getProductConfigItemsCanModify($product_id,$role);
        $product_config_item_ids = array_column($product_config_items, 'id');

        //获取主产品配置 主体配置
        $account_product_configs = ProductConfigRepositorie::getProductAccountConfigList($product_id,$account_id,$product_config_item_ids);
        $account_product_configs = array_column($account_product_configs, null, 'config_item_id');

        //合并配置值,默认值
        foreach($product_config_items as &$item){
            if(key_exists($item['id'], $account_product_configs)){
                $item['is_config'] = true;
                $item['item_value'] = $account_product_configs[$item['id']]['item_value'];
            }else{
                $item['is_config'] = false;
                $item['item_value'] = $item['item_default'];
            }
        }
        return $product_config_items;
    }


    /**
     * 获取账户-子产品配置
     *
     * @param $account_id
     * @param $sub_product_id
     * @param $role
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2023-09-28 15:02:47
     */
    private function __a_s($account_id,$sub_product_id,$role){
        //获取子产品配置项
        $sub_product_config_items = SubProductConfigItemRepositorie::getSubProductConfigItemCanModify($sub_product_id);
        $sub_product_config_item_ids = array_column($sub_product_config_items, 'id');
        //获取主产品配置项 可被客户修改的配置项 可以继承
        $sub_product_info = SubProductRepositorie::info($sub_product_id);
        $product_config_items = ProductConfigItemRepositorie::getProductConfigItemsCanModifyCanExtend($sub_product_info['father_id'],$role);
        $product_config_item_ids = array_column($product_config_items, 'id');

        //获取子产品配置值 继承自主产品
        $account_sub_product_configs = SubProductConfigRepositorie::getSubProductAccountConfigList($account_id,$sub_product_id,$product_config_item_ids);
        $account_sub_product_configs = array_column($account_sub_product_configs, null, 'config_item_id');

        //获取子产品配置值 子产品自身
        $account_sub_product_configs_sub = SubProductConfigRepositorie::getSubProductAccountConfigList($account_id,$sub_product_id,$sub_product_config_item_ids);
        $account_sub_product_configs_sub = array_column($account_sub_product_configs_sub, null, 'config_item_id');

        //合并配置值 子产品继承主产品配置值 覆盖 主产品配置值
        foreach($product_config_items as &$item){
            if(key_exists($item['id'], $account_sub_product_configs)){
                $item['is_config'] = true;
                $item['item_value'] = $account_sub_product_configs[$item['id']]['item_value'];
            }else{
                $item['is_config'] = false;
                $item['item_value'] = $item['item_default'];
            }

            $item['config_item_source'] = Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT;
        }

        //合并配置值 子产品配置值 覆盖 资产配置项默认值
        foreach($sub_product_config_items as &$item){
            if(key_exists($item['id'], $account_sub_product_configs_sub)){
                $item['is_config'] = true;
                $item['item_value'] = $account_sub_product_configs_sub[$item['id']]['item_value'];
            }else{
                $item['is_config'] = false;
                $item['item_value'] = $item['item_default'];
            }
            $item['config_item_source'] = Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_SUB;
        }

        return array_merge($product_config_items,$sub_product_config_items);
        // return $sub_product_config_items;
    }


    /**
     * 编辑,添加客户-产品-配置
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-10-09 19:50:55
     *
     */
    public function edit(){
        $product_id         = intval($this->request->get('product_id'));                           //产品id
        $sub_product_id     = intval($this->request->get('sub_product_id'));                       //子产品id
        $group_id           = $this->request->get('group_id');                                     //主体id
        $customer_id        = $this->request->get('customer_id');                                  //客户id
        $account_id         = $this->request->get('account_id');                                   //账号id
        $config_item_id     = $this->request->get('item_id');                                      //配置项id
        $item_value         = $this->request->get('item_value', 0, false);                         //配置项值
        $config_item_source = $this->request->get('config_item_source', 0, false);                 //配置项来源

        try {
            $res = ConfigLogic::customer_product_config_edit($product_id,$sub_product_id,$group_id,$customer_id,$account_id,$config_item_id,$item_value,$config_item_source,$this->admin);

            if($res > 0){
                $this->response(Enu::RESP_OK, '已提交成功,等待审核!');
            }else{
                $this->response(Enu::RESP_FAILED);
            }
        }catch(Exception $e){
            $this->response($e->getCode());
        }

    }


    /**
     * 删除客户-产品配置
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-11-06 20:25:16
     *
     */
    public function delete(){
        $product_id         = intval($this->request->get('product_id'));                           //产品id
        $sub_product_id     = intval($this->request->get('sub_product_id'));                       //子产品id
        $group_id           = $this->request->get('group_id');                                     //主体id
        $customer_id        = $this->request->get('customer_id');                                  //客户id
        $account_id         = $this->request->get('account_id');                                   //账号id
        $config_item_id     = $this->request->get('item_id');                                      //配置项id
        $config_item_source = $this->request->get('config_item_source', 0, false); //配置项来源

        $pic = 0;
        if(!empty($sub_product_id)){
            $product_id = 0;
            $pic = 1;
        }
        if(!empty($product_id)){
            $sub_product_id = 0;
            $pic = 1;
        }

        if($pic != 1){
            $this->response(Enu::RESP_CUSTOMER_PRODUCT_CONFIG_MISSING_PRODUCT_ID_OR_SUB_PRODUCT_ID);
        }

        $cc = 0;
        if(!empty($group_id)){
            $cc ++;
        }
        if(!empty($customer_id)){
            $cc ++;
        }
        if(!empty($account_id)){
            $cc ++;
        }
        if($cc != 1){
            $this->response(Enu::RESP_CUSTOMER_PRODUCT_CONFIG_MISSING_GROUP_ID_OR_CUSTOMER_ID_OR_ACCOUNT_ID);
        }

        if(empty($config_item_id)){
            $this->response(Enu::RESP_CUSTOMER_PRODUCT_CONFIG_MISSING_ITEM_ID);
        }

        if($product_id > 0){
            $config_item_source = Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT;
        }


        if(!in_array($config_item_source,[Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_SUB,Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT])){
            $this->response(Enu::RESP_CUSTOMER_PRODUCT_CONFIG_WORNG_SOURCE_VALUE);
        }

        if(!empty($product_id)){
            $config_info = ProductConfigRepositorie::infoWithGCA($product_id, $group_id, $customer_id, $account_id, $config_item_id);
        }else{
            $config_info = SubProductConfigRepositorie::infoWithGCA($sub_product_id, $group_id, $customer_id, $account_id, $config_item_id, $config_item_source);
        }

        $data = [
            'approval_id'        => create_uuid('CA'),
            'type'               => Enu::APPROVAL_TYPE_CUSTOMER_PRODUCT_CONFIG,
            'action'             => Enu::APPROVAL_ACTION_DELETE,
            'group_id'           => $group_id,
            'customer_id'        => $customer_id,
            'account_id'         => $account_id,
            'product_id'         => $product_id,
            'sub_product_id'     => $sub_product_id,
            'config_item_id'     => $config_item_id,
            'config_item_source' => $config_item_source,
            'value'              => $config_info['item_value'],
            'new_value'          => '',
            'status'             => Enu::APPROVAL_STATUS_NEED_APPROVAL,
            'applicant'          => $this->admin,
        ];
        try {
            $res = ApprovalLogic::add($data);
        }catch(Exception $e){
            $this->response($e->getCode());
        }

        if($res > 0){
            $this->response(Enu::RESP_OK, '已提交成功,等待审核!');
        }else{
            $this->response(Enu::RESP_FAILED);
        }
    }
}