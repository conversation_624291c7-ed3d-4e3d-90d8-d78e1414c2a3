<?php

namespace App\Controllers\Config;

use App\Controllers\BaseController;
use App\Logics\Approval\ApprovalLogic;
use App\Logics\Config\SearchLogic;
use App\Logics\Customer\CompanyTypeLogic;
use App\Models\Product\AccountProductModel;
use App\Models\Product\ProductModel;
use App\Models\System\SystemUserModel;
use App\Repositories\Customer\AccountRepositorie;
use App\Repositories\Customer\CustomerGroupRepositorie;
use App\Repositories\Customer\CustomerRepositorie;
use App\Repositories\Product\ProductConfigItemRepositorie;
use App\Repositories\Product\ProductConfigRepositorie;
use App\Repositories\Product\ProductRepositorie;
use App\Repositories\Product\SubProductConfigItemRepositorie;
use App\Repositories\Product\SubProductConfigRepositorie;
use App\Repositories\Product\SubProductRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

/**
 * 主产品
 */
class ConfigController extends BaseController
{
    /**
     * 检查配置是否存在,是否在审核中
     * 废弃
     *
     * @throws Exception
     * @deprecated
     */
    // public function check(){
    //     $product_id         = intval($this->request->get('product_id'));                           //产品id
    //     $sub_product_id     = intval($this->request->get('sub_product_id'));                       //子产品id
    //     $action             = $this->request->get('action');                                       //操作类型
    //     $type               = $this->request->get('type');                                         //操作模块
    //     $group_id           = $this->request->get('group_id');                                     //主体id
    //     $customer_id        = $this->request->get('customer_id');                                  //客户id
    //     $account_id         = $this->request->get('account_id');                                   //账号id
    //     $config_item_id     = $this->request->get('item_id');                                      //配置项id
    //     $config_item_source = $this->request->get('config_item_source', 0, false); //配置项来源
    //
    //     $cs = Enu::CONFIG_STATUS_NONE;
    //     try {
    //         $cs = ApprovalLogic::check($type,$action,$group_id,$customer_id,$account_id,$product_id,$sub_product_id,$config_item_id,$config_item_source);
    //     } catch (Exception $e) {
    //         $this->response($e->getCode(),$e->getMessage());
    //     }
    //
    //     $config_status_list = config('options.config');
    //     $this->response(Enu::RESP_OK,['status' => $cs,'name' => $config_status_list[$cs]]);
    // }
}