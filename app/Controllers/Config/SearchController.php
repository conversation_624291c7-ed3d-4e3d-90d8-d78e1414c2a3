<?php

namespace App\Controllers\Config;

use App\Controllers\BaseController;
use App\Logics\Config\SearchLogic;
use App\Logics\Customer\CompanyTypeLogic;
use App\Models\Product\AccountProductModel;
use App\Models\Product\ProductModel;
use App\Models\System\SystemUserModel;
use App\Repositories\Customer\AccountRepositorie;
use App\Repositories\Customer\CustomerGroupRepositorie;
use App\Repositories\Customer\CustomerRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

/**
 * 主产品
 */
class SearchController extends BaseController
{
    /**
     * @var array 子产品配置项列表
     */
    private $sub_product_config_item_list;
    /**
     * @var array 主产品配置项列表
     */
    private $product_config_item_list;

    /**
     * 客户配置 列表
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-27 20:06:35
     *
     */
    public function searchList(){
        $page      = intval($this->request->get('page', 1));
        $page_size = intval($this->request->get('page_size', 10));

        $group_id    = $this->request->get('group_id');               //主体id
        $customer_id = $this->request->get('customer_id');            //客户id
        $account_id  = $this->request->get('account_id');             //账号id

        $product_id     = intval($this->request->get('product_id'));     //产品id
        $sub_product_id = intval($this->request->get('sub_product_id')); //子产品id

        //1 列表数据
        //2.1 相关配置 账号-主产品 账号-子产品
        //2.2 获取配置项
        //3 配置数量

        $res = [
            'page'      => $page,
            'page_size' => $page_size,
            'count'     => 0,
            'list'      => [],
        ];
        //1 获取列表
        $customer_id_arr = [];
        if(!empty($group_id) || !empty($customer_id) || !empty($account_id)){
            $customer_id_arr = $this->get_customer_ids($group_id,$customer_id,$account_id);
            if(empty($customer_id_arr)){
                //返回空
                $this->response(Enu::RESP_OK,$res);
            }
        }

        $where = [];
        if(!empty($customer_id_arr)) {
            $where[] = ['customer_id', 'in', $customer_id_arr];
        }
        $custoemr_list  = CustomerRepositorie::getCustomerList($where,['*'],$page_size,$page,['group_id','desc, id desc']);
        $custoemr_count = CustomerRepositorie::getCustomerCount($where);
        $res['count'] = $custoemr_count;

        $group_ids = array_column($custoemr_list, 'group_id');
        $group_ids = array_unique(array_filter($group_ids, function($value){ return '' !== $value; }));

        $group_list = CustomerGroupRepositorie::getCustomerGroupListByGrouupIds($group_ids);
        $group_list = array_column($group_list, null, 'group_id');

        $customer_ids = array_column($custoemr_list, 'customer_id');
        $user_names = array_column($custoemr_list, 'salesman');

        $user_name_map = SystemUserModel::getUserNameMaps($user_names);

        $company_type_map = CompanyTypeLogic::getCompanyTypeMap();

        //1.2 获取客户的账号
        $_account_list = AccountRepositorie::getListWithCustomers($customer_ids);
        $account_names = [];
        $account_list = [];
        $account_ids = [];
        foreach($_account_list as $account_info){
            if(!empty($account_id) && $account_id != $account_info['account_id']){
                continue;
            }
            $_customer_id = $account_info['customer_id'];
            $account_ids[] = $account_info['account_id'];
            $account_names[$account_info['account_id']] = $account_info['account_name'];
            if(!key_exists($_customer_id, $account_list)){
                $account_list[$_customer_id] = [];
            }
            $account_list[$_customer_id][] = $account_info;
        }

        //1.3 获取账号关联的产品 子产品
        $ap_father_id = $product_id;
        $ap_sub_product_ids = ProductModel::getSubProductsByFatherId($ap_father_id);
        $ap_sub_product_ids = array_column($ap_sub_product_ids,'product_id');
        $ap_sub_product_ids[] = $ap_father_id;
        $_account_product_list = AccountProductModel::getListWithAccountsAndProduts($account_ids,$ap_sub_product_ids);
        $product_ids = [];
        $account_product_list = [];
        foreach($_account_product_list as $ap_info){
            $product_ids[] = $ap_info['product_id'];
            $_account_id = $ap_info['account_id'];
            if(!key_exists($_account_id, $account_product_list)){
                $account_product_list[$_account_id] = [];
            }
            $account_product_list[$_account_id][] = $ap_info;
        }
        //1.5 子产品信息
        $sub_product_list = ProductModel::getListByFatherids($product_ids);
        $father_ids = array_filter(array_column($sub_product_list, 'father_id'));
        $product_ids = array_merge($product_ids,$father_ids);
        $product_ids = array_unique($product_ids);
        $sub_product_list = array_column($sub_product_list, null, 'product_id');
        $sub_product_ids = array_keys($sub_product_list);

        $res_list = [];
        foreach ($custoemr_list as $c_info){
            $customer_id = $c_info['customer_id'];
            $_info = [
                'group_id'          => $c_info['group_id'],
                'group_name'        => empty($c_info['group_id']) ? '' : $group_list[$c_info['group_id']]['group_name'],
                'customer_id'       => $c_info['customer_id'],
                'customer_name'     => $c_info['name'],
                'company_name'      => $c_info['company'],
                'company_type_name' => isset($company_type_map[$c_info['type']])?$company_type_map[$c_info['type']]:'其它--其它',
                'salesman_name'     => isset($user_name_map[$c_info['salesman']])?$user_name_map[$c_info['salesman']]:'',
            ];
            if(!key_exists($customer_id,$account_list)){
                $_info['account_id'] = '';
                $_info['account_name'] = '';
                $_info['product_list'] = [];
                $res_list[] = $_info;
            }else{
                foreach($account_list[$customer_id] as $account_info){//客户
                    $account_id = $account_info['account_id'];
                    $_info['account_id']   = $account_id;
                    $_info['account_name'] = $account_names[$account_id];
                    $res_list[] = $_info;
                }
            }
        }

        list($product_config_item_list,$sub_product_config_item_list,$product_item_ids,$sub_product_item_ids) = SearchLogic::get_config_items($product_ids,$sub_product_ids);

        $config_arr = SearchLogic::get_configs($group_ids,$customer_ids,$account_ids,'',[],$product_item_ids,$sub_product_item_ids,'',$product_config_item_list,$sub_product_config_item_list);
        $group_config_count    = $config_arr['group_config_count'];
        $customer_config_count = $config_arr['customer_config_count'];
        $account_config_count  = $config_arr['account_config_count'];

        usort($res_list, function($a,$b){
            $cmp = strcmp($b['group_id'], $a['group_id']);
            if ($cmp == 0) {
                $_cmp = strcmp($a['customer_id'], $b['customer_id']);
                if ($_cmp == 0) {
                    return strcmp($a['account_id'], $b['account_id']);
                }
                return $_cmp;
            }
            return $cmp;
        });

        //主体合并
        $group_count = [];
        //客户合并
        $customer_count = [];
        //账号
        $account_count = [];
        foreach($res_list as $rl_info){
            if(!empty($rl_info['group_id'])) {
                $group_count[$rl_info['group_id']] = isset($group_count[$rl_info['group_id']]) ? $group_count[$rl_info['group_id']] + 1 : 1;
            }
            $customer_count[$rl_info['customer_id']] = isset($customer_count[$rl_info['customer_id']]) ? $customer_count[$rl_info['customer_id']] + 1 : 1;
            if(!empty($rl_info['account_id'])) {
                $account_count[$rl_info['account_id']] = isset($account_count[$rl_info['account_id']]) ? $account_count[$rl_info['account_id']] + 1 : 1;
            }
        }
        foreach($res_list as &$rl_info){

            $rl_info['customer_merge_count'] = $customer_count[$rl_info['customer_id']];
            if ($customer_count[$rl_info['customer_id']] > 0) {
                $customer_count[$rl_info['customer_id']] = 0;
            }

            if(empty($rl_info['account_id'])) {
                $rl_info['account_merge_count'] = 1;
            }else {
                $rl_info['account_merge_count'] = $account_count[$rl_info['account_id']];
                if ($account_count[$rl_info['account_id']] > 0) {
                    $account_count[$rl_info['account_id']] = 0;
                }
            }

            $rl_info['product_merge_count'] = 1;

            $rl_info['group_config_count'] = key_exists($rl_info['group_id'],$group_config_count)? $group_config_count[$rl_info['group_id']]:0;
            $rl_info['customer_config_count'] = key_exists($rl_info['customer_id'],$customer_config_count)? $customer_config_count[$rl_info['customer_id']]:0;
            $rl_info['account_config_count']  = key_exists($rl_info['account_id'],$account_config_count)? $account_config_count[$rl_info['account_id']]:0;

            if (!empty($rl_info['group_id'])) {
                $rl_info['group_merge_count'] = $group_count[$rl_info['group_id']];
                if ($group_count[$rl_info['group_id']] > 0) {
                    $group_count[$rl_info['group_id']] = 0;
                }
            }else{
                $rl_info['group_name'] = '--';
                $rl_info['group_merge_count'] = $rl_info['customer_merge_count'];
            }
        }

        $res['list'] = $res_list;
        $this->response(Enu::RESP_OK,$res);
    }

    /**
     * 配置详情
     *
     * @throws Exception
     */
    public function detailList()
    {
        $detailType = $this->request->get('detailType', 'group');//数据维度，默认为主体
        if (!$detailType) {
            $this->response(Enu::RESP_SEARCH_PARAMS_EMPTY);
        }
        $params = [
            'page' => intval($this->request->get('page', 1)),
            'pageSize' => intval($this->request->get('page_size', 20)),
            'fpid' => intval($this->request->get('fpid', '0')),
            'pid' => intval($this->request->get('pid', '0')),
            'detailType' => $detailType,
            'detailId' => $this->request->get('detailId'),
        ];

        $logicRes = SearchLogic::detailList($params);

        $this->response($logicRes['code'], $logicRes['data']);
    }


    /**
     * 根据条件获取客户id
     *
     * @param $group_id
     * @param $customer_id
     * @param $account_id
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2023-09-27 20:11:35
     */
    private function get_customer_ids($group_id,$customer_id,$account_id){
        $customer_id_arr = [];
        if(!empty($customer_id) && empty($account_id)){
            return [$customer_id];
        }

        if(!empty($account_id)){
            $account_list = AccountRepositorie::getListByAccountId($account_id);
            $customer_id_arr = array_column($account_list, 'customer_id');
            if(!empty($customer_id) && !in_array($customer_id,$customer_id_arr)){
                return [];
            }
        }

        if(!empty($group_id)) {
            $group_list = CustomerRepositorie::getCustomerWithGroup([$group_id]);
            $_customer_ids = array_column($group_list, 'customer_id');
            $customer_id_arr = empty($customer_id_arr) ? $_customer_ids : array_intersect($customer_id_arr,$_customer_ids);
            if(!empty($customer_id) && !in_array($customer_id,$customer_id_arr)){
                return [];
            }
            if(empty($customer_id_arr)){
                return [];
            }
        }

        return $customer_id_arr;
    }


    /**
     * 客户特殊配置列表
     * 仅展示有配置的客户
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-10-31 18:20:47
     */
    public function configList(){
        $group_id    = $this->request->get('group_id');               //主体id
        $customer_id = $this->request->get('customer_id');            //客户id
        $account_id  = $this->request->get('account_id');             //账号id

        $item_name   = $this->request->get('item_name');                   //配置项名称
        $config_path = $this->request->get('path');                        //配置路径

        $product_id     = intval($this->request->get('product_id'));     //产品id
        $sub_product_id = intval($this->request->get('sub_product_id')); //子产品id

        $page      = intval($this->request->get('page', 1));
        $page_size = intval($this->request->get('page_size', 10));

        $res = SearchLogic::get_have_config_customer_list($group_id,$customer_id,$account_id,$product_id,$sub_product_id,$item_name,$config_path,$page,$page_size);

        $this->response(Enu::RESP_OK,$res);
    }
}