<?php

namespace App\Controllers\Contract;

use App\Controllers\BaseController;
use App\Logics\Contract\ContractLogic;
use App\Utils\Helpers\Enu;
use Exception;

/**
 * 合同
 */
class ContractController extends BaseController
{

    /**
     * @return void
     * @throws Exception
     * <AUTHOR> 2024-02-26 15:10:14
     *
     */
    public function options(){
        $res = ContractLogic::options();
        $this->response(Enu::RESP_OK,$res);
    }


    /**
     * 获取合同列表
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2024-02-21 18:09:31
     *
     */
    public function contractList(){
        $contract_no       = $this->request->get('contract_no');                //合同编号
        $contract_category = $this->request->get('contract_category');          //合同类别
        $contract_type     = $this->request->get('contract_type');              //合同类型
        $contract_start    = $this->request->get('contract_start');             //合同开始日期
        $contract_end      = $this->request->get('contract_end');               //合同结束日期
        $created_at_start  = $this->request->get('created_at_start');           //归档日期 开始
        $created_at_end    = $this->request->get('created_at_end');             //归档日期 结束
        $company_name      = $this->request->get('company_name');               //公司名称

        $page      = intval($this->request->get('page', 1));
        $page_size = intval($this->request->get('page_size', 10));


        $params = [
            'contract_no'       => $contract_no,
            'contract_category' => $contract_category,
            'contract_type'     => $contract_type,
            'contract_start'    => $contract_start,
            'contract_end'      => $contract_end,
            'created_at_start'  => $created_at_start,
            'created_at_end'    => $created_at_end,
            'company_name'      => $company_name,
            'page'              => $page,
            'page_size'         => $page_size,
        ];

        $list = ContractLogic::get_list($params);
        $count = ContractLogic::get_count($params);

        $res = [
            'list'                  => $list,
            'count'                 => $count,
            'page'                  => $page,
            'page_size'             => $page_size,
        ];

        $this->response(Enu::RESP_OK, $res);
    }

    /**
     * 添加合同
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2024-02-29 17:35:27
     *
     */
    public function add(){
        $contract_category = $this->request->get('category');                //合同类别
        $excel             = $this->request->get('excel');                   //excel文件
        $pdf               = $this->request->get('pdf');                     //pdf列表

        try {
            $res = ContractLogic::add($contract_category,$excel,$pdf,$this->admin);
            $this->response(Enu::RESP_OK, $res);
        }catch(Exception $e){
            $this->response($e->getCode(),$e->getMessage());
        }
    }

    /**
     * 删除合同
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2024-02-21 18:32:54
     */
    public function del(){
        $contract_no = $this->request->get('contract_no'); //合同编号

        try {
            $res = ContractLogic::del($contract_no,$this->admin);
            $this->response(Enu::RESP_OK, $res);
        }catch(Exception $e){
            $this->response($e->getCode(),$e->getMessage());
        }
    }


    /**
     * 获取公司列表
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2024-02-21 18:33:29
     */
    public function companyList(){
        $company_name = $this->request->get('company_name'); //合同编号

        try {
            $res = ContractLogic::company_list($company_name);
            $this->response(Enu::RESP_OK, $res);
        }catch(Exception $e){
            $this->response($e->getCode(),$e->getMessage());
        }
    }
}