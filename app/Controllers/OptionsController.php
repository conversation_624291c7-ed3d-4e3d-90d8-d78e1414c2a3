<?php

namespace App\Controllers;

use App\Logics\Customer\CustomerLogic;
use App\Logics\Product\ProductLogic;
use App\Repositories\OptionsRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

class OptionsController extends BaseController
{

    /**
     * 产品级联下拉列表
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-10-16 11:39:39
     *
     */
    public function products(){
        // $res = ProductLogic::options();//主产品 子产品 级联
        $product_id = $this->request->get('product_id');
        $res = OptionsRepositorie::product_options($product_id);
        $this->response(Enu::RESP_OK, $res);
    }


    /**
     * 主产品列表
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-10-24 14:54:30
     *
     */
    public function mainProductList(){
        $res = ProductLogic::get_main_product_list();
        $this->response(Enu::RESP_OK, $res);
    }


    /**
     * 获取 客户-账号 级联列表
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-11-06 17:07:36
     *
     */
    public function customerAccountList(){
        $group_id = $this->request->get('group_id');
        $res = CustomerLogic::customer_account_list($group_id);
        $this->response(Enu::RESP_OK, $res);
    }

    //获取客户列表
    public function customerList(){
        $group_id = $this->request->get('group_id', '');
        $res = CustomerLogic::customerList($group_id);
        $this->response(Enu::RESP_OK, $res);
    }

    //获取账号列表
    public function accountList(){
        $customer_id = $this->request->get('customer_id', '');
        $res = CustomerLogic::accountList($customer_id);
        $this->response(Enu::RESP_OK, $res);
    }

    public function userInfo(){
        $group_id = $this->request->get('group_id');
        $res = CustomerLogic::customer_account_list($group_id);
        $this->response(Enu::RESP_OK, $res);
    }
}