<?php

namespace App\Controllers\Customer;

use App\Controllers\BaseController;
use App\Repositories\Customer\CompanyTypeRepositorie;
use App\Repositories\Customer\CustomerGroupRepositorie;
use App\Repositories\Customer\CustomerRepositorie;
use App\Repositories\Customer\CustomerSalesmanHistoryRepositorie;
use App\Repositories\System\UserRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

/**
 * 主体
 */
class CustomerGroupController extends BaseController
{

    /**
     * 添加主体
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-19 00:28:00
     *
     */
    public function addGroup() {
        $group_name = $this->request->get('group_name');
        if (empty($group_name)) {
            $this->response(Enu::RESP_GROUP_MISSING_GROUP_NAME);//请传入主体名称
        }

        $count = CustomerGroupRepositorie::getCustomerGroupCountByName($group_name);
        if($count > 0){
            $this->response(Enu::RESP_GROUP_DUPLICATION_GROUP_NAME);//主体名称重复
        }

        $group_id = create_uuid('GR');
        CustomerGroupRepositorie::add($group_id, $group_name,$this->admin);
        $this->response(Enu::RESP_OK);
    }

    /**
     * 编辑主体
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-19 00:31:07
     *
     */
    public function edigGroup() {
        $group_id   = $this->request->get('group_id');
        $group_name = $this->request->get('group_name');
        if (empty($group_id)) {
            $this->response(Enu::RESP_GROUP_MISSING_GROUP_ID);//请传入主体id
        }
        if (empty($group_name)) {
            $this->response(Enu::RESP_GROUP_MISSING_GROUP_NAME);//请传入主体名称
        }
        $count = CustomerGroupRepositorie::getCustomerGroupCountByName($group_name,$group_id);
        if($count > 0){
            $this->response(Enu::RESP_GROUP_DUPLICATION_GROUP_NAME);//主体名称重复
        }

        CustomerGroupRepositorie::edit($group_id, $group_name,$this->admin);
        $this->response(Enu::RESP_OK);
    }


    /**
     * 获取主体列表
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-18 18:16:22
     */
    public function getGroupList() {
        $page      = intval($this->request->get('page', 1));
        $page_size = intval($this->request->get('page_size', 20));
        $group_id  = $this->request->get('group_id');

        $where = [];
        if (!empty($group_id)) {
            $where[] = ['group_id', '=', $group_id];
        }

        $_list  = CustomerGroupRepositorie::getCustomerGroupList($where, ['*'], $page_size, $page);
        $count = CustomerGroupRepositorie::getCustomerGroupCount($where);

        //获取主体包含的客户
        $group_id_arr = array_column($_list, 'group_id');
        $customer_list = CustomerRepositorie::getCustomerWithGroup($group_id_arr,['customer_id','group_id','name']);

        $group_customer_list = [];
        foreach($customer_list as $customer_info){
            $_group_id = $customer_info['group_id'];
            if(!key_exists($_group_id, $group_customer_list)){
                $group_customer_list[$_group_id] = [];
            }
            $group_customer_list[$_group_id][] = [
                'customer_id' => $customer_info['customer_id'],
                'customer_name' => $customer_info['name'],
            ];
        }

        $list = [];
        foreach($_list as $info){
            $_group_id = $info['group_id'];
            unset($info['id']);
            $info['status'] = intval($info['status']);
            $_info = $info;
            if(!key_exists($_group_id, $group_customer_list)){
                $_info['customer_id']    = '';
                $_info['customer_name']  = '';
                $_info['customer_count'] = 1;
                $list[] = $_info;
            }else{
                $customer_count = count($group_customer_list[$_group_id]);

                foreach($group_customer_list[$_group_id] as $customer_info){
                    $_info['customer_id']    = $customer_info['customer_id'];
                    $_info['customer_name']  = $customer_info['customer_name'];
                    $_info['customer_count'] = $customer_count;
                    if($customer_count > 0){
                        $customer_count = 0;
                    }
                    $list[] = $_info;
                }
            }
        }

        $res = [
            'list'      => $list,
            'count'     => $count,
            'page'      => $page,
            'page_size' => $page_size,
        ];

        $this->response(Enu::RESP_OK, $res);
    }

    public function info(){
        $group_id  = $this->request->get('group_id');
        if (empty($group_id)) {
            $this->response(Enu::RESP_GROUP_MISSING_GROUP_ID);//请传入主体id
        }
        $info  = CustomerGroupRepositorie::getCustomerGroupInfo($group_id);
        $this->response(Enu::RESP_OK, $info);
    }

    /**
     * 主体列表下来菜单数据
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-18 19:19:40
     */
    public function getGroupOption() {
        $list    = CustomerGroupRepositorie::getAllCustomerGroup();
        $options = [];
        foreach ($list as $info) {
            $item = [
                "group_id"   => $info['group_id'],
                "group_name" => $info['group_name'],
            ];
            $options[] = $item;
        }
        $status_options = [];
        $_status_options = config('options.group.status');
        foreach($_status_options as $value => $label){
            $status_options[] = [
                'value' => $value,
                'label' => $label
            ];
        }
        $res = [
            'group_options' => $options,
            'status_options' => $status_options,
        ];
        $this->response(Enu::RESP_OK, $res);
    }


    /**
     * 主体关联客户
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-18 19:27:38
     */
    public function link() {
        $group_id    = $this->request->get('group_id');
        $customer_id = $this->request->get('customer_id');

        if (empty($group_id)) {
            $this->response(Enu::RESP_GROUP_MISSING_GROUP_ID);//请传入主体id
        }
        if (empty($customer_id)) {
            $this->response(Enu::RESP_GROUP_MISSING_CUSTOMER_ID);//请传入关联客户id
        }

        $customer_info = CustomerRepositorie::getCustomerInfo($customer_id);
        if ($customer_info['status'] == Enu::CUSTOMER_STATUS_DISENABLE) {
            $this->response(Enu::RESP_GROUP_WORNG_CUSTOMER);//客户状态错误
        }
        if (!empty($customer_info['group_id'])) {
            $this->response(Enu::RESP_GROUP_CUSTOMER_BIND);//客户已经关联主体
        }

        $data = ['group_id' => $group_id, 'updated_at' => date(Enu::TIME_FORMAT)];
        CustomerRepositorie::editCustomer($customer_id, $data);

        $this->response(Enu::RESP_OK);
    }


    /**
     * 获取没有关联主体的客户
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-18 19:38:38
     *
     */
    public function getAllUnlinkCustomer() {
        $list = CustomerRepositorie::getCustomerWithoutGroup(['customer_id', 'name']);

        $customer_options = [];
        foreach ($list as $customer_info) {
            $item               = [
                'customer_id' => $customer_info['customer_id'],
                'name'        => $customer_info['name'],
            ];
            $customer_options[] = $item;
        }
        $this->response(Enu::RESP_OK, $customer_options);
    }


    /**
     * 获取主体下的客户
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-18 19:44:12
     *
     */
    public function getGroupCutomerList() {
        $group_id = $this->request->get('group_id');

        if (empty($group_id)) {
            $this->response(Enu::RESP_GROUP_MISSING_GROUP_ID);//请传入主体id
        }
        $list            = CustomerRepositorie::getGroupCustomers($group_id, ['customer_id', 'name']);
        $group_customers = [];
        foreach ($list as $customer_info) {
            $item              = [
                'customer_id' => $customer_info['customer_id'],
                'name'        => $customer_info['name'],
            ];
            $group_customers[] = $item;
        }
        $this->response(Enu::RESP_OK, $group_customers);
    }
}