<?php

namespace App\Controllers\Customer;

use App\Controllers\BaseController;
use App\Repositories\Customer\CompanyTypeRepositorie;
use App\Repositories\Customer\CustomerGroupRepositorie;
use App\Repositories\Customer\CustomerRepositorie;
use App\Repositories\Customer\CustomerSalesmanHistoryRepositorie;
use App\Repositories\System\UserRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

/**
 * 客户
 */
class CustomerController extends BaseController
{

    private $intval_fields = ['id','type','status','contract_status','payment_type','credit','email_type','customer_type','channel_mode','reconciliation_cycle','sign_type'];


    /**
     * 客户列表筛选用下拉列表数据
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-18 18:29:42
     *
     */
    public function customerOptions(){
        $this->customer_options();
    }


    /**
     * 添加客户 编辑客户 下拉列表数据
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-18 18:42:23
     *
     */
    public function customerModifyOptions(){
        $this->customer_options(false);
    }

    /**
     * 客户列表筛选用下拉列表数据
     *
     * @return void
     * @throws Exception
     */
    private function customer_options($is_list_options = true) {
        $options_list = [];
        if($is_list_options) {
            $list      = CustomerRepositorie::getAllCustomer(['customer_id', 'name', 'company', 'salesman', 'operator']);
            $customers = [];
            $company   = [];
            $salesmans = [];
            $operators = [];
            foreach ($list as $info) {
                $customers[] = [
                    'customer_id' => $info['customer_id'],
                    'name'        => $info['name'],
                ];
                $company[]   = $info['company'];
                if (!key_exists($info['salesman'], $salesmans)) {
                    $salesmans[$info['salesman']] = $info['salesman'];
                }
                if (!key_exists($info['operator'], $operators)) {
                    $operators[$info['operator']] = $info['operator'];
                }
            }
            $username_arr = array_merge($salesmans, $operators);
            $user_list    = UserRepositorie::getUserList($username_arr);
            $user_list    = array_column($user_list, 'realname', 'username');
            $operator_options = [];
            foreach($operators as $info){
                $operator_options[] = [
                    'operator' => $info,
                    'realname'  => $user_list[$info],
                ];
            }
            $salesman_options = [];
            foreach($salesmans as $item){
                $salesman_options[] = [
                    'salesman' => $item,
                    'realname'  => $user_list[$item],
                ];
            }
            $options_list['customers']     = $customers;
            $options_list['company']       = $company;
            $options_list['salesmans']     = $salesman_options;
            $options_list['operators']     = $operator_options;
        }else{
            $user_list    = UserRepositorie::getAllUser();
            $user_options = [];
            foreach($user_list as $item){
                $user_options[] = [
                    'username' => $item['username'],
                    'realname' => $item['realname'],
                ];
            }
            $options_list['user'] = $user_options;
        }

        $dhb_sign_corp_options = config('options.dhb_sign_corp');


        $_customer_options = config('options.customer');
        $_customer_options['dhb_sign_corp_options'] = $dhb_sign_corp_options;
        $customer_options = [];
        //整理为前端使用的结构
        foreach($_customer_options as $field => $options){
            foreach($options as $value => $label){
                $customer_options[$field][] = [
                    'value' => $value,
                    'label' => $label
                ];
            }
        }
        $options_list = array_merge($options_list,$customer_options);

        //公司类型
        $company_type_list = CompanyTypeRepositorie::getCompanyType();
        $options_list['company_type']  = $company_type_list;

        $this->response(Enu::RESP_OK, $options_list);
    }

    /**
     * 客户列表
     * @api
     * @throws Exception
     */
    public function customerList() {
        $page      = intval($this->request->get('page', 1));
        $page_size = intval($this->request->get('page_size', 20));

        //处理参数
        $where = [];
        $skip_para = ['page','page_size','s'];//这些参数不做条件
        foreach($this->request->keys() as $para_name){
            if(in_array($para_name, $skip_para)){
                continue;
            }

            $para_val = $this->request->get($para_name);
            //值为空不需要处理 跳过
            if(empty($para_val)){
                continue;
            }

            //签约状态是多选,使用in
            if($para_name == 'contract_status'){
                $where[] = [$para_name,'in',$para_val];
                continue;
            }

            //客户名在参数和表中名称不一致
            if($para_name == 'customer_name'){
                $para_name = 'name';
            }
            //大多数参数使用等号
            $where[] = [$para_name,'=',$para_val];
        }
        $list = CustomerRepositorie::getCustomerList($where,['*'],$page_size,$page);
        $count = CustomerRepositorie::getCustomerCount($where);

        //公司类型名称
        $company_names = CompanyTypeRepositorie::getCompanyTypeName();

        $username_arr = [];
        $group_ids = [];
        foreach ($list as $info){
            if(!key_exists($info['operator'], $username_arr)){
                $username_arr[$info['operator']] = $info['operator'];
            }
            if(!key_exists($info['salesman'], $username_arr)){
                $username_arr[$info['salesman']] = $info['salesman'];
            }
            if(!key_exists($info['admin'], $username_arr)){
                $username_arr[$info['admin']] = $info['admin'];
            }
            if(!key_exists($info['group_id'], $group_ids)){
                $group_ids[$info['group_id']] = $info['group_id'];
            }
        }

        //获取主体名称
        // $group_ids   = array_unique(array_column($list, 'group_id'));
        $group_names = CustomerGroupRepositorie::getAllCustomerGroup([['group_id', 'in', $group_ids]]);
        $group_names = array_column($group_names, 'group_name','group_id');


        //获取后台用户名
        $user_list = UserRepositorie::getUserList($username_arr);
        $user_list = array_column($user_list, 'realname','username');

        $customer_options = config('options.customer');
        $dhb_sign_corp_options = config('options.dhb_sign_corp');

        foreach($list as &$item){
            $item['type_name']            = key_exists($item['type'], $company_names) ? $company_names[$item['type']]['name'] : '';
            $item['group_name']           = key_exists($item['group_id'], $group_names) ? $group_names[$item['group_id']] : '';
            $item['contract_status_name'] = $customer_options['contract_status'][$item['contract_status']];
            $item['sign_type_name']       = $customer_options['sign_type'][$item['sign_type']];
            $item['status_name']          = $customer_options['status'][$item['status']];
            $item['salesman_name']        = key_exists($item['salesman'], $user_list) ? $user_list[$item['salesman']] : $item['salesman'];
            $item['operator_name']        = key_exists($item['operator'], $user_list) ? $user_list[$item['operator']] : $item['operator'];
            $item['admin_name']           = key_exists($item['admin'], $user_list) ? $user_list[$item['admin']] : $item['admin'];
            $item['dhb_sign_corp']        = $dhb_sign_corp_options[$item['dhb_sign_corp']];
            $item['payment_type_name']    = $customer_options['payment_type'][$item['payment_type']];

            foreach($this->intval_fields as $field){
                $item[$field] = intval($item[$field]);
            }
        }

        $res = [
            'list'      => $list,
            'count'     => $count,
            'page'      => $page,
            'page_size' => $page_size,
        ];

        $this->response(Enu::RESP_OK, $res);
    }


    /**
     * 添加客户
     *
     * @api
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-15 16:00:46
     */
    public function add(){
        $name                 = $this->request->get('name');
        $company              = $this->request->get('company');
        $dhb_sign_corp        = $this->request->get('dhb_sign_corp');
        $status               = $this->request->get('status');
        $type                 = $this->request->get('type');
        $customer_type        = $this->request->get('customer_type', 1);
        $operator             = $this->request->get('operator');
        $salesman             = $this->request->get('salesman');
        $channel_follower     = $this->request->get('channel_follower');                               // 渠道跟进人
        $source_id            = implode(',', $this->request->get('source_id', []));                    // 渠道跟进人可见征信机构
        $channel_mode         = $this->request->get('channel_mode');                                   //客户类型 是否为渠道客户
        $sign_type            = $this->request->get('sign_type', Enu::CUSTOMER_SIGN_TYPE_DHB, false);  //征信客户分类： 0: 电话邦签约, 1: 朴道签约
        // $group_id             = $this->request->get('group_id');                                       //主体id
        $reconciliation_cycle = $this->request->get('reconciliation_cycle');                           //对账方式 对账周期
        $payment_type         = $this->request->get('payment_type');
        $bill_cc_email        = $this->request->get('bill_cc_email');
        $email_type           = $this->request->get('email_type', 1);
        $bill_email           = $this->request->get('bill_email');

        $data = [
            'name'                 => $name,
            'company'              => $company,
            'dhb_sign_corp'        => $dhb_sign_corp,
            'status'               => $status,
            'type'                 => $type,
            'customer_type'        => $customer_type,
            'operator'             => $operator,
            'salesman'             => $salesman,
            'channel_follower'     => $channel_follower,
            'source_id'            => $source_id,
            'channel_mode'         => $channel_mode,
            'sign_type'            => $sign_type,
            // 'group_id'             => $group_id,
            'reconciliation_cycle' => $reconciliation_cycle,
            'bill_email'           => $bill_email,
            'bill_cc_email'        => $bill_cc_email,
            'payment_type'         => $payment_type,
        ];

        $data = $this->check_params($data);

        $name_unique = CustomerRepositorie::getCustomerCount([['name','=',$name]]);
        if ($name_unique > 0) {
            $this->response(Enu::RESP_CUSTOMER_USED);//客户名称已经被占用，请更换客户名称
        }


        //生成customer_id
        $customer_id = create_uuid('C');
        $create_at = $update_at = time();//date(Enu::TIME_FORMAT);

        $data['customer_id']     = $customer_id;
        $data['email_type']      = $email_type;
        $data['admin']           = $this->admin;
        $data['create_at']       = $create_at;
        $data['update_at']       = $update_at;

        CustomerRepositorie::createCustomer($data);

        //添加历史商务记录
        CustomerSalesmanHistoryRepositorie::add($customer_id,$salesman,date(Enu::TIME_FORMAT_YM01));
        $this->response(Enu::RESP_OK);
    }

    /**
     * 获取客户详情
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-18 00:31:34
     */
    public function info(){
        $customer_id = $this->request->get('customer_id');
        if(empty($customer_id)){
            $this->response(Enu::RESP_CUSTOMER_MISSING_ID);
        }
        $info = CustomerRepositorie::getCustomerInfo($customer_id);

        foreach($this->intval_fields as $field){
            $info[$field] = intval($info[$field]);
        }

        $group_info = CustomerGroupRepositorie::getCustomerGroupInfo($info['group_id']);
        $info['group_name'] = $group_info['group_name'];

        //公司类型名称
        $company_names = CompanyTypeRepositorie::getCompanyTypeName();
        $customer_options = config('options.customer');
        // $dhb_sign_corp_options = config('options.dhb_sign_corp');
        //获取后台用户名
        $user_list = UserRepositorie::getUserList([$info['salesman'],$info['operator'],$info['admin']]);
        $user_list = array_column($user_list, 'realname','username');

        $info['type_name']            = key_exists($info['type'], $company_names) ? $company_names[$info['type']]['name'] : '';
        $info['type_ids']             = key_exists($info['type'], $company_names) ? $company_names[$info['type']]['ids'] : '';
        $info['contract_status_name'] = $customer_options['contract_status'][$info['contract_status']];
        $info['sign_type_name']       = $customer_options['sign_type'][$info['sign_type']];
        $info['status_name']          = $customer_options['status'][$info['status']];
        $info['salesman_name']        = key_exists($info['salesman'], $user_list) ? $user_list[$info['salesman']] : $info['salesman'];
        $info['operator_name']        = key_exists($info['operator'], $user_list) ? $user_list[$info['operator']] : $info['operator'];
        $info['admin_name']           = key_exists($info['admin'], $user_list) ? $user_list[$info['admin']] : $info['admin'];
        $info['dhb_sign_corp']        = $info['dhb_sign_corp'];
        $info['payment_type_name']    = $customer_options['payment_type'][$info['payment_type']];
        $info['source_id_arr']        = array_map(function($item){return intval($item);},explode(',',$info['source_id']));


        $this->response(Enu::RESP_OK,$info);
    }

    /**
     *
     * 编辑客户信息
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-18 10:09:21
     */
    public function edit(){
        $name                 = $this->request->get('name');
        $company              = $this->request->get('company');
        $dhb_sign_corp        = $this->request->get('dhb_sign_corp');
        $status               = $this->request->get('status',0,false);
        $type                 = $this->request->get('type');
        $change_month         = $this->request->get('change_month');
        $customer_type        = $this->request->get('customer_type', 1);
        $operator             = $this->request->get('operator');
        $salesman             = $this->request->get('salesman');
        $channel_follower     = $this->request->get('channel_follower');                               // 渠道跟进人
        $source_id            = implode(',', $this->request->get('source_id', []));                    // 渠道跟进人可见征信机构
        $customer_id          = $this->request->get('customer_id');

        if (empty($customer_id)) {
            $this->response(Enu::RESP_CUSTOMER_MISSING_ID);//未选择客户
        }
        $customer_info = CustomerRepositorie::getCustomerInfo($customer_id);
        if(empty($customer_info)){
            $this->response(Enu::RESP_CUSTOMER_CAN_NOT_FIND);//客户已经禁用或不存在
        }

        $channel_mode         = $this->request->get('channel_mode');                                   //客户类型 是否为渠道客户
        $sign_type            = $this->request->get('sign_type', Enu::CUSTOMER_SIGN_TYPE_DHB, false);  //征信客户分类： 0: 电话邦签约, 1: 朴道签约
        $group_id             = $this->request->get('group_id');                                       //主体id
        $reconciliation_cycle = $this->request->get('reconciliation_cycle');                           //对账方式 对账周期
        $payment_type         = $this->request->get('payment_type');
        $bill_email           = $this->request->get('bill_email');
        $bill_cc_email        = $this->request->get('bill_cc_email');
        $email_type           = $this->request->get('email_type', 1);


        $data = [
            'name'                 => $name,
            'company'              => $company,
            'dhb_sign_corp'        => $dhb_sign_corp,
            'status'               => $status,
            'type'                 => $type,
            'customer_type'        => $customer_type,
            'operator'             => $operator,
            'salesman'             => $salesman,
            'channel_follower'     => $channel_follower,
            'source_id'            => $source_id,
            'channel_mode'         => $channel_mode,
            'sign_type'            => $sign_type,
            'group_id'             => $group_id,
            'reconciliation_cycle' => $reconciliation_cycle,
            'bill_email'           => $bill_email,
            'bill_cc_email'        => $bill_cc_email,
            'payment_type'         => $payment_type,
        ];

        $customer_info = CustomerRepositorie::getCustomerInfo($customer_id);

        //校验参数
        $data = $this->check_params($data);
        $name_unique = CustomerRepositorie::getCustomerCount([['name','=',$name],['customer_id','!=',$customer_id]]);
        if ($name_unique > 0) {
            $this->response(Enu::RESP_CUSTOMER_USED);//客户名称已经被占用，请更换客户名称
        }

        $csh_list = CustomerSalesmanHistoryRepositorie::getCustomerSalesman($customer_id);
        $current_salesman = array_pop($csh_list);//当前的商务
        $current_min_end = strtotime("+1 month",strtotime($current_salesman['start_day'])) - 1;//起始时间往后一个月
        $last_salesman = array_pop($csh_list);//当前商务的上一个商务 可能是空
        $this->check_change_month($change_month,$current_salesman,$salesman,$current_min_end,$last_salesman);

        $update_at = date(Enu::TIME_FORMAT);

        $data['email_type']      = $email_type;
        $data['admin']           = $this->admin;
        $data['update_at']       = $update_at;

        $diff_count = 0;
        foreach($customer_info as $field => $value){
            if(in_array($field, ['id','customer_id','update_at','create_at','contract_status','credit','deleted_at'])){
                continue;
            }
            if($data[$field] != $value){
                $diff_count ++;
            }
        }

        $current_salesman_start_month = date("Y-m",strtotime($current_salesman['start_day']));
        if($current_salesman_start_month == $change_month){
            //客户信息无变化
            if($diff_count == 0){
                $this->response(Enu::RESP_CUSTOMER_NOTHING_CHANGE);
            }
        }

        //更新客户
        if($diff_count > 0) {
            $edit_customer_res = CustomerRepositorie::editCustomer($customer_id, $data);
            if ($edit_customer_res < 1) {
                $this->response(Enu::RESP_CUSTOMER_UPDATE_FAIL);//客户信息更新失败
            }
        }

        //更新客户商务历史记录
        $this->change_salesman($customer_id,$change_month,$current_salesman,$salesman,$last_salesman);

        $this->response(Enu::RESP_OK);
    }


    /**
     * 参数校验
     *
     * @param $params
     *
     * @return mixed
     * <AUTHOR> 2023-09-18 10:21:11
     */
    private function check_params($params){
        if (empty($params['name'])) {
            $this->response(Enu::RESP_CUSTOMER_MISSING_NAME);//客户名称未填写
        }

        if (empty($params['company'])) {
            $this->response(Enu::RESP_CUSTOMER_MISSING_COMPANY);//客户公司未填写
        }

        // if (empty($params['group_id'])) {
        //     $this->response(Enu::RESP_CUSTOMER_MISSING_GROUP);//客户主体未填写
        // }

        $crlf = "/\n|\r|(\n\r)/";
        //账单邮箱
        if (!empty($params['bill_email'])) {
            $bill_email_arr = preg_split($crlf,$params['bill_email']);
            if (count($bill_email_arr) > 8) {
                $this->response(Enu::RESP_CUSTOMER_BILL_EMAIL_LIMIT);//账单收件人最多只允许设置8个邮箱
            }
            //校验邮箱格式
            $bill_email_arr = array_map(function ($bill_email) {
                $bill_email = trim($bill_email);
                if (!filter_var($bill_email, FILTER_VALIDATE_EMAIL)) {
                    $this->response(Enu::RESP_CUSTOMER_EMAIL_TYPE_ERR,['data' => $bill_email."不是标准的邮箱格式"]);//不是标准的邮箱格式
                }
                return $bill_email;
            }, $bill_email_arr);
            $params['bill_email'] = implode(';', $bill_email_arr);
        }

        //账单抄送人
        if (!empty($params['bill_cc_email'])) {
            $bill_cc_email_arr = preg_split($crlf,$params['bill_cc_email']);
            if (count($bill_cc_email_arr) > 20) {
                $this->response(Enu::RESP_CUSTOMER_BILL_CC_EMAIL_LIMIT);//账单抄送人最多只允许设置8个邮箱
            }
            $bill_cc_email_arr = array_map(function ($bill_cc_email) {
                $bill_cc_email = trim($bill_cc_email);
                //校验邮箱格式
                if (!filter_var($bill_cc_email, FILTER_VALIDATE_EMAIL)) {
                    $this->response(Enu::RESP_CUSTOMER_EMAIL_TYPE_ERR,['data' => $bill_cc_email."不是标准的邮箱格式"]);//不是标准的邮箱格式
                }
                return $bill_cc_email;
            }, $bill_cc_email_arr);
            $params['bill_cc_email'] = implode(';', $bill_cc_email_arr);
        }

        return $params;
    }


    /**
     * 检查修改商务的月份数据
     *
     * @param $change_month
     * @param $current_salesman
     * @param $new_salesman
     * @param $current_min_end
     * @param $last_salesman
     *
     * @return void
     * <AUTHOR> 2023-09-18 11:47:15
     */
    private function check_change_month($change_month,$current_salesman,$new_salesman,$current_min_end,$last_salesman){
        //客户商务未修改
        if($current_salesman['salesman'] == $new_salesman){
            return;
        }
        $change_time = strtotime($change_month."-01");

        if ($current_min_end > $change_time) {//校验切换时间 添加新商务需要给当前商务最少一个月
            $this->response(Enu::RESP_CUSTOMER_SALESMAN_SAME_MONTH);//商务跟进人切换间隔应大于一个月
        }

        //更改当前商务的开始时间
        if(!is_null($last_salesman)) {//当前商务之前没有商务,随意更改不校验
            //当前商务的前一个商务需要最少一个月
            $last_min_end = strtotime("+1 month", strtotime($last_salesman['start_day'])) - 1;//起始时间往后一个月
            if ($last_min_end > $change_time) {//校验切换时间
                $this->response(Enu::RESP_CUSTOMER_SALESMAN_SAME_MONTH);//商务跟进人切换间隔应大于一个月
            }
        }
    }

    /**
     * 更新历史商务
     *
     * @param $customer_id
     * @param $change_month
     *
     * @param $current_salesman
     * @param $new_salesman
     * @param $last_salesman
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-18 14:48:06
     */
    private function change_salesman($customer_id,$change_month,$current_salesman,$new_salesman,$last_salesman){
        //更新历史商务
        $now = date(Enu::TIME_FORMAT);
        $change_month = strtotime($change_month."-01");
        if($current_salesman['salesman'] != $new_salesman){//更改商务
            //更新之前的商务 添加结束时间至前一天
            //更新之前商务的条件
            $where = [
                ['customer_id','=',$customer_id],
                ['salesman','=',$current_salesman['salesman']],
                ['end_day','=',''],//默认值为空字符串
            ];
            $update_data = [
                'end_day'   => date("Ymd",strtotime("-1 day",$change_month)),
                'update_at' => $now,
            ];
            CustomerSalesmanHistoryRepositorie::edit($where,$update_data);

            //添加新商务
            CustomerSalesmanHistoryRepositorie::add($customer_id,$new_salesman,date("Ymd",$change_month));
        }else{//如果修改的商务与当前一致,时间不一致 更新当前的开始时间,上一个商务的结束时间
            //更新上一个结束时间
            $change_day = date("Ymd", $change_month);
            if($change_day != $current_salesman['start_day']) {//商务没有更改,时间更改了
                if (!is_null($last_salesman)) {//有多条历史商务数据
                    $where       = [['id', '=', $last_salesman['id']]];
                    $update_data = [
                        'end_day'    => date("Ymd", strtotime("-1 day", $change_month)),
                        'update_at' => $now,
                    ];
                    CustomerSalesmanHistoryRepositorie::edit($where,$update_data);
                }
                //更新当前开始时间
                $where       = [['id' ,'=', $current_salesman['id']]];
                $update_data = [
                    'start_day' => $change_day,
                    'update_at' => $now,
                ];
                CustomerSalesmanHistoryRepositorie::edit($where,$update_data);
            }
        }
    }
}