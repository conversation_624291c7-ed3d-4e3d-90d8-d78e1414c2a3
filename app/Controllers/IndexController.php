<?php

namespace App\Controllers;

use App\Jobs\TestJob;
use App\Models\System\SystemUserModel;
use App\Models\UserModel;
use App\Repositories\System\UserRepositorie;
use App\Utils\Helpers\Enu;
use Exception;
use Yulore\Components\Db;
use App\Models\UserMongoModel;
use Yulore\Components\Queue;

class IndexController extends BaseController
{

    /**
     * @throws Exception
     */
    public function index()
    {
        //$res = UserModel::getAll([]);
        //$res = $this->cache->get('abc');
        //var_dump($res);die;
        //$res = UserModel::query('select * from  cuishou_user where id>0',[],'all');
        //var_dump($res);die;

        // $res = Db::query('','select * from  dict_itagtel where id=10');

        // $res = Db::query('','select * from  dict_itagtel where id=10');


        //$res = $db->exec('','update dict_itagtel set company_name = "ccccc" where id=10');
        //$res = $db->query($db->getPdo('default'),'select * from  dict_itagtel where id=10');

        //var_dump($res);die;

        //foreach ($res as $row) {
        //print_r($row); //你可以用 echo($GLOBAL); 来看到这些值
        //}
        //$res = UserMongoModel::getOne(['sid' => '82beabc0eaaa0f1a7e4837c92c330af1'], ['apikey']);
        //var_dump($res);
        //app('queue')->onQueue(TestJob::class, ['name' => 'yangchengsheng']);


        // SystemUserModel::beginTransaction();
        // //
        // $res = SystemUserModel::edit([['username','=','wei.xiu']],['token' => 'c68665cedacf727bbc6f55ad0c20f049']);
        // SystemUserModel::commit();
        // dd($res);

        $this->response(Enu::RESP_OK, 'ok');
        // $this->response(0, ['hello word 3233223 ec  '.Enu::TEST_ENUM]);
    }


    /**
     * 校验token是否过期
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-12-11 18:29:39
     */
    public function check_token(){
        $this->response(Enu::RESP_OK, 'ok');
    }


    /**
     * 获取登录地址
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-09-15 16:45:18
     */
    public function auth_url(){

        // $login_from_url = $this->request->get('login_from_url');
        // $itemurl = rtrim(config('site_url')).'validate';
        // $auth_url = config('auth_url'). '/login.php?itemid=' . config('dhb_item_id') . '&itemurl=';
        $data = [
            'auth_url' => config('auth_url').'/login.php',
            'item_id'  => intval(config('dhb_item_id')),
            'item_url' => '',
        ];
        $this->response(0,$data);
    }


    /**
     * @throws Exception
     */
    public function validate(){
        $ticket = $this->request->get('ticket');
        $info = $this->get_user_by_cas($ticket);
        if($info === false){
            $this->response(1, 'fail!');
        } else {//通过校验

            //1 查看是否存在用户
            //1.1 如果不存在创建用户
            //2 设置token信息
            //3 返回token信息

            $now = time();
            $now_str = date(Enu::TIME_FORMAT,$now);

            // 生成token
            $token = UserRepositorie::createToken($info['username'],$now_str);
            // token截止时间
            $expire_at = date(Enu::TIME_FORMAT, $now + Enu::TOKEN_EXPR);


            $user_info = UserRepositorie::getUser($info['username']);


            if($user_info === false){
                $this->response(1, '请使用原后台进行登录!!!');
                // UserRepositorie::createUser($info['username'],$info['email'],$now_str,$token,$expire_at);
            }else{
                UserRepositorie::updateTokenInfo($info['username'],$now,$token,$expire_at);
            }

            $this->response(0, ['token' => $token, 'username' => $user_info['realname']]);
        }
    }


    /**
     * 使用ticket换取用户信息
     *
     * @param $ticket
     *
     * @return false|mixed
     */
    private function get_user_by_cas($ticket) {
        $url = config('auth_url') . '/validate.php?ticket=' . urlencode($ticket);
        $result = curl($url);
        $result = json_decode($result, true);
        if ($result['status'] == "success") {
            return $result['data'];
        }
        return false;
    }








    public function get_group_list(){
         $this->response(0, [1,2,3,4,5]);
    }
}