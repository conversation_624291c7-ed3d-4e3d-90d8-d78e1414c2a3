<?php

/**
 * CURL请求
 *
 * @param string $url    请求网址
 * @param mixed  $params 请求参数
 * @param int    $ispost 请求方式
 * @param int    $https  https协议
 * @param array  $header 头信息
 *
 * @return bool
 */
function curl($url, $params = false, $ispost = false, $https = false, $header = []) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2272.118 Safari/537.36');
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    //设置header
    if (!empty($header)) {
        if (isset($header['Content-Type']) && $header['Content-Type'] == 'application/json') {
            $params = json_encode($params);
            curl_setopt($ch, CURLOPT_HTTPHEADER, ["Content-type: application/json;charset='utf-8'"]);
        }
    }
    if ($https) {
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 对认证证书来源的检查
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 从证书中检查SSL加密算法是否存在
    }
    if ($ispost) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
        curl_setopt($ch, CURLOPT_URL, $url);
    } else {
        if ($params) {
            if (is_array($params)) {
                $params = http_build_query($params);
            }
            curl_setopt($ch, CURLOPT_URL, $url . (strpos($url, '?') ? '&' : '?') . $params);
        } else {
            curl_setopt($ch, CURLOPT_URL, $url);
        }
    }

    $response = curl_exec($ch);

    if ($response === false) {
        return false;
    }
    curl_close($ch);
    return $response;
}

/**
 * filter an param from the request.
 *
 * @param mixed $param
 *
 * @return mixed
 */
function param_filter($param) {
    if (!$param) return;

    if (is_array($param)) {
        foreach ($param as &$v) {
            $v = is_array($v) ? param_filter($v) : htmlspecialchars(strip_tags($v), ENT_QUOTES);
        }
    } else {
        $param = htmlspecialchars(strip_tags($param), ENT_QUOTES);
    }
    return $param;
}

/**
 * 获取客户端IP地址
 *
 * @param integer $type 返回类型 0 返回IP地址 1 返回IPV4地址数字
 *
 * @return mixed
 */
function get_client_ip($type = 0) {
    if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $arr = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
        $pos = array_search('unknown', $arr);
        if (false !== $pos)
            unset($arr[$pos]);
        $ip = trim($arr[0]);
    } elseif (isset($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (isset($_SERVER['REMOTE_ADDR'])) {
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    // IP地址合法验证
    $long = sprintf("%u", ip2long($ip));
    $ip   = $long ? [$ip, $long] : ['0.0.0.0', 0];
    return $ip[$type];
}


/**
 * 客户编号规范为：C20180816A1B2C3
 * 账号编号规范为：FA/TA20180816A1B2C3（FA为正式账号/TA为测试账号）
 * 主体: GR
 * 配置审批 CA config approval
 * 说明（客户、账号）
 * 20180816：创建的当前年月日
 * A1B2C3：后6位做0-9A-Z6位随机数
 * @param $prex
 * @param $random_len
 *
 * @return string
 * <AUTHOR> 2023-09-15 17:23:47
 */
function create_uuid($prex, $random_len = 6) {
    $date = date('Ymd');

    $b = range('A', 'Z');
    $c = range('0', '9');
    $chars = array_merge($b, $c);
    $charslen = count($chars) - 1;
    shuffle($chars);

    $output = $prex . $date;
    for ($i = 0; $i < $random_len; $i++) {
        $output .= $chars[mt_rand(0, $charslen)];
    }

    return $output;
}


/**
 * 使用换行符分割字符串
 *
 * @param $str
 *
 * @return array|false|string[]
 * <AUTHOR> 2023-09-26 14:28:48
 */
function split_with_crlf($str){
    return preg_split("/\n|\r|(\r\n)|,|;/", $str);
}


/**
 * 获取临时key
 *
 * @param ...$args
 *
 * @return string
 * <AUTHOR> 2023-10-12 11:58:14
 */
function get_tmp_key(...$args){
    return implode('_',$args);
}


/**
 * 打印变量
 * @param ...$param
 *
 * @return void
 */
function dd(...$param){
    if(PHP_SAPI == 'cli'){
        foreach(func_get_args() as $var){
            var_dump($var);
            echo PHP_EOL;
        }
        die;
    }
    echo '<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><title>php 调试</title></head><body style="background-color:#1c1c1c;color:#cbcbcb">';
    echo '<pre>';
    foreach(func_get_args() as $var){
        var_dump($var);
        echo '<br />';
    }
    echo '</pre></body></html>';
    die;
    // header('Content-Type: application/json; charset=utf-8');
    // $paras = func_get_args();
    // array_unshift($paras,'debug');
    // exit(json_encode($paras));
}


/**
 * 打印请求参数
 *
 * @return void
 * <AUTHOR> 2023-09-15 16:03:09
 */
function dd_paras($request){
    echo '<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><title>php 调试</title></head><body style="background-color:#1c1c1c;color:#cbcbcb">';
    echo "<pre>";
    foreach($request->keys() as $para_nam => $para_val){
        echo '<span style="color: #4f918c">',$para_val,'</span> ';
        var_dump($request->get($para_val));
    }
    echo '</pre></body></html>';
    die;
}


/**
 * 处理 客户配置中对于主体,客户,账号相关的条件
 *
 * @param $where
 * @param $group_id
 * @param $customer_id
 * @param $account_id
 *
 * @return void
 * <AUTHOR> 2023-11-09 10:50:15
 */
function gca_where(&$where,$group_id,$customer_id,$account_id){
    $_where = [];
    if(!empty($group_id)){
        $_where['group_id'] = $group_id;
        $_where['customer_id'] = '';
        $_where['account_id'] = '';
    }
    if(!empty($customer_id)){
        $_where['customer_id'] = $customer_id;
        $_where['account_id'] = '';
    }
    if(!empty($account_id)){
        $_where['account_id'] = $account_id;
    }

    foreach($_where as $col => $val){
        $where[] = [$col,'=',$val];
    }
}