<?php

namespace App\Utils\Helpers;

/**
 * 枚举
 *
 * 数据库状态
 */
class Enu
{
    // ---------- 错误码 -----------------------------------------------------------------
    /** @var int 请求成功 */
    const RESP_OK = 0;
    /** @var int 请求失败 */
    const RESP_FAILED = 1;
    /** @var int 客户token过期 */
    const RESP_USER_TOKEN_EXPIRED = 1001;
    /** @var int 缺少token */
    const RESP_USER_MISSING_TOKEN = 1002;
    /** @var int 客户不可用 */
    const RESP_USER_STATUS_ERR = 1003;
    /** @var int 未找到用户 */
    const RESP_USER_NOT_FOND = 1004;
    /** @var int 没有权限进行该操作 */
    const RESP_USER_HAVE_NO_AUTHORIZATION = 1005;
    /** @var int 程序运行异常 */
    const RESP_SERVICE_UNAVAILABLE = 5001;

    // ---客户相关 错误码
    /** @var int 客户名称未填写 */
    const RESP_CUSTOMER_MISSING_NAME = 1101;
    /** @var int 客户名称已存在 */
    const RESP_CUSTOMER_USED = 1102;
    /** @var int 账单收件人最多只允许设置8个邮箱 */
    const RESP_CUSTOMER_BILL_EMAIL_LIMIT = 1103;
    /** @var int 账单抄送人最多只允许设置8个邮箱 */
    const RESP_CUSTOMER_BILL_CC_EMAIL_LIMIT = 1104;
    /** @var int 邮箱不是标准的邮箱格式 */
    const RESP_CUSTOMER_EMAIL_TYPE_ERR = 1105;
    /** @var int 请选择客户 */
    const RESP_CUSTOMER_MISSING_ID = 1106;
    /** @var int 客户公司未填写 */
    const RESP_CUSTOMER_MISSING_COMPANY = 1107;
    /** @var int 客户主体未填写 */
    const RESP_CUSTOMER_MISSING_GROUP = 1108;
    /** @var int 客户已经禁用或不存在 */
    const RESP_CUSTOMER_CAN_NOT_FIND = 1109;
    /** @var int 客户没有修改 */
    const RESP_CUSTOMER_NOTHING_CHANGE = 1110;
    /** @var int 客户没有修改 */
    const RESP_CUSTOMER_UPDATE_FAIL = 1111;



    // ---客户历史商务 错误码
    /** @var int 商务跟进人切换间隔应大于一个月 */
    const RESP_CUSTOMER_SALESMAN_SAME_MONTH = 1201;


    // ---主体 错误码
    /** @var int 客户关联主体 缺少主体id */
    const RESP_GROUP_MISSING_GROUP_ID = 1301;
    /** @var int 客户关联主体 缺少客户id */
    const RESP_GROUP_MISSING_CUSTOMER_ID = 1302;
    /** @var int 客户状态错误 */
    const RESP_GROUP_WORNG_CUSTOMER = 1303;
    /** @var int 客户已经关联主体 */
    const RESP_GROUP_CUSTOMER_BIND = 1304;
    /** @var int 添加,编辑客户主体 主体名称 */
    const RESP_GROUP_MISSING_GROUP_NAME = 1305;
    /** @var int 主体名称重复 */
    const RESP_GROUP_DUPLICATION_GROUP_NAME = 1306;


    // --- 主产品 错误码
    /** @var int 缺少产品id */
    const RESP_PRODUCT_MISSING_PRODUCT_ID = 1401;
    /** @var int 缺少产品名称 */
    const RESP_PRODUCT_MISSING_PRODUCT_NAME = 1402;
    /** @var int 缺少产品key */
    const RESP_PRODUCT_MISSING_PRODUCT_KEY = 1403;
    /** @var int 产品id重复 */
    const RESP_PRODUCT_DUPLICATION_PRODUCT_ID = 1404;
    /** @var int 产品名称重复 */
    const RESP_PRODUCT_DUPLICATION_PRODUCT_NAME = 1405;

    // --- 主产品配置项 错误码
    /** @var int 缺少产品配置项key */
    const RESP_PRODUCT_CONFIG_ITEM_MISSING_ID = 1500;
    /** @var int 缺少产品配置项key */
    const RESP_PRODUCT_CONFIG_ITEM_MISSING_KEY = 1501;
    /** @var int 缺少产品配置项名称 */
    const RESP_PRODUCT_CONFIG_ITEM_MISSING_NAME = 1502;
    /** @var int 缺少产品配置项角色 */
    const RESP_PRODUCT_CONFIG_ITEM_MISSING_ROLE = 1503;
    /** @var int 缺少产品配置项是否可被客户修改 */
    const RESP_PRODUCT_CONFIG_ITEM_MISSING_MODIFY = 1504;
    /** @var int 缺少产品配置项是否可被子产品继承 */
    const RESP_PRODUCT_CONFIG_ITEM_MISSING_EXTEND = 1505;
    /** @var int 缺少产品配置项类型 */
    const RESP_PRODUCT_CONFIG_ITEM_MISSING_ITEM_TYPE = 1506;
    /** @var int 缺少产品配置项默认值 */
    const RESP_PRODUCT_CONFIG_ITEM_MISSING_ITEM_DEFAULT = 1507;
    /** @var int 产品配置项key重复 */
    const RESP_PRODUCT_CONFIG_ITEM_DUPLICATION_KEY = 1508;
    /** @var int 请传入默认值 */
    const RESP_PRODUCT_CONFIG_ITEM_MISSING_DEFAULT = 1509;
    /** @var int 默认值与选项中值不匹配 */
    const RESP_PRODUCT_CONFIG_ITEM_DEFAULT_VALUE_NOT_IN_OPTIONS = 1510;
    /** @var int 缺少配置路径 */
    const RESP_PRODUCT_CONFIG_ITEM_MISSING_CONFIG_PATH = 1511;
    /** @var int 缺少 配置范围  */
    const RESP_PRODUCT_CONFIG_ITEM_MISSING_CONFIG_SCOPE = 1512;
    /** @var int 参数冲突 仅在客户维度配置时须可被客户修改 */
    const RESP_PRODUCT_CONFIG_ITEM_CONFLICT_CONFIG_SCOPE_AND_MODIFY = 1513;

    // --- 主产品配置
    /** @var int 缺少产品配置值 */
    const RESP_PRODUCT_CONFIG_MISSING_VALUE = 1600;
    /** @var int 配置值错误 */
    const RESP_PRODUCT_CONFIG_UNEXPECTED_VALUE = 1601;
    /** @var int 配置项没有更改 */
    const RESP_PRODUCT_CONFIG_HAVE_NO_MODIFY = 1602;


    // --- 子产品
    /** @var int 缺少主产品id */
    const RESP_SUB_PRODUCT_MISSING_FATHER_ID = 1701;
    /** @var int 缺少子产品id */
    const RESP_SUB_PRODUCT_MISSING_PRODUCT_ID = 1702;
    /** @var int 缺少子产品名称 */
    const RESP_SUB_PRODUCT_MISSING_PRODUCT_NAME = 1703;
    /** @var int 缺少子产品key */
    const RESP_SUB_PRODUCT_MISSING_PRODUCT_KEY = 1704;
    /** @var int 子产品id重复 */
    const RESP_SUB_PRODUCT_DUPLICATION_PRODUCT_ID = 1705;
    /** @var int 子产品名称重复 */
    const RESP_SUB_PRODUCT_DUPLICATION_PRODUCT_NAME = 1706;
    /** @var int 主产品不存在 */
    const RESP_SUB_PRODUCT_NONE_FATHER_PRODUCT = 1707;


    // --- 子产品配置项
    /** @var int 缺少子产品配置项key */
    const RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_ID = 1800;
    /** @var int 缺少子产品配置项key */
    const RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_KEY = 1801;
    /** @var int 缺少子产品配置项名称 */
    const RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_NAME = 1802;
    /** @var int 缺少子产品配置项角色 */
    const RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_ROLE = 1803;
    /** @var int 缺少子产品配置项是否可被客户修改 */
    const RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_MODIFY = 1804;
    /** @var int 缺少子产品配置项是否可被子产品继承 */
    const RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_EXTEND = 1805;
    /** @var int 缺少子产品配置项类型 */
    const RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_ITEM_TYPE = 1806;
    /** @var int 缺少子产品配置项默认值 */
    const RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_ITEM_DEFAULT = 1807;
    /** @var int 缺少配置选项 */
    const RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_OPTIONS = 1808;
    /** @var int 与配置项key重复 */
    const RESP_SUB_PRODUCT_CONFIG_ITEM_DUPLICATION_KEY = 1809;
    /** @var int 与主产品配置项key重复 */
    const RESP_SUB_PRODUCT_CONFIG_ITEM_DUPLICATION_PRODUCT_KEY = 1810;
    /** @var int 请传入子产品id */
    const RESP_SUB_PRODUCT_CONFIG_ITEM_NOT_SUB_PRODUCT_ID = 1811;
    /** @var int 请传入默认值 */
    const RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_DEFAULT = 1812;
    /** @var int 默认值与选项中值不匹配 */
    const RESP_SUB_PRODUCT_CONFIG_ITEM_DEFAULT_VALUE_NOT_IN_OPTIONS = 1813;
    /** @var int 缺少 配置范围  */
    const RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_CONFIG_SCOPE = 1814;
    /** @var int 参数冲突 仅在客户维度配置时须可被客户修改 */
    const RESP_SUB_PRODUCT_CONFIG_ITEM_CONFLICT_CONFIG_SCOPE_AND_MODIFY = 1815;

    // --- 子产品配置
    /** @var int 错误的配置来源 */
    const RESP_SUB_PRODUCT_CONFIG_WORNG_SOURCE = 1850;
    /** @var int 错误的配置id */
    const RESP_SUB_PRODUCT_CONFIG_WORNG_ITEM_ID = 1851;


    // --- 客户产品
    /** @var int 参数错误 */
    const RESP_CUSTOMER_PRODUCT_CONFIG_WORNG_PARA = 1900;
    /** @var int 缺少账户id */
    const RESP_CUSTOMER_PRODUCT_CONFIG_MISSING_ACCOUNT_ID = 1901;
    /** @var int 请传入产品id或子产品id */
    const RESP_CUSTOMER_PRODUCT_CONFIG_MISSING_PRODUCT_ID_OR_SUB_PRODUCT_ID = 1902;
    /** @var int 请传入正确参数 是否可被客户修改 1:可修改 2:不可修改 */
    const RESP_CUSTOMER_PRODUCT_CONFIG_WORNG_MODIFY_VALUE = 1903;
    /** @var int 配置来源错误 */
    const RESP_CUSTOMER_PRODUCT_CONFIG_WORNG_SOURCE_VALUE = 1904;
    /** @var int 请传入主体id,客户id或账号id */
    const RESP_CUSTOMER_PRODUCT_CONFIG_MISSING_GROUP_ID_OR_CUSTOMER_ID_OR_ACCOUNT_ID = 1905;
    /** @var int 缺少配置项id */
    const RESP_CUSTOMER_PRODUCT_CONFIG_MISSING_ITEM_ID = 1906;
    /** @var int 没有对应配置 */
    const RESP_CUSTOMER_PRODUCT_CONFIG_HAVE_NO_CONFIG = 1907;
    /** @var int 该客户没有开通此产品 */
    const RESP_CUSTOMER_PRODUCT_CUSTOMER_HAS_NO_OPEN_THIS_PRODUCT = 1908;
    /** @var int 产品无此配置项 */
    const RESP_CUSTOMER_PRODUCT_ILLEG_ITEM_ID = 1909;


    // --- 审批
    /** @var int 添加审批失败 */
    const RESP_APPROVAL_ADD_FAIL = 2001;
    /** @var int 状态错误 */
    const RESP_APPROVAL_STATUS_WORNG = 2002;
    /** @var int 该操作正在审批中 */
    const RESP_APPROVAL_DUPLICATION = 2003;

    // --- 搜索
    const RESP_SEARCH_PARAMS_EMPTY = 2100;



    // --- 合同管理

    /** @var int 上传合同未选择类别 */
    const RESP_CONTRACT_UPLOAD_CATEGORY_EMPTY = 2200;
    /** @var int 上传合同未选择excel文件 */
    const RESP_CONTRACT_UPLOAD_EXCEL_EMPTY = 2201;
    /** @var int 上传合同未选择pdf文件 */
    const RESP_CONTRACT_UPLOAD_PDF_EMPTY = 2202;
    /** @var int excel文件获取失败 */
    const RESP_CONTRACT_LOAD_EXCEL_FAIL = 2203;
    /** @var int 选择的类别与excel内容符,请检查 */
    const RESP_CONTRACT_CATEGORY_EXCEL_NOT_MATCH = 2204;
    /** @var int 合同类型错误 */
    const RESP_CONTRACT_TYPE_ERR = 2205;
    /** @var int 签约类型错误 */
    const RESP_CONTRACT_SIGN_ERR = 2206;
    /** @var int 合同编号缺失 */
    const RESP_CONTRACT_MISSING_CONTRACT_NO = 2207;
    /** @var int 合同文件缺失 */
    const RESP_CONTRACT_MISSING_CONTRACT_FILE = 2208;
    /** @var int 合同文件数量大于数据行数 */
    const RESP_CONTRACT_CONTRACT_FILE_COUNT_GREATER_THAN_ROWS = 2209;
    /** @var int 合同文件数量小于数据行数 */
    const RESP_CONTRACT_CONTRACT_FILE_COUNT_LESS_THAN_ROWS = 2210;
    /** @var int 合同编号重复 */
    const RESP_CONTRACT_CONTRACT_NO_DUPLICATION = 2211;


    // ---------- 错误码 结束 -----------------------------------------------------------------


    // ------------ 通用 ---------------------------------------------------------------
    /** @var string 时间格式 */
    const TIME_FORMAT      = 'Y-m-d H:i:s';
    const TIME_FORMAT_YM01 = 'Ym01';
    const TIME_FORMAT_YM   = 'Ym';
    /** @var string 公司名称 德宝天辰 */
    const DHB_SIGN_CORP_DEBAOTC = 'debaotc';
    /** @var string 公司名称 翊新 */
    const DHB_SIGN_CORP_YIXIN = 'yixin';
    /** @var string 公司名称 羽乐 */
    const DHB_SIGN_CORP_YULORE = 'yulore';
    // ------------ 通用 ---------------------------------------------------------------

    // ----------- 后台用户 -------------------------------------------------------------
    /** @var int  用户状态 可用 */
    const USER_STATUS_ENABLE = 1;
    /** @var int 用户状态 禁用 */
    const USER_STATUS_DISENABLE = 2;
    /** @var string token 加盐 */
    const TOKEN_SALT = 'dianhua';
    /** @var int token过期时间 */
    const TOKEN_EXPR = 604800;//7天过期时间

    //--- 用户角色
    /** @var int 管理员 */
    const USER_ROLE_TYPE_SUPER = 51;
    /** @var string 用户角色 技术 */
    const USER_ROLE_TYPE_TECHNICIAN = 5;
    /** @var string 用户角色 运营 */
    const USER_ROLE_TYPE_OPERATOR = 3;
    /** @var string 用户角色 产品 */
    const USER_ROLE_TYPE_PRODUCT_MANAGER = 10;
    /** @var int 用户角色 产品配置审批角色 */
    const USER_ROLE_TYPE_APPROVAL_PRODUCT_MANAGER = 90;
    /** @var int 用户角色 运营配置审批角色 */
    const USER_ROLE_TYPE_APPROVAL_OPERATOR = 93;
    /** @var int 用户角色 技术配置审批角色 */
    const USER_ROLE_TYPE_APPROVAL_TECHNICIAN = 95;

    //--- 用户角色

    // ----------- 后台用户 -------------------------------------------------------------

    //------------ 主体,客户,账号 --------------------------------------------------------

    //--- 主体
    /** @var int 主体状态 可用 */
    const CUSTOMER_GROUP_STATUS_ENABLE = 1;
    /** @var int 主体状态 禁用 */
    const CUSTOMER_GROUP_STATUS_DISENABLE = 2;
    //--- 主体


    //---客户
    /** @var int 客户状态 可用 */
    const CUSTOMER_STATUS_ENABLE = 1;
    /** @var int 克虎状态 禁用 */
    const CUSTOMER_STATUS_DISENABLE = 0;


    /** @var int 客户类型 金融客户 */
    const CUSTOMER_TYPE_FIN = 1;
    /** @var int 客户类型 企服客户 */
    const CUSTOMER_TYPE_COM = 2;


    /** @var int 客户付费方式 预付费 */
    const CUSTOMER_PAYMENT_TYPE_PREPAID = 1;
    /** @var int 客户付费方式 后付费 */
    const CUSTOMER_PAYMENT_TYPE_POSTPAID = 2;


    /** @var int 客户对账周期 月度 */
    const CUSTOMER_RECONCILIATION_CYCLE_MONTHLY = 1;
    /** @var int 客户对账周期 季度 */
    const CUSTOMER_RECONCILIATION_CYCLE_QUARTERLY = 2;
    /** @var int 客户对账周期 年度 */
    const CUSTOMER_RECONCILIATION_CYCLE_ANNUALLY = 3;


    /** @var int 客户 征信客户分类 电话邦签约 */
    const CUSTOMER_SIGN_TYPE_DHB = 0;
    /** @var int 客户 征信客户分类 朴道签约 */
    const CUSTOMER_SIGN_TYPE_PD = 1;
    /** @var int 客户 征信客户分类 浙数交签约 */
    const CUSTOMER_SIGN_TYPE_ZSJ = 10;


    /** @var int 客户签约状态 已签约已付款 */
    const CUSTOMER_CONTRACT_STATUS_PAID = 1;
    /** @var int 客户签约状态 已签约未付款 */
    const CUSTOMER_CONTRACT_STATUS_SIGNED = 2;
    /** @var int 客户签约状态 未签约 */
    const CUSTOMER_CONTRACT_STATUS_UNSIGN = 3;
    /** @var int 客户签约状态 其他 */
    const CUSTOMER_CONTRACT_STATUS_OTHER = 4;
    /** @var int 客户签约状态 特殊客户 */
    const CUSTOMER_CONTRACT_STATUS_SPECIAL = 5;

    /** @var int 渠道客户类型 渠道客户 */
    const CUSTOMER_CHANNEL_MODE_CHANNEL_CUSTOMER = 1;
    /** @var int 渠道客户类型 非渠道客户(直客) */
    const CUSTOMER_CHANNEL_MODE_JUST_CUSTOMER = 2;

    /** @var int 账单发送邮件类型 标准 */
    const CUSTOMER_EMAIL_TYPE_STANDARD = 1;
    /** @var int 账单发送邮件类型 非标准 */
    const CUSTOMER_EMAIL_TYPE_NOT_STANDARD = 2;
    //---客户


    //---账号
    /** @var int 账号状态 可用 */
    const ACCOUNT_STATUS_ENABLE = 1;
    /** @var int 账号状态 禁用 */
    const ACCOUNT_STATUS_DISABLE = 0;
    //---账号

    //------------ 主体,客户,账号 --------------------------------------------------------


    //------------ 主产品,子产品 ---------------------------------------------------------
    /** @var int[] 主产品的father_id */
    const PRODUCT_FATHER_ID_ARRAY = [0,401];
    /** @var int 邦秒验产品id */
    const PRODUCT_BMY_PRODUCT_ID = 200;
    //------------ 主产品,子产品 ---------------------------------------------------------


    //------------ 配置通用 -------------------------------------------------------------
    /** @var string 配置状态 无配置,无审批 */
    // const CONFIG_STATUS_NONE = 'none';
    // /** @var string 配置状态 审批中 */
    // const CONFIG_STATUS_APPROVAL = 'approval';
    // /** @var string 配置状态 已经配置 */
    // const CONFIG_STATUS_CONFIG = 'config';
    //------------ 配置通用 -------------------------------------------------------------




    //------------ 产品配置 -------------------------------------------------------------
    // --- 主产品
    /** @var int 产品状态可用 */
    const PRODUCT_STATUS_ENBALE = 1;

    /** @var int 配置项 可被客户修改 */
    const PRODUCT_CONFIG_ITEM_CUSTOMER_CAN_MODIFY = 1;
    /** @var int 配置项 不可被客户修改 */
    const PRODUCT_CONFIG_ITEM_CUSTOMER_CAN_NOT_MODIFY = 2;


    /** @var int 配置项 子产品可继承 */
    const PRODUCT_CONFIG_ITEM_SUB_CAN_EXTEND = 1;
    /** @var int 配置项 可子产品不继承 */
    const PRODUCT_CONFIG_ITEM_SUB_CAN_NOT_EXTEND = 2;

    /** @var int 配置项 可以在产品配置中进行配置  */
    const PRODUCT_CONFIG_ITEM_PRODUCT_CAN_EDIT = 1;
    /** @var int 配置项 不可以在产品配置中进行配置 */
    const PRODUCT_CONFIG_ITEM_PRODUCT_CAN_NOT_EDIT = 2;

    /** @var int 产品配置项类型 单选 */
    const PRODUCT_CONFIG_ITEM_TYPE_RADIO = 'radio';
    /** @var int 产品配置项类型 多选 */
    const PRODUCT_CONFIG_ITEM_TYPE_CHECKBOX = 'checkbox';
    /** @var int 产品配置项类型 输入框 */
    const PRODUCT_CONFIG_ITEM_TYPE_INPUT = 'input';
    /** @var int 产品配置项类型 多行输入框 */
    const PRODUCT_CONFIG_ITEM_TYPE_MULTI_INPUT = 'textarea';
    /** @var int 产品配置项类型 日期 */
    const PRODUCT_CONFIG_ITEM_TYPE_DATE = 'date';
    /** @var int 产品配置项类型 时间 */
    const PRODUCT_CONFIG_ITEM_TYPE_TIME = 6;
    // --- 主产品
    //------------ 产品配置 -------------------------------------------------------------



    //------------ 子产品配置 -------------------------------------------------------------
    /** @var int 配置来源 主产品配置项 */
    const SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT = 1;
    /** @var int 配置来源 子产品配置项 */
    const SUB_PRODUCT_CONFIG_ITEM_SOURCE_SUB = 2;
    //------------ 子产品配置 -------------------------------------------------------------



    //------------ 配置搜索 -------------------------------------------------------------
    /** @var int 是否单独配置 是单独配置 */
    const CONFIG_SEARCH_IS_CONFIG_CONFIG = 1;
    /** @var int 是否单独配置 不是单独配置 */
    const CONFIG_SEARCH_IS_CONFIG_ITEM = 2;

    //----------- 配置维度 ------------------------------------
    /** @var string 配置维度 主产品 */
    const CONFIG_DIM_PRODUCT= 'product';        // '主体-主产品',
    /** @var string 配置维度 子产品 */
    const CONFIG_DIM_SUB_PRODUCT= 'sub_product';        // '子产品',
    /** @var string 配置维度 主体-主产品 */
    const CONFIG_DIM_GROUP_PRODUCT= 'group_product';        // '主体-主产品',
    /** @var string 配置维度 主体-子产品 */
    const CONFIG_DIM_GROUP_SUB_PRODUCT= 'group_sub_product';    // '主体-子产品',
    /** @var string 配置维度 客户-主产品 */
    const CONFIG_DIM_CUSTOMER_PRODUCT= 'customer_product';     // '客户-主产品',
    /** @var string 配置维度 客户-子产品 */
    const CONFIG_DIM_CUSTOMER_SUB_PRODUCT= 'customer_sub_product'; // '客户-子产品',
    /** @var string 配置维度 账号-主产品 */
    const CONFIG_DIM_ACCOUNT_PRODUCT= 'account_product';      // '账号-主产品',
    /** @var string 配置维度 账号-子产品 */
    const CONFIG_DIM_ACCOUNT_SUB_PRODUCT= 'account_sub_product';  // '账号-子产品',
    //----------- 配置维度 ------------------------------------
    //------------ 配置搜索 -------------------------------------------------------------




    //------------ 审批 -------------------------------------------------------------
    /** @var int[] 拥有审批权限的角色 */
    const APPROVAL_ROLES = [1,90,93,95];

    /** @var int 审批状态 待审批 */
    const APPROVAL_STATUS_NEED_APPROVAL = 1;
    /** @var int 审批状态 审批通过 */
    const APPROVAL_STATUS_PASS = 2;
    /** @var int 审批状态 审批驳回 */
    const APPROVAL_STATUS_REJECT = 3;
    /** @var int 审批状态 审批撤销 */
    const APPROVAL_STATUS_CANCEL = 4;

    //-----------------------------审批类型-----------------------
    /** @var string 审核类型 产品配置项 */
    const APPROVAL_TYPE_PRODUCT_CONFIG_ITEM = 'product_config_item';
    /** @var string 审核类型 产品配置 */
    const APPROVAL_TYPE_PRODUCT_CONFIG = 'product_config';
    /** @var string 审核类型 客户产品配置 */
    const APPROVAL_TYPE_CUSTOMER_PRODUCT_CONFIG = 'customer_product_config';
    //-----------------------------审批类型-----------------------

    //-----------------------------审批操作类型-----------------------
    /** @var string 添加操作 */
    const APPROVAL_ACTION_ADD = 'add';
    /** @var string 修改操作 */
    const APPROVAL_ACTION_EDIT = 'edit';
    /** @var string 删除操作 */
    const APPROVAL_ACTION_DELETE = 'delete';
    //-----------------------------审批操作类型-----------------------

    //------------ 审批 -------------------------------------------------------------





    //------------ 合同管理 ----------------------------------------------------------
    /** @var int 合同类别 客户类别 */
    const CONTRACT_CATEGORY_CUSTOMER = 1;
    /** @var int 合同类别 渠道类别 */
    const CONTRACT_CATEGORY_SOURCE   = 2;
    /** @var int 合同类别 三方类别 */
    const CONTRACT_CATEGORY_THRID    = 3;


    /** @var int 合同类型 收入 */
    const CONTRACT_TYPE_INCOME  = 1;
    /** @var int 合同类型 支出 */
    const CONTRACT_TYPE_EXPEND  = 2;
    /** @var int 合同类型 保密 */
    const CONTRACT_TYPE_SECRECY = 3;
    /** @var int 合同类型 其他 */
    const CONTRACT_TYPE_OTHER   = 4;

    /** @var int 签约类型 收入 */
    const CONTRACT_SIGN_TYPE_NEW_SIGN  = 1;
    /** @var int 签约类型 支出 */
    const CONTRACT_SIGN_TYPE_RENEW_SIGN  = 2;

    //------------ 合同管理 ----------------------------------------------------------
}