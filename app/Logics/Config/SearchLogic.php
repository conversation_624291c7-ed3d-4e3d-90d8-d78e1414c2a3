<?php
/**
 * 搜索逻辑
 */
namespace App\Logics\Config;

use App\Logics\BaseLogic;
use App\Models\Customer\AccountModel;
use App\Models\Customer\CustomerModel;
use App\Models\Product\AccountProductModel;
use App\Models\Product\ProductConfigItemModel;
use App\Models\Product\ProductConfigModel;
use App\Models\Product\ProductModel;
use App\Models\Product\SubProductConfigItemModel;
use App\Models\Product\SubProductConfigModel;
use App\Repositories\Customer\AccountRepositorie;
use App\Repositories\Customer\CustomerGroupRepositorie;
use App\Repositories\Customer\CustomerRepositorie;
use App\Repositories\Product\ProductConfigRepositorie;
use App\Repositories\Product\ProductRepositorie;
use App\Repositories\Product\SubProductConfigRepositorie;
use App\Repositories\Product\SubProductRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

class SearchLogic extends BaseLogic
{
    public static function detailList($params)
    {
        $return = ['code' => Enu::RESP_OK, 'data' => []];
        $detailType = isset($params['detailType']) ? $params['detailType'] : '';
        switch ($detailType) {
            case 'group':
                $return['data'] = self::getGroupDetailList($params);
                break;
            case 'customer':
                $return['data'] = self::getCusDetailList($params);
                break;
            case 'account':
                $return['data'] = self::getAccDetailList($params);
                break;
        }
        return $return;
    }

    //获取主体维度明细列表
    private static function getGroupDetailList($params)
    {
        $return = ['page' => $params['page'], 'pageSize' => $params['pageSize'], 'count' => 0, 'list' => []];

        $where = [];
        //指定主体
        if ($params['detailId']) {
            $where[] = ['group_id', '=', $params['detailId']];
        }

        $total = CustomerGroupRepositorie::getCustomerGroupCount($where);
        if (!$total) {
            return $return;
        }
        $return['count'] = $total;
        //获取主体列表
        $cusGroupInfos = CustomerGroupRepositorie::getCustomerGroupList($where, ['*'], $params['pageSize'],$params['page'], ['id', 'desc']);
        if (!$cusGroupInfos) {
            return $return;
        }

        $groupIds = array_column($cusGroupInfos, 'group_id');
        $groupMaps = array_column($cusGroupInfos, null, 'group_id');
        //获取主体下所有客户/账号信息
        $accCusInfos = AccountModel::getAccountCusGroupInfosByIds($groupIds);
        $accIds = array_values(array_filter(array_column($accCusInfos, 'account_id')));

        $wherePids = [];
        if ($params['fpid'] && !$params['pid']) {
            // $wherePids[] = $params['fpid'];
            $subPros = ProductModel::getSubProductsByFatherId($params['fpid']);
            $wherePids = array_merge($wherePids, array_column($subPros, 'product_id'));
        }
        if ($params['pid']) {
            $wherePids[] = $params['pid'];
        }
        //主体下所有开通的产品
        $accProInfos = AccountProductModel::getAccountProByPidsAccIds($wherePids, $accIds);
        $accProMap = [];
        foreach ($accProInfos as $item) {
            $accProMap[$item['account_id']][$item['product_id']] = $item;
        }

        //获取配置项明细
        $fpConItems = ProductConfigItemModel::getAllAvailableItemByPids();
        $pConItems = SubProductConfigItemModel::getAllAvailableItemByPids();
        $configItemMap = [];
        foreach ($fpConItems as $item) {
            $kk = "main_{$item['id']}";
            $configItemMap[$kk] = $item;
        }
        foreach ($pConItems as $item) {
            $kk = "sub_{$item['id']}";
            $configItemMap[$kk] = $item;
        }

        //获取主体-主产品配置
        $fpids = [];
        if ($params['fpid']) {
            $fpids[] = $params['fpid'];
        }
        $fpConfigInfos = ProductConfigRepositorie::getConfigWithGroupIdsPids($groupIds, $fpids);
        $fpConMaps = [];
        foreach ($fpConfigInfos as $item) {
            $tmpKey = "{$item['group_id']}_{$item['product_id']}";
            $tmpOptionKey = "main_{$item['config_item_id']}";
            $item['configOptionItem'] = $configItemMap[$tmpOptionKey];
            $fpConMaps[$tmpKey][] = $item;
        }


        //获取主体-子产品配置
        $pids = [];
        if ($params['pid']) {
            $pids[] = $params['pid'];
        }
        $pConfigInfos = SubProductConfigRepositorie::getConfigWithGroupIdsPids($groupIds,$pids);
        $pConMaps = [];
        foreach ($pConfigInfos as $item) {
            $tmpKey = "{$item['group_id']}_{$item['sub_product_id']}";
            $source = 'sub';
            if ($item['config_item_source'] == 1) {
                $source = 'main';
            }
            $tmpOptionKey = "{$source}_{$item['config_item_id']}";
            $item['configOptionItem'] = $configItemMap[$tmpOptionKey];
            $pConMaps[$tmpKey][] = $item;
        }

        //获取所有可用产品信息
        $allAvailablePros = ProductRepositorie::getAllProduct();
        $allPids = array_column($allAvailablePros, 'product_id');
        $allProInfosMap = array_column($allAvailablePros, null, 'product_id');

        //合并主体下对应的的产品
        $tmpDatas = [];
        foreach ($accCusInfos as $item) {
            if (!isset($tmpDatas[$item['group_id']])) {
                $tmpDatas[$item['group_id']] = [];
            }

            if (empty($accProMap[$item['account_id']])) {
                continue;
            }
            foreach ($accProMap[$item['account_id']] as $ttK => $ttI) {
                if (!in_array($ttK, $allPids)) {
                    continue;
                }
                $fatherId = empty($allProInfosMap[$ttK]['father_id']) ? $ttK : $allProInfosMap[$ttK]['father_id'];

                //主产品配置
                $tmpFatherKey = "{$item['group_id']}_$fatherId";
                if (!isset($tmpDatas[$item['group_id']][$fatherId])) {
                    $fpConfigItems = isset($fpConMaps[$tmpFatherKey]) ? $fpConMaps[$tmpFatherKey] : [];
                    $tmpDatas[$item['group_id']][$fatherId][$fatherId] = $fpConfigItems;
                }
                if ($ttK == $fatherId) {
                    continue;
                }
                //子产品配置
                $tmpSubKey = "{$item['group_id']}_{$ttK}";
                $pConfigItems = isset($pConMaps[$tmpSubKey]) ? $pConMaps[$tmpSubKey] : [];
                $tmpDatas[$item['group_id']][$fatherId][$ttK] = $pConfigItems;
            }
        }
        $data = [];
        foreach ($tmpDatas as $groupId => $productConfig) {
            $groupConfigCount = 0;
            $groupMergeCount = 0;
            foreach ($productConfig as $fatherId => $datas) {
                $fatherLineData = [];
                foreach ($datas as $ttPid => $ttCons) {
                    $groupMergeCount++;
                    $hasCon = true;
                    $productConfigCount = 0;
                    $productMergeCount = 1;
                    $tmpYuDatas = [];
                    $tmp = [
                        "detail_name" => $groupMaps[$groupId]['group_name'],
                        "product_name" => $allProInfosMap[$ttPid]['product_name'],
                        "detail_id" => $groupId,
                        "is_father" => $ttPid == $fatherId ? 1 : 0,
                        "fpid" => $fatherId,
                        "pid" => $ttPid,
                        "product_config_count" => $productConfigCount,
                        "product_merge_count" => $productMergeCount,
                        "op_merge_count" => $productMergeCount,
                        "config_name" => '',
                        "config_value" => '',
                        "is_online" => '',
                    ];
                    if ($ttCons) {
                        foreach ($ttCons as $item) {
                            $groupConfigCount++;
                            $groupMergeCount++;
                            $productConfigCount++;
                            $productMergeCount++;
                            if ($hasCon) {
                                $productMergeCount--;
                                $groupMergeCount--;
                                $hasCon = false;
                            }
                            $tmp['config_name'] = $item['configOptionItem']['item_name'];
                            $tmp['config_value'] = ConfigLogic::get_value($item['configOptionItem']['item_type'], $item['configOptionItem']['item_options'], $item['item_value']);
                            $tmp['is_online'] = '是';
                            $tmpYuDatas[] = $tmp;
                        }
                    } else {
                        $tmpYuDatas[] = $tmp;
                    }

                    foreach ($tmpYuDatas as $ttkk => $tmpItem) {
                        $tmpItem['product_config_count'] = $productConfigCount;
                        $tmpItem['product_merge_count'] = 0;
                        $tmpItem['op_merge_count'] = 0;
                        if ($ttkk == 0) {
                            $tmpItem['product_merge_count'] = $productMergeCount;
                            $tmpItem['op_merge_count'] = $productMergeCount;
                        }
                        $tmpYuDatas[$ttkk] = $tmpItem;
                    }
                    if (!isset($data[$groupId][$fatherId])) {
                        $data[$groupId][$fatherId] = [];
                    }
                    //主产品配置
                    if ($ttPid == $fatherId) {
                        $fatherLineData = $tmpYuDatas;
                        continue;
                    }
                    $data[$groupId][$fatherId] = array_merge($data[$groupId][$fatherId], $tmpYuDatas);
                }
                foreach ($data[$groupId][$fatherId] as $orDD) {
                    $fatherLineData[] = $orDD;
                }
                $data[$groupId][$fatherId] = $fatherLineData;
            }
            $data[$groupId]['groupConfigCount'] = $groupConfigCount;
            $data[$groupId]['groupMergeCount'] = $groupMergeCount;
        }
        $list = [];
        foreach ($data as $ttyData) {
            $tgroupConfigCount = $ttyData['groupConfigCount'];
            $tgroupMergeCount = $ttyData['groupMergeCount'];
            unset($ttyData['groupConfigCount']);
            unset($ttyData['groupMergeCount']);
            $oook = 0;
            foreach ($ttyData as $uiuiDatas) {
                // $tmpSort = array_column($uiuiDatas, 'is_father');
                // array_multisort($tmpSort, SORT_DESC, $uiuiDatas);
                foreach ($uiuiDatas as $item) {
                    $item['detail_config_count'] = $tgroupConfigCount;
                    $item['detail_merge_count'] = 0;
                    if ($oook == 0) {
                        $oook++;
                        $item['detail_merge_count'] = $tgroupMergeCount;
                    }
                    $list[] = $item;
                }
            }
        }
        $return['list'] = $list;
        return $return;
    }

    //获取客户维度明细列表
    private static function getCusDetailList($params)
    {
        $return = ['page' => $params['page'], 'pageSize' => $params['pageSize'], 'count' => 0, 'list' => []];

        $where = [];
        //指定客户
        if ($params['detailId']) {
            $where[] = ['customer_id', '=', $params['detailId']];
        }

        $total = CustomerRepositorie::getCustomerCount($where);
        if (!$total) {
            return $return;
        }
        $return['count'] = $total;
        //获取客户列表
        $cusGroupInfos = CustomerRepositorie::getCustomerList($where, ['*'], $params['pageSize'],$params['page'], ['id', 'desc']);

        if (!$cusGroupInfos) {
            return $return;
        }

        $groupIds = array_column($cusGroupInfos, 'customer_id');
        $groupMaps = array_column($cusGroupInfos, null, 'customer_id');
        //获取主体下所有客户/账号信息
        $accCusInfos = AccountModel::getAccountCusGroupInfosByIds([], $groupIds);
        $accIds = array_values(array_filter(array_column($accCusInfos, 'account_id')));

        $wherePids = [];
        if ($params['fpid'] && !$params['pid']) {
            // $wherePids[] = $params['fpid'];
            $subPros = ProductModel::getSubProductsByFatherId($params['fpid']);
            $wherePids = array_merge($wherePids, array_column($subPros, 'product_id'));
        }
        if ($params['pid']) {
            $wherePids[] = $params['pid'];
        }
        //主体下所有开通的产品
        $accProInfos = AccountProductModel::getAccountProByPidsAccIds($wherePids, $accIds);
        $accProMap = [];
        foreach ($accProInfos as $item) {
            $accProMap[$item['account_id']][$item['product_id']] = $item;
        }

        //获取配置项明细
        $fpConItems = ProductConfigItemModel::getAllAvailableItemByPids();
        $pConItems = SubProductConfigItemModel::getAllAvailableItemByPids();
        $configItemMap = [];
        foreach ($fpConItems as $item) {
            $kk = "main_{$item['id']}";
            $configItemMap[$kk] = $item;
        }
        foreach ($pConItems as $item) {
            $kk = "sub_{$item['id']}";
            $configItemMap[$kk] = $item;
        }

        //获取主体-主产品配置
        $fpids = [];
        if ($params['fpid']) {
            $fpids[] = $params['fpid'];
        }
        $fpConfigInfos = ProductConfigRepositorie::getConfigWithCusIdsPids($groupIds, $fpids);
        $fpConMaps = [];
        foreach ($fpConfigInfos as $item) {
            $tmpKey = "{$item['customer_id']}_{$item['product_id']}";
            $tmpOptionKey = "main_{$item['config_item_id']}";
            $item['configOptionItem'] = $configItemMap[$tmpOptionKey];
            $fpConMaps[$tmpKey][] = $item;
        }


        //获取主体-子产品配置
        $pids = [];
        if ($params['pid']) {
            $pids[] = $params['pid'];
        }
        $pConfigInfos = SubProductConfigRepositorie::getConfigWithCusIdsPids($groupIds,$pids);
        $pConMaps = [];
        foreach ($pConfigInfos as $item) {
            $tmpKey = "{$item['customer_id']}_{$item['sub_product_id']}";
            $source = 'sub';
            if ($item['config_item_source'] == 1) {
                $source = 'main';
            }
            $tmpOptionKey = "{$source}_{$item['config_item_id']}";
            $item['configOptionItem'] = $configItemMap[$tmpOptionKey];
            $pConMaps[$tmpKey][] = $item;
        }

        //获取所有可用产品信息
        $allAvailablePros = ProductRepositorie::getAllProduct();
        $allPids = array_column($allAvailablePros, 'product_id');
        $allProInfosMap = array_column($allAvailablePros, null, 'product_id');

        //合并主体下对应的的产品
        $tmpDatas = [];
        foreach ($accCusInfos as $item) {
            if (!isset($tmpDatas[$item['customer_id']])) {
                $tmpDatas[$item['customer_id']] = [];
            }

            if (empty($accProMap[$item['account_id']])) {
                continue;
            }
            foreach ($accProMap[$item['account_id']] as $ttK => $ttI) {
                if (!in_array($ttK, $allPids)) {
                    continue;
                }
                $fatherId = empty($allProInfosMap[$ttK]['father_id']) ? $ttK : $allProInfosMap[$ttK]['father_id'];

                //主产品配置
                $tmpFatherKey = "{$item['customer_id']}_$fatherId";
                if (!isset($tmpDatas[$item['customer_id']][$fatherId])) {
                    $fpConfigItems = isset($fpConMaps[$tmpFatherKey]) ? $fpConMaps[$tmpFatherKey] : [];
                    $tmpDatas[$item['customer_id']][$fatherId][$fatherId] = $fpConfigItems;
                }
                if ($ttK == $fatherId) {
                    continue;
                }
                //子产品配置
                $tmpSubKey = "{$item['customer_id']}_{$ttK}";
                $pConfigItems = isset($pConMaps[$tmpSubKey]) ? $pConMaps[$tmpSubKey] : [];
                $tmpDatas[$item['customer_id']][$fatherId][$ttK] = $pConfigItems;
            }
        }
        $data = [];
        foreach ($tmpDatas as $groupId => $productConfig) {
            $groupConfigCount = 0;
            $groupMergeCount = 0;
            foreach ($productConfig as $fatherId => $datas) {
                $fatherLineData = [];
                foreach ($datas as $ttPid => $ttCons) {
                    $groupMergeCount++;
                    $hasCon = true;
                    $productConfigCount = 0;
                    $productMergeCount = 1;
                    $tmpYuDatas = [];
                    $tmp = [
                        "detail_name" => $groupMaps[$groupId]['name'],
                        "product_name" => $allProInfosMap[$ttPid]['product_name'],
                        "detail_id" => $groupId,
                        "is_father" => $ttPid == $fatherId ? 1 : 0,
                        "fpid" => $fatherId,
                        "pid" => $ttPid,
                        "product_config_count" => $productConfigCount,
                        "product_merge_count" => $productMergeCount,
                        "op_merge_count" => $productMergeCount,
                        "config_name" => '',
                        "config_value" => '',
                        "is_online" => '',
                    ];
                    if ($ttCons) {
                        foreach ($ttCons as $item) {
                            $groupConfigCount++;
                            $groupMergeCount++;
                            $productConfigCount++;
                            $productMergeCount++;
                            if ($hasCon) {
                                $productMergeCount--;
                                $groupMergeCount--;
                                $hasCon = false;
                            }
                            $tmp['config_name'] = $item['configOptionItem']['item_name'];
                            $tmp['config_value'] = ConfigLogic::get_value($item['configOptionItem']['item_type'], $item['configOptionItem']['item_options'], $item['item_value']);
                            $tmp['is_online'] = '是';
                            $tmpYuDatas[] = $tmp;
                        }
                    } else {
                        $tmpYuDatas[] = $tmp;
                    }

                    foreach ($tmpYuDatas as $ttkk => $tmpItem) {
                        $tmpItem['product_config_count'] = $productConfigCount;
                        $tmpItem['product_merge_count'] = 0;
                        $tmpItem['op_merge_count'] = 0;
                        if ($ttkk == 0) {
                            $tmpItem['product_merge_count'] = $productMergeCount;
                            $tmpItem['op_merge_count'] = $productMergeCount;
                        }
                        $tmpYuDatas[$ttkk] = $tmpItem;
                    }
                    if (!isset($data[$groupId][$fatherId])) {
                        $data[$groupId][$fatherId] = [];
                    }
                    //主产品配置
                    if ($ttPid == $fatherId) {
                        $fatherLineData = $tmpYuDatas;
                        continue;
                    }
                    $data[$groupId][$fatherId] = array_merge($data[$groupId][$fatherId], $tmpYuDatas);
                }
                foreach ($data[$groupId][$fatherId] as $orDD) {
                    $fatherLineData[] = $orDD;
                }
                $data[$groupId][$fatherId] = $fatherLineData;
            }
            $data[$groupId]['groupConfigCount'] = $groupConfigCount;
            $data[$groupId]['groupMergeCount'] = $groupMergeCount;
        }
        $list = [];
        foreach ($data as $ttyData) {
            $tgroupConfigCount = $ttyData['groupConfigCount'];
            $tgroupMergeCount = $ttyData['groupMergeCount'];
            unset($ttyData['groupConfigCount']);
            unset($ttyData['groupMergeCount']);
            $oook = 0;
            foreach ($ttyData as $uiuiDatas) {
                // $tmpSort = array_column($uiuiDatas, 'is_father');
                // array_multisort($tmpSort, SORT_DESC, $uiuiDatas);
                foreach ($uiuiDatas as $item) {
                    $item['detail_config_count'] = $tgroupConfigCount;
                    $item['detail_merge_count'] = 0;
                    if ($oook == 0) {
                        $oook++;
                        $item['detail_merge_count'] = $tgroupMergeCount;
                    }
                    $list[] = $item;
                }
            }
        }
        $return['list'] = $list;
        return $return;
    }

    //获取账号维度明细列表
    private static function getAccDetailList($params)
    {
        $return = ['page' => $params['page'], 'pageSize' => $params['pageSize'], 'count' => 0, 'list' => []];

        $where = [];
        //指定客户
        if ($params['detailId']) {
            $where[] = ['account_id', '=', $params['detailId']];
        }

        $total = AccountModel::getAllCount($where);
        if (!$total) {
            return $return;
        }
        $return['count'] = $total;
        //获取客户列表
        $cusGroupInfos = AccountModel::getList($where, ['*'], $params['pageSize'],$params['page'], ['id', 'desc']);

        if (!$cusGroupInfos) {
            return $return;
        }

        $groupIds = array_column($cusGroupInfos, 'account_id');
        $groupMaps = array_column($cusGroupInfos, null, 'account_id');
        //获取主体下所有客户/账号信息
        $accCusInfos = $cusGroupInfos;
        $accIds = $groupIds;

        $wherePids = [];
        if ($params['fpid'] && !$params['pid']) {
            // $wherePids[] = $params['fpid'];
            $subPros = ProductModel::getSubProductsByFatherId($params['fpid']);
            $wherePids = array_merge($wherePids, array_column($subPros, 'product_id'));
        }
        if ($params['pid']) {
            $wherePids[] = $params['pid'];
        }

        //主体下所有开通的产品
        $accProInfos = AccountProductModel::getAccountProByPidsAccIds($wherePids, $accIds);

        $accProMap = [];
        foreach ($accProInfos as $item) {
            $accProMap[$item['account_id']][$item['product_id']] = $item;
        }

        //获取配置项明细
        $fpConItems = ProductConfigItemModel::getAllAvailableItemByPids();
        $pConItems = SubProductConfigItemModel::getAllAvailableItemByPids();
        $configItemMap = [];
        foreach ($fpConItems as $item) {
            $kk = "main_{$item['id']}";
            $configItemMap[$kk] = $item;
        }
        foreach ($pConItems as $item) {
            $kk = "sub_{$item['id']}";
            $configItemMap[$kk] = $item;
        }

        //获取主体-主产品配置
        $fpids = [];
        if ($params['fpid']) {
            $fpids[] = $params['fpid'];
        }
        $fpConfigInfos = ProductConfigRepositorie::getConfigWithAccountIdsPids($groupIds, $fpids);
        $fpConMaps = [];
        foreach ($fpConfigInfos as $item) {
            $tmpKey = "{$item['account_id']}_{$item['product_id']}";
            $tmpOptionKey = "main_{$item['config_item_id']}";
            $item['configOptionItem'] = $configItemMap[$tmpOptionKey];
            $fpConMaps[$tmpKey][] = $item;
        }


        //获取主体-子产品配置
        $pids = [];
        if ($params['pid']) {
            $pids[] = $params['pid'];
        }
        $pConfigInfos = SubProductConfigRepositorie::getConfigWithAccountIdsPids($groupIds,$pids);
        $pConMaps = [];
        foreach ($pConfigInfos as $item) {
            $tmpKey = "{$item['account_id']}_{$item['sub_product_id']}";
            $source = 'sub';
            if ($item['config_item_source'] == 1) {
                $source = 'main';
            }
            $tmpOptionKey = "{$source}_{$item['config_item_id']}";
            $item['configOptionItem'] = $configItemMap[$tmpOptionKey];
            $pConMaps[$tmpKey][] = $item;
        }

        //获取所有可用产品信息
        $allAvailablePros = ProductRepositorie::getAllProduct();
        $allPids = array_column($allAvailablePros, 'product_id');
        $allProInfosMap = array_column($allAvailablePros, null, 'product_id');

        //合并主体下对应的的产品
        $tmpDatas = [];
        foreach ($accCusInfos as $item) {
            if (!isset($tmpDatas[$item['account_id']])) {
                $tmpDatas[$item['account_id']] = [];
            }

            if (empty($accProMap[$item['account_id']])) {
                continue;
            }
            foreach ($accProMap[$item['account_id']] as $ttK => $ttI) {
                if (!in_array($ttK, $allPids)) {
                    continue;
                }
                $fatherId = empty($allProInfosMap[$ttK]['father_id']) ? $ttK : $allProInfosMap[$ttK]['father_id'];

                //主产品配置
                $tmpFatherKey = "{$item['account_id']}_$fatherId";
                if (!isset($tmpDatas[$item['account_id']][$fatherId])) {
                    $fpConfigItems = isset($fpConMaps[$tmpFatherKey]) ? $fpConMaps[$tmpFatherKey] : [];
                    $tmpDatas[$item['account_id']][$fatherId][$fatherId] = $fpConfigItems;
                }
                if ($ttK == $fatherId) {
                    continue;
                }
                //子产品配置
                $tmpSubKey = "{$item['account_id']}_{$ttK}";
                $pConfigItems = isset($pConMaps[$tmpSubKey]) ? $pConMaps[$tmpSubKey] : [];
                $tmpDatas[$item['account_id']][$fatherId][$ttK] = $pConfigItems;
            }
        }
        $data = [];
        foreach ($tmpDatas as $groupId => $productConfig) {
            $groupConfigCount = 0;
            $groupMergeCount = 0;
            foreach ($productConfig as $fatherId => $datas) {
                $fatherLineData = [];
                foreach ($datas as $ttPid => $ttCons) {
                    $groupMergeCount++;
                    $hasCon = true;
                    $productConfigCount = 0;
                    $productMergeCount = 1;
                    $tmpYuDatas = [];
                    $tmp = [
                        "detail_name" => $groupMaps[$groupId]['account_name'],
                        "product_name" => $allProInfosMap[$ttPid]['product_name'],
                        "detail_id" => $groupId,
                        "is_father" => $ttPid == $fatherId ? 1 : 0,
                        "fpid" => $fatherId,
                        "pid" => $ttPid,
                        "product_config_count" => $productConfigCount,
                        "product_merge_count" => $productMergeCount,
                        "op_merge_count" => $productMergeCount,
                        "config_name" => '',
                        "config_value" => '',
                        "is_online" => '',
                    ];
                    if ($ttCons) {
                        foreach ($ttCons as $item) {
                            $groupConfigCount++;
                            $groupMergeCount++;
                            $productConfigCount++;
                            $productMergeCount++;
                            if ($hasCon) {
                                $productMergeCount--;
                                $groupMergeCount--;
                                $hasCon = false;
                            }
                            $tmp['config_name'] = $item['configOptionItem']['item_name'];
                            $tmp['config_value'] = ConfigLogic::get_value($item['configOptionItem']['item_type'], $item['configOptionItem']['item_options'], $item['item_value']);
                            $tmp['is_online'] = '是';
                            $tmpYuDatas[] = $tmp;
                        }
                    } else {
                        $tmpYuDatas[] = $tmp;
                    }

                    foreach ($tmpYuDatas as $ttkk => $tmpItem) {
                        $tmpItem['product_config_count'] = $productConfigCount;
                        $tmpItem['product_merge_count'] = 0;
                        $tmpItem['op_merge_count'] = 0;
                        if ($ttkk == 0) {
                            $tmpItem['product_merge_count'] = $productMergeCount;
                            $tmpItem['op_merge_count'] = $productMergeCount;
                        }
                        $tmpYuDatas[$ttkk] = $tmpItem;
                    }
                    if (!isset($data[$groupId][$fatherId])) {
                        $data[$groupId][$fatherId] = [];
                    }
                    //主产品配置
                    if ($ttPid == $fatherId) {
                        $fatherLineData = $tmpYuDatas;
                        continue;
                    }
                    $data[$groupId][$fatherId] = array_merge($data[$groupId][$fatherId], $tmpYuDatas);
                }
                foreach ($data[$groupId][$fatherId] as $orDD) {
                    $fatherLineData[] = $orDD;
                }
                $data[$groupId][$fatherId] = $fatherLineData;
            }
            $data[$groupId]['groupConfigCount'] = $groupConfigCount;
            $data[$groupId]['groupMergeCount'] = $groupMergeCount;
        }
        $list = [];
        foreach ($data as $ttyData) {
            $tgroupConfigCount = $ttyData['groupConfigCount'];
            $tgroupMergeCount = $ttyData['groupMergeCount'];
            unset($ttyData['groupConfigCount']);
            unset($ttyData['groupMergeCount']);
            $oook = 0;
            foreach ($ttyData as $uiuiDatas) {
                // $tmpSort = array_column($uiuiDatas, 'is_father');
                // array_multisort($tmpSort, SORT_DESC, $uiuiDatas);
                foreach ($uiuiDatas as $item) {
                    $item['detail_config_count'] = $tgroupConfigCount;
                    $item['detail_merge_count'] = 0;
                    if ($oook == 0) {
                        $oook++;
                        $item['detail_merge_count'] = $tgroupMergeCount;
                    }
                    $list[] = $item;
                }
            }
        }
        $return['list'] = $list;
        return $return;
    }


    /**
     * 获取存在配置的客户列表
     * 1. 通过条件筛选客户id
     *  1.0 根据主产品,子产品参数,计算对应的主产品,子产品id
     *  1.1 查询配置项名称对应的配置项key
     * 2. 根据条件查询客户id并进行分页 查询对应配置;剔除无配置账号
     * 3. 查询配置项
     * 4. 获取相关配置
     * 5. 底层配置值覆盖上层配置值,区分主产品,子产品配置项
     * 6. 展开配置 最终每条都是账号-主产品,账号主产品,每个配置占一条 [['a1' => configs['c1','c2']]] => [['a1' => 'c1'],['a1' => 'c2']]
     * 7. 整理数据:调整展示文本;处理单元格合并
     *
     * @param $group_id
     * @param $customer_id
     * @param $account_id
     * @param $product_id
     * @param $sub_product_id
     * @param $item_name
     * @param $config_path
     * @param $page
     * @param $page_size
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2023-10-31 18:51:25
     *
     * @static
     */
    public static function get_have_config_customer_list($group_id,$customer_id,$account_id,$product_id,$sub_product_id,$item_name,$config_path,$page,$page_size){
        $res = [
            'page'      => $page,
            'page_size' => $page_size,
            'count'     => 0,
            'list'      => [],
        ];
        //1.0
        if(empty($product_id) && !empty($sub_product_id)){
            $origin_product_id = ProductModel::getFatherId($sub_product_id);
            $sub_product_ids = [$sub_product_id];
            $product_ids = [$origin_product_id];
        }else if(!empty($product_id) && empty($sub_product_id)) {
            $sub_product_id_arr = ProductModel::getSubProductsByFatherId($product_id);
            $sub_product_ids = array_column($sub_product_id_arr, 'product_id');
            $product_ids = [$product_id];
        }else if(!empty($product_id) && !empty($sub_product_id)) {
            $sub_product_ids = [$sub_product_id];
            $product_ids = [$product_id];
        }else{
            $_products = ProductModel::getFatherProduct();
            $product_ids = array_column($_products, 'product_id');
            $sub_product_ids = ProductModel::getSubProductsByFatherIds($product_ids);
            $sub_product_ids = array_column($sub_product_ids, 'product_id');
        }
        //1.1 根据配置项名获取配置项id,配置项名可能会重复,如果主产品或子产品id为空,则查询所有产品
        $product_item_ids     = ProductConfigItemModel::getListByItemNameAndProductId($item_name, $product_id);
        $product_item_ids     = array_column($product_item_ids, 'id');
        $sub_product_item_ids = SubProductConfigItemModel::getListByItemNameAndSubProductId($item_name, $sub_product_id);
        $sub_product_item_ids = array_column($sub_product_item_ids, 'id');

        //2 根据条件获取客户id
        $customer_ids = self::get_customer_by_param($group_id,$customer_id,$account_id,$product_ids,$sub_product_ids,$config_path,$product_item_ids,$sub_product_item_ids);
        if(empty($customer_ids)){
            return $res;
        }

        //根据客户id获取相关信息
        //对应的产品,主体,客户和依据主体-客户-账号-主产品-子产品排序的数组,最终返回的列表在这个数组上修改
        $_sub_product_id_arr = array_merge($sub_product_ids,$product_ids);
        $customer_infos = self::get_customer_infos($customer_ids,$account_id,$_sub_product_id_arr,$page_size,$page);
        $res['count'] = $customer_infos['count'];//分页用
        // $sub_product_list = $customer_infos['sub_product_list'];
        // $product_ids      = $customer_infos['product_ids'];
        $group_ids        = $customer_infos['group_ids'];
        $account_ids      = $customer_infos['account_ids'];
        $res_list         = $customer_infos['res_list'];
        //3 查询配置项
        list($product_config_item_list,$sub_product_config_item_list,$product_item_ids,$sub_product_item_ids) = self::get_config_items($product_ids,$sub_product_ids,$item_name);
        //4 根据查询出的主体,客户,账号获取相关配置
        $config_arr = self::get_configs($group_ids,$customer_ids,$account_ids,$product_ids,$sub_product_ids,$product_item_ids,$sub_product_item_ids,$config_path,$product_config_item_list,$sub_product_config_item_list,$sub_product_id);
        $group_config_count    = $config_arr['group_config_count'];
        $customer_config_count = $config_arr['customer_config_count'];
        $account_config_count  = $config_arr['account_config_count'];
        //5 将不同维度(配置路径)的配置信息按顺序排列,达到底层配置值覆盖上层配置值
        foreach($config_arr['configs'] as $lv => $some_lv_configs){
            if(empty($some_lv_configs)){//这层没有配置
                continue;
            }
            foreach($res_list as &$account_info){
                $k = self::get_lv_key($lv,$account_info);
                if(!isset($some_lv_configs[$k])){//没有对应配置路径的配置
                    continue;
                }
                foreach($some_lv_configs[$k] as $id_source => $config){
                    if(empty($product_ids) && $account_info['is_father_product'] ){
                        continue;
                    }
                    if(isset($config['extend'])) {
                        //如果能被继承则不展示在主产品上
                        if ($account_info['is_father_product'] && $config['extend'] == Enu::PRODUCT_CONFIG_ITEM_SUB_CAN_EXTEND) {
                            continue;
                        }
                        //不能被继承不展示在子产品上
                        if (!$account_info['is_father_product'] && $config['extend'] == Enu::PRODUCT_CONFIG_ITEM_SUB_CAN_NOT_EXTEND) {
                            continue;
                        }
                    }
                    $account_info['configs'][$id_source]['config_item_source'] = $config['config_item_source'];
                    $account_info['configs'][$id_source]['config_item_id']     = $config['config_item_id'];
                    $account_info['configs'][$id_source]['value']              = $config['item_value'];
                    $account_info['configs'][$id_source]['path']               = $lv;
                    $account_info['configs'][$id_source]['extend']             = isset($config['extend']) ? $config['extend'] : '';
                }
            }
        }
        $_res_list = [];
        $configs_product_father_ids = $configs_product_product_ids = [];//拥有配置的主产品id,子产品id
        $path_map = config('options.config_path');
        //6 展开多个配置并补充配置相关信息
        foreach($res_list as $info){
            $info['is'] = $info['value'] = $info['config_path'] = $info['extend'] = $info['config_item_id'] = $info['config_item_source'] = '';
            if($info['product_id'] == 0){
                continue;
            }
            if(isset($info['configs'])){
                $configs = $info['configs'];
                $_info = $info;

                $flag = false;
                unset($_info['configs']);
                foreach($configs as $is => $c){
                    $_info['is']                 = $is;
                    $_info['value']              = $c['value'];
                    $_info['config_path']        = $c['path'];
                    $_info['extend']             = $c['extend'];
                    $_info['config_item_id']     = $c['config_item_id'];
                    $_info['config_item_source'] = $c['config_item_source'];
                    $item_info = self::get_item_name($c['config_item_source'], $c['config_item_id'], $info['father_id'], $info['product_id'], $info['is_father_product'], $c['value'], $product_config_item_list, $sub_product_config_item_list);
                    $_info['config_item_name']  = $item_info['item_name'];
                    $_info['config_item_value'] = $item_info['item_value'];
                    $_info['config_path_name']  = isset($path_map[$c['path']])?$path_map[$c['path']]:'';
                    if(!empty($item_name) && $item_name != $item_info['item_name']){
                        continue;
                    }
                    $_res_list[] = $_info;
                    $flag = true;
                }
                if($flag) {
                    $configs_product_product_ids[$info['account_id']][$info['product_id']] = $info['product_id'];
                    $configs_product_father_ids[$info['account_id']][$info['father_id']]   = $info['father_id'];
                }else{
                    if($info['is_father_product']){
                        $_res_list[] = $info;
                    }
                }
            }else{
                $_res_list[] = $info;
            }
        }
        $res_list = [];
        //7.1 填充配置项名称,配置项值
        foreach($_res_list as $rl_info) {
            if($rl_info['is_father_product'] && !isset($configs_product_father_ids[$rl_info['account_id']][$rl_info['product_id']])){
                continue;
            }
            if((isset($configs_product_father_ids[$rl_info['account_id']][$rl_info['product_id']]) || isset($configs_product_product_ids[$rl_info['account_id']][$rl_info['product_id']])) || !empty($rl_info['config_item_id'])) {
                $res_list[] = $rl_info;
            }
        }

        //7.2 处理合并单元格,配置数量
        $group_count = $customer_count = $account_count = $product_count = [];//主体 客户 账号 产品 合并单元格数量

        foreach($res_list as $rl_info){
            $group_count[$rl_info['group_id']]       = key_exists($rl_info['group_id'], $group_count) ? $group_count[$rl_info['group_id']] + 1 : 1;
            $customer_count[$rl_info['customer_id']] = key_exists($rl_info['customer_id'], $customer_count) ? $customer_count[$rl_info['customer_id']] + 1 : 1;
            $account_count[$rl_info['account_id']]   = key_exists($rl_info['account_id'], $account_count) ? $account_count[$rl_info['account_id']] + 1 : 1;

            $p_k = get_tmp_key($rl_info['group_id'],$rl_info['customer_id'],$rl_info['account_id'],$rl_info['product_id']);
            $product_count[$p_k] = key_exists($p_k, $product_count) ? $product_count[$p_k] + 1 : 1;
        }

        foreach($res_list as &$rl_info){
            $rl_info['customer_merge_count'] = $customer_count[$rl_info['customer_id']];
            if ($customer_count[$rl_info['customer_id']] > 0) {
                $customer_count[$rl_info['customer_id']] = 0;
            }
            $rl_info['account_merge_count'] = $account_count[$rl_info['account_id']];
            if ($account_count[$rl_info['account_id']] > 0) {
                $account_count[$rl_info['account_id']] = 0;
            }

            $p_k = get_tmp_key($rl_info['group_id'],$rl_info['customer_id'],$rl_info['account_id'],$rl_info['product_id']);
            $rl_info['product_merge_count'] = $product_count[$p_k];
            if ($product_count[$p_k] > 0) {
                $product_count[$p_k] = 0;
            }

            $rl_info['group_config_count']    = isset($group_config_count[$rl_info['group_id']])? $group_config_count[$rl_info['group_id']]:0;
            $rl_info['customer_config_count'] = isset($customer_config_count[$rl_info['customer_id']])? $customer_config_count[$rl_info['customer_id']]:0;
            $rl_info['account_config_count']  = isset($account_config_count[$rl_info['account_id']])? $account_config_count[$rl_info['account_id']]:0;

            if (!empty($rl_info['group_id'])) {
                $rl_info['group_merge_count'] = $group_count[$rl_info['group_id']];
                if ($group_count[$rl_info['group_id']] > 0) {
                    $group_count[$rl_info['group_id']] = 0;
                }
            }else{
                $rl_info['group_name'] = '--';
                $rl_info['group_merge_count'] = $rl_info['customer_merge_count'];
            }
        }
        $res['list'] = $res_list;

        return $res;
    }


    /**
     * 获取配置项名称,配置值
     *
     * @param $config_item_source
     * @param $config_item_id
     * @param $product_id
     * @param $sub_product_id
     * @param $is_father_product
     * @param $item_value
     * @param $product_config_item_list
     * @param $sub_product_config_item_list
     *
     * @return array|string[]
     * <AUTHOR> 2023-10-12 15:01:48
     */
    private static function get_item_name($config_item_source, $config_item_id, $product_id, $sub_product_id,$is_father_product,$item_value,$product_config_item_list,$sub_product_config_item_list) {
        $res = [
            'item_name'  => '',
            'item_value' => '',
        ];
        if($is_father_product){
            $item_k = get_tmp_key('1',$config_item_id);
            $item_info = isset($product_config_item_list[$product_id][$item_k])?$product_config_item_list[$product_id][$item_k]:[];
        }else{
            if($config_item_source > 0) {
                $item_k = get_tmp_key($config_item_source,$config_item_id);
                if($config_item_source == Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT) {
                    $item_info = isset($product_config_item_list[$product_id][$item_k])?$product_config_item_list[$product_id][$item_k]:[];
                }else{
                    $item_info = isset($sub_product_config_item_list[$sub_product_id][$item_k])?$sub_product_config_item_list[$sub_product_id][$item_k]:[];
                }
            }else{
                $item_info = [];
            }
        }
        if(empty($item_info)){
            return $res;
        }

        $res['item_value'] = ConfigLogic::get_value($item_info['item_type'], $item_info['item_options'], $item_value);
        $res['item_name'] = $item_info['item_name'];

        return $res;
    }


    /**
     * 根据条件获取客户配置客户id
     *
     * @param $group_id
     * @param $customer_id
     * @param $account_id
     * @param $product_id
     * @param $sub_product_id_arr
     * @param $config_path
     * @param $product_item_ids
     * @param $sub_product_item_ids
     *
     * @return array
     * @throws Exception
     * @static
     * <AUTHOR> 2023-11-02 16:25:57
     */
    private static function get_customer_by_param($group_id,$customer_id,$account_id,$product_id,$sub_product_id_arr,$config_path,$product_item_ids,$sub_product_item_ids){
        $res_1 = SubProductConfigModel::getConfigList($sub_product_id_arr,$group_id,$customer_id,$account_id,$config_path,Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_SUB,$sub_product_item_ids);
        $res_2 = SubProductConfigModel::getConfigList($sub_product_id_arr,$group_id,$customer_id,$account_id,$config_path,Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT,$product_item_ids);
        $res_0 = ProductConfigModel::getConfigList($product_id,$group_id,$customer_id,$account_id,$config_path,$product_item_ids);

        $res = [$res_1,$res_2,$res_0];
        $group_ids = $customer_ids = $account_ids = [];
        foreach($res as $_res){
            foreach($_res as $__res) {
                if(!empty($__res['group_id'])) {
                    $group_ids[$__res['group_id']] = $__res['group_id'];
                }
                if(!empty($__res['customer_id'])) {
                    $customer_ids[$__res['customer_id']] = $__res['customer_id'];
                }
                if(!empty($__res['account_id'])) {
                    $account_ids[$__res['account_id']] = $__res['account_id'];
                }

            }
        }
        if(!empty($group_id)){
            $group_ids[$group_id] = $group_id;
        }

        if(!empty($customer_id)){
            $customer_ids[$customer_id] = $customer_id;
        }

        if(!empty($account_id)){
            $account_ids[$account_id] = $account_id;
        }

        //处理主体不为空时 客户id
        if(!empty($group_ids)){
            $customer_ids = CustomerModel::getListByGroupIds($group_ids);
            $customer_ids = array_column($customer_ids, 'customer_id');
        }

        if(!empty($customer_ids) && !empty($customer_id)){
            $customer_ids = [$customer_id];
        }

        //处理账号不为空时 客户id
        if(!empty($account_ids) && !empty($account_id)){
            $customer_ids = AccountModel::getListByAccountIds($account_ids);
            $customer_ids = array_column($customer_ids, 'customer_id');
        }

        return $customer_ids;
    }


    /**
     * 获取客户列表
     *
     * @param array $customer_id_arr
     * @param       $account_id
     * @param       $sub_product_ids
     * @param       $page_size
     * @param       $page
     *
     * @return array
     * @throws Exception
     * @static
     * <AUTHOR> 2023-11-08 12:03:51
     */
    private static function get_customer_infos(array $customer_id_arr,$account_id,$sub_product_ids,$page_size,$page) {
        //1 获取列表
        $custoemr_list  = CustomerModel::getCustomerListByCustomerIds($customer_id_arr, ['*'], $page_size, $page, ['group_id', 'desc']);
        $custoemr_count = CustomerModel::getCustomerCountByCustomerIds($customer_id_arr);

        $group_ids = array_unique(array_filter(array_column($custoemr_list, 'group_id')));

        $group_list = CustomerGroupRepositorie::getCustomerGroupListByGrouupIds($group_ids);
        $group_list = array_column($group_list, null, 'group_id');

        $customer_ids = array_column($custoemr_list, 'customer_id');

        //1.2 获取客户的账号
        $_account_list = AccountRepositorie::getListWithCustomers($customer_ids,$account_id);
        $account_names = [];
        $account_list  = [];
        $account_ids   = [];
        foreach ($_account_list as $account_info) {
            $_customer_id                               = $account_info['customer_id'];
            $account_ids[]                              = $account_info['account_id'];
            $account_names[$account_info['account_id']] = $account_info['account_name'];
            if (!key_exists($_customer_id, $account_list)) {
                $account_list[$_customer_id] = [];
            }
            $account_list[$_customer_id][] = $account_info;
        }

        //1.3 获取账号关联的产品 子产品
        $_account_product_list = AccountProductModel::getListWithAccountsAndProduts($account_ids,$sub_product_ids);
        $product_ids = [];
        $account_product_list = [];
        foreach($_account_product_list as $ap_info){
            $product_ids[] = $ap_info['product_id'];
            $_account_id = $ap_info['account_id'];
            if(!key_exists($_account_id, $account_product_list)){
                $account_product_list[$_account_id] = [];
            }
            $account_product_list[$_account_id][] = $ap_info;
        }
        //1.5 子产品信息
        $sub_product_list = SubProductRepositorie::getSubProductListByProductIds($product_ids);
        $father_ids = array_column($sub_product_list, 'father_id');
        $product_ids = array_merge($product_ids,$father_ids);
        $sub_product_list = array_column($sub_product_list, null, 'product_id');

        //1.4 主产品信息
        $product_list = ProductRepositorie::getMainProductListByProductIds($product_ids);
        $product_list = array_column($product_list, null, 'product_id');
        $res_list = [];
        foreach ($custoemr_list as $c_info){
            $customer_id = $c_info['customer_id'];
            $_info = [
                'group_id'          => $c_info['group_id'],
                'group_name'        => empty($c_info['group_id']) ? '' : $group_list[$c_info['group_id']]['group_name'],
                'customer_id'       => $c_info['customer_id'],
                'customer_name'     => $c_info['name'],
            ];
            if(!key_exists($customer_id,$account_list)){
                $_info['account_id'] = '';
                $_info['account_name'] = '';
                $_info['product_list'] = [];
                $res_list[] = $_info;
            }else{
                foreach($account_list[$customer_id] as $account_info){//客户
                    $account_id = $account_info['account_id'];
                    $_info['account_id']   = $account_id;
                    $_info['account_name'] = $account_names[$account_id];
                    if(!key_exists($account_id, $account_product_list)){
                        $_info['product_list'] = [];
                    }else{
                        $c_info_product_list = [];
                        foreach ($account_product_list[$account_id] as $ap_info){//产品
                            $product_id = $ap_info['product_id'];
                            if(key_exists($product_id, $product_list)){
                                $c_info_product_name = $product_list[$product_id]['product_name'];
                                $c_info_product_wt = "wt".$product_id;
                                $c_info_product_is_father = true;
                                $c_info_product_father_id = $product_id;
                            }else {
                                $c_info_product_name = $sub_product_list[$product_id]['product_name'];
                                $c_info_product_father_id = $sub_product_list[$product_id]['father_id'];
                                $c_info_product_wt = "wt".$c_info_product_father_id.$product_id;
                                $c_info_product_is_father = false;
                            }
                            $c_info_product_list[] = [
                                'product_id'        => $ap_info['product_id'],
                                'father_id'         => $c_info_product_father_id,
                                'product_name'      => $c_info_product_name,
                                'is_father_product' => $c_info_product_is_father,
                                'product_wt'        => $c_info_product_wt,
                            ];
                        }
                        usort($c_info_product_list,function($a,$b){
                            return $a['product_wt'] > $b['product_wt'];
                        });
                        $_info['product_list'] = $c_info_product_list;
                    }
                    $res_list[] = $_info;
                }
            }
        }
        $_res_list = [];
        foreach($res_list as $rl_info){
            $_res_info = $rl_info;
            if(count($rl_info['product_list']) > 0) {
                foreach ($rl_info['product_list'] as $product_info) {
                    $_res_info['product_id']        = $product_info['product_id'];
                    $_res_info['father_id']         = $product_info['father_id'];
                    $_res_info['product_name']      = $product_info['product_name'];
                    $_res_info['is_father_product'] = $product_info['is_father_product'];
                    $_res_info['product_wt']        = $product_info['product_wt'];
                    unset($_res_info['product_list']);
                    $_res_list[] = $_res_info;
                }
            }else{
                unset($_res_info['product_list']);
                $_res_info['product_id']        = 0;
                $_res_info['father_id']         = 0;
                $_res_info['product_name']      = '';
                $_res_info['is_father_product'] = false;
                $_res_info['product_wt']        = 'wt';
                $_res_list[] = $_res_info;
            }
        }

        usort($_res_list, function($a,$b){
            $cmp = strcmp($b['group_id'], $a['group_id']);
            if ($cmp == 0) {
                $_cmp = strcmp($a['customer_id'], $b['customer_id']);
                if ($_cmp == 0) {
                    return strcmp($a['account_id'].$a['product_wt'], $b['account_id'].$b['product_wt']);
                }
                return $_cmp;
            }
            return $cmp;
        });

        return [
            'sub_product_list'=> $sub_product_list,
            'product_ids'     => $product_ids,
            'group_ids'       => $group_ids,
            'account_ids'     => $account_ids,
            'res_list'        => $_res_list,
            'count'           => $custoemr_count,
        ];
    }


    /**
     * 获取配置列表
     *
     * @param        $group_ids
     * @param array  $customer_ids
     * @param        $account_ids
     * @param        $product_ids
     * @param        $sub_product_id_arr
     * @param        $product_item_ids
     * @param        $sub_product_item_ids
     * @param        $config_path
     * @param        $product_config_item_list
     * @param        $sub_product_config_item_list
     * @param string $sub_product_id
     *
     * @return array[]
     * @throws Exception
     * @static
     * <AUTHOR> 2023-11-08 13:50:02
     */
    public static function get_configs($group_ids,$customer_ids,$account_ids,$product_ids,$sub_product_id_arr,$product_item_ids,$sub_product_item_ids,$config_path,$product_config_item_list,$sub_product_config_item_list,$sub_product_id = '') {
        $group_config_count = $customer_config_count = $account_config_count = [];

        //2.1 获取配置
        // 主体-产品配置
        $group_product_config = [];
        $_group_product_config = ProductConfigModel::getConfigOnlyGroupIds($group_ids,$product_ids,$product_item_ids,$config_path);
        foreach($_group_product_config as $gpc_info){
            if(empty($gpc_info['group_id'])){
                continue;
            }
            if(!empty($sub_product_id)) {//如果搜索了子产品,则不计算主产品数据
                continue;
            }
            $item_source = 1;
            $group_config_count[$gpc_info['group_id']] = key_exists($gpc_info['group_id'], $group_config_count) ? $group_config_count[$gpc_info['group_id']] + 1 : 1;

            $tk = get_tmp_key($gpc_info['group_id'],$gpc_info['product_id']);
            $uk = get_tmp_key($gpc_info['config_item_id'],$item_source);

            if(isset($gpc_info['product_id'])) {
                $iid       = get_tmp_key(Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT, $gpc_info['config_item_id']);
                $item_info = isset($product_config_item_list[$gpc_info['product_id']][$iid]) ? $product_config_item_list[$gpc_info['product_id']][$iid] : [];
                $gpc_info  = array_merge($gpc_info, $item_info);
            }
            $gpc_info['config_item_source'] = Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT;
            $group_product_config[$tk][$uk] = $gpc_info;
        }

        //主体-子产品配置
        $group_sub_product_config = $_group_sub_product_config = [];
        $_group_sub_product_config[] = SubProductConfigModel::getConfigOnlyGroupIds($group_ids,$sub_product_id_arr,Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_SUB,$sub_product_item_ids,$config_path);
        $_group_sub_product_config[] = SubProductConfigModel::getConfigOnlyGroupIds($group_ids,$sub_product_id_arr,Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT,$product_item_ids,$config_path);
        foreach($_group_sub_product_config as $__group_sub_product_config) {
            foreach ($__group_sub_product_config as $gspc_info) {
                $item_source = $gspc_info['config_item_source'];
                $group_config_count[$gspc_info['group_id']] = key_exists($gspc_info['group_id'], $group_config_count) ? $group_config_count[$gspc_info['group_id']] + 1 : 1;
                $iid = get_tmp_key($item_source, $gspc_info['config_item_id']);
                if ($item_source == Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT) {
                    $father_id = ProductModel::getFatherId($gspc_info['sub_product_id']);
                    $item_info = isset($product_config_item_list[$father_id][$iid]) ? $product_config_item_list[$father_id][$iid] : [];
                } else {
                    $item_info = isset($sub_product_config_item_list[$gspc_info['sub_product_id']][$iid]) ? $sub_product_config_item_list[$gspc_info['sub_product_id']][$iid] : [];
                }

                $tk = get_tmp_key($gspc_info['group_id'], $gspc_info['sub_product_id']);
                $uk = get_tmp_key($gspc_info['config_item_id'], $item_source);

                $gspc_info                          = array_merge($gspc_info, $item_info);
                $group_sub_product_config[$tk][$uk] = $gspc_info;
            }
        }
        //客户-产品配置
        $customer_product_config = [];
        $_customer_product_config = ProductConfigModel::getConfigOnlyCustomerIds($customer_ids,$product_ids,$product_item_ids,$config_path);
            foreach($_customer_product_config as $cpc_info){
            if(!empty($sub_product_id)) {//如果搜索了子产品,则不计算主产品数据
                continue;
            }
            $item_source = 1;
            $customer_config_count[$cpc_info['customer_id']] = key_exists($cpc_info['customer_id'], $customer_config_count) ? $customer_config_count[$cpc_info['customer_id']] + 1 : 1;

            $tk = get_tmp_key($cpc_info['customer_id'],$cpc_info['product_id']);
            $uk = get_tmp_key($cpc_info['config_item_id'],$item_source);

            if(isset($cpc_info['product_id'])) {
                $iid       = get_tmp_key(Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT, $cpc_info['config_item_id']);
                $item_info = isset($product_config_item_list[$cpc_info['product_id']][$iid]) ? $product_config_item_list[$cpc_info['product_id']][$iid] : [];
                $cpc_info  = array_merge($cpc_info, $item_info);
            }
            $cpc_info['config_item_source'] = Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT;
            $customer_product_config[$tk][$uk] = $cpc_info;
        }
        //客户-子产品配置
        $customer_sub_product_config = $_customer_sub_product_config = [];
        $_customer_sub_product_config[] = SubProductConfigModel::getConfigOnlyCustomerIds($customer_ids,$sub_product_id_arr,Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_SUB, $sub_product_item_ids,$config_path);
        $_customer_sub_product_config[] = SubProductConfigModel::getConfigOnlyCustomerIds($customer_ids,$sub_product_id_arr,Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT, $product_item_ids,$config_path);
        foreach($_customer_sub_product_config as $__customer_sub_product_config) {
            foreach ($__customer_sub_product_config as $cspc_info) {
                $item_source = $cspc_info['config_item_source'];
                $customer_config_count[$cspc_info['customer_id']] = key_exists($cspc_info['customer_id'], $customer_config_count) ? $customer_config_count[$cspc_info['customer_id']] + 1 : 1;
                $iid = get_tmp_key($item_source, $cspc_info['config_item_id']);
                if ($item_source == Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT) {
                    $father_id = ProductModel::getFatherId($cspc_info['sub_product_id']);
                    $item_info = isset($product_config_item_list[$father_id][$iid]) ? $product_config_item_list[$father_id][$iid] : [];
                } else {
                    $item_info = isset($sub_product_config_item_list[$cspc_info['sub_product_id']][$iid]) ? $sub_product_config_item_list[$cspc_info['sub_product_id']][$iid] : [];
                }

                $tk = get_tmp_key($cspc_info['customer_id'], $cspc_info['sub_product_id']);
                $uk = get_tmp_key($cspc_info['config_item_id'], $item_source);

                $cspc_info                             = array_merge($cspc_info, $item_info);
                $customer_sub_product_config[$tk][$uk] = $cspc_info;
            }
        }
        //账号-产品配置
        $account_product_config = [];
        $_account_product_config = ProductConfigModel::getConfigOnlyAccountIds($account_ids,$product_ids,$product_item_ids,$config_path);
        foreach($_account_product_config as $apc_info){
            if(!empty($sub_product_id)) {//如果搜索了子产品,则不计算主产品数据
                continue;
            }
            $item_source = 1;
            $account_config_count[$apc_info['account_id']] = key_exists($apc_info['account_id'], $account_config_count) ? $account_config_count[$apc_info['account_id']] + 1 : 1;

            $tk = get_tmp_key($apc_info['account_id'],$apc_info['product_id']);
            $uk = get_tmp_key($apc_info['config_item_id'],$item_source);

            if(isset($apc_info['product_id'])) {
                $iid       = get_tmp_key(Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT, $apc_info['config_item_id']);
                $item_info = isset($product_config_item_list[$apc_info['product_id']][$iid]) ? $product_config_item_list[$apc_info['product_id']][$iid] : [];
                $apc_info  = array_merge($apc_info, $item_info);
            }
            $apc_info['config_item_source'] = Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT;
            $account_product_config[$tk][$uk] = $apc_info;
        }
        //账号-子产品配置
        $account_sub_product_config = $_account_sub_product_config = [];
        $_account_sub_product_config[] = SubProductConfigModel::getConfigOnlyAccountIds($account_ids,$sub_product_id_arr,Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_SUB, $sub_product_item_ids,$config_path);
        $_account_sub_product_config[] = SubProductConfigModel::getConfigOnlyAccountIds($account_ids,$sub_product_id_arr,Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT, $product_item_ids,$config_path);
        foreach($_account_sub_product_config as $__account_sub_product_config) {
            foreach ($__account_sub_product_config as $aspc_info) {
                $item_source = $aspc_info['config_item_source'];
                $account_config_count[$aspc_info['account_id']] = key_exists($aspc_info['account_id'], $account_config_count) ? $account_config_count[$aspc_info['account_id']] + 1 : 1;
                $iid = get_tmp_key($item_source, $aspc_info['config_item_id']);

                if ($item_source == Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT) {
                    $father_id = ProductModel::getFatherId($aspc_info['sub_product_id']);
                    $item_info = isset($product_config_item_list[$father_id][$iid]) ? $product_config_item_list[$father_id][$iid] : [];
                } else {
                    $item_info = isset($sub_product_config_item_list[$aspc_info['sub_product_id']][$iid]) ? $sub_product_config_item_list[$aspc_info['sub_product_id']][$iid] : [];
                }

                $tk = get_tmp_key($aspc_info['account_id'], $aspc_info['sub_product_id']);
                $uk = get_tmp_key($aspc_info['config_item_id'], $item_source);

                $aspc_info = array_merge($aspc_info, $item_info);
                $account_sub_product_config[$tk][$uk] = $aspc_info;
            }
        }

        // .1 主体有的配置 客户 账号 都要有,客户有的配置 账号也要有
        // .2 获取账号最终生效的配置 账号-子产品 -> 客户-子产品 ...
        return [
            'configs' => [
                'group_product'        => $group_product_config,       //主体-主产品
                'customer_product'     => $customer_product_config,    //客户-主产品
                'account_product'      => $account_product_config,     //账号-主产品
                'group_sub_product'    => $group_sub_product_config,   //主体-子产品
                'customer_sub_product' => $customer_sub_product_config,//客户-子产品
                'account_sub_product'  => $account_sub_product_config, //账号-子产品
            ],
            'group_config_count'    => $group_config_count,
            'customer_config_count' => $customer_config_count,
            'account_config_count'  => $account_config_count,
        ];
    }

    /**
     * 获取临时key
     *
     * @param $lv
     * @param $account_info
     *
     * @static
     * @return string
     * <AUTHOR> 2023-11-08 14:34:03
     */
    private static function get_lv_key($lv, $account_info) {
        $lv_key = '';

        switch($lv) {
            case 'group_product':
                $lv_key = get_tmp_key($account_info['group_id'], $account_info['father_id']);
                break;
            case 'customer_product':
                $lv_key = get_tmp_key($account_info['customer_id'], $account_info['father_id']);
                break;
            case 'account_product':
                $lv_key = get_tmp_key($account_info['account_id'], $account_info['father_id']);
                break;
            case 'group_sub_product':
                $lv_key = get_tmp_key($account_info['group_id'], $account_info['product_id']);
                break;
            case 'customer_sub_product':
                $lv_key = get_tmp_key($account_info['customer_id'], $account_info['product_id']);
                break;
            case 'account_sub_product':
                $lv_key = get_tmp_key($account_info['account_id'], $account_info['product_id']);
                break;
        }
        return $lv_key;
    }

    /**
     * 获取产品配置项
     *
     * @param        $product_ids
     * @param array  $sub_product_ids
     * @param string $item_name
     *
     * @return array
     * @throws Exception
     * @static
     * <AUTHOR> 2023-11-08 16:12:14
     */
    public static function get_config_items($product_ids, $sub_product_ids,$item_name = '') {
        $product_config_item_list = $sub_product_config_item_list = $product_item_ids = $sub_product_item_ids = [];
        //获取配置项
        $_product_config_item_list = ProductConfigItemModel::getListByItemNameAndProductIds($item_name, $product_ids);
        foreach($_product_config_item_list as $p_item_info){
            $pk = get_tmp_key(Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT,$p_item_info['id']);
            $product_config_item_list[$p_item_info['product_id']][$pk] = $p_item_info;
            $product_item_ids[$p_item_info['id']] = $p_item_info['id'];
        }
        $_sub_product_config_item_list = SubProductConfigItemModel::getListByItemNameAndSubProductIds($item_name, $sub_product_ids);
        foreach($_sub_product_config_item_list as $s_item_info){
            $sk = get_tmp_key(Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_SUB,$s_item_info['id']);
            $sub_product_config_item_list[$s_item_info['product_id']][$sk] = $s_item_info;
            $sub_product_item_ids[$s_item_info['id']] = $s_item_info['id'];
        }
        return [$product_config_item_list,$sub_product_config_item_list,$product_item_ids,$sub_product_item_ids];
    }
}