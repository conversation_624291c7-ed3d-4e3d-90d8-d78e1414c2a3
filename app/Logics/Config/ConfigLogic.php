<?php
namespace App\Logics\Config;

use App\Logics\Approval\ApprovalLogic;
use App\Logics\BaseLogic;
use App\Logics\Product\AccountProductLogic;
use App\Models\Product\ProductConfigItemModel;
use App\Models\Product\ProductConfigModel;
use App\Models\Product\ProductModel;
use App\Models\Product\SubProductConfigItemModel;
use App\Models\Product\SubProductConfigModel;
use App\Repositories\Product\ProductConfigRepositorie;
use App\Repositories\Product\SubProductConfigRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

class ConfigLogic extends BaseLogic{


    /**
     * 编辑,添加客户-产品-配置
     *
     * @param $product_id
     * @param $sub_product_id
     * @param $group_id
     * @param $customer_id
     * @param $account_id
     * @param $config_item_id
     * @param $item_value
     * @param $config_item_source
     * @param $admin
     *
     * @return int
     * @throws Exception
     * @static
     * <AUTHOR> 2023-12-14 16:40:39
     */
    public static function customer_product_config_edit($product_id, $sub_product_id, $group_id, $customer_id, $account_id, $config_item_id, $item_value, $config_item_source,$admin){
        if(is_array($item_value)){
            $item_value = implode(',',$item_value);
        }

        $pic = 0;
        if(!empty($sub_product_id)){
            $product_id = 0;
            $pic = 1;
        }
        if(!empty($product_id)){
            $sub_product_id = 0;
            $pic = 1;
        }

        if($pic != 1){
            throw new Exception('',Enu::RESP_CUSTOMER_PRODUCT_CONFIG_MISSING_PRODUCT_ID_OR_SUB_PRODUCT_ID);
        }

        $cc = 0;
        if(!empty($group_id)){
            $cc ++;
        }
        if(!empty($customer_id)){
            $cc ++;
        }
        if(!empty($account_id)){
            $cc ++;
        }
        if($cc != 1){
            throw new Exception('',Enu::RESP_CUSTOMER_PRODUCT_CONFIG_MISSING_GROUP_ID_OR_CUSTOMER_ID_OR_ACCOUNT_ID);
        }

        if(empty($config_item_id)){
            throw new Exception('',Enu::RESP_CUSTOMER_PRODUCT_CONFIG_MISSING_ITEM_ID);
        }

        if($product_id > 0){
            $config_item_source = Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT;
        }


        if(!in_array($config_item_source,[Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_SUB,Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT])){
            throw new Exception('',Enu::RESP_CUSTOMER_PRODUCT_CONFIG_WORNG_SOURCE_VALUE);
        }

        //校验客户是否开通该产品
        $has_product = AccountProductLogic::has_product($group_id,$customer_id,$account_id,$product_id,$sub_product_id);
        if(!$has_product){
            throw new Exception('',Enu::RESP_CUSTOMER_PRODUCT_CUSTOMER_HAS_NO_OPEN_THIS_PRODUCT);
        }



        // 校验config_item_id是否可配置
        if(!empty($product_id)){
            $item_info = ProductConfigItemModel::getInfoByIdAndProductId($config_item_id,$product_id);
        }else{
            if($config_item_source == Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT){
                $father_id = ProductModel::getFatherId($sub_product_id);
                $item_info = ProductConfigItemModel::getInfoByIdAndProductId($config_item_id,$father_id);
            }else{
                $item_info = SubProductConfigItemModel::getInfoByIdAndProductId($config_item_id,$sub_product_id);
            }
        }

        //产品没有这个配置项
        if(!$item_info){
            throw new Exception('',Enu::RESP_CUSTOMER_PRODUCT_ILLEG_ITEM_ID);
        }

        //获取配置信息
        if(!empty($product_id)){
            $config_info = ProductConfigRepositorie::infoWithGCA($product_id, $group_id, $customer_id, $account_id, $config_item_id);
        }else{
            $config_info = SubProductConfigRepositorie::infoWithGCA($sub_product_id, $group_id, $customer_id, $account_id, $config_item_id,$config_item_source);
        }


        $value = !empty($config_info)?$config_info['item_value']:'';//原值

        $data = [
            'approval_id'        => create_uuid('CA'),
            'type'               => Enu::APPROVAL_TYPE_CUSTOMER_PRODUCT_CONFIG,
            'action'             => empty($config_info) ? Enu::APPROVAL_ACTION_ADD : Enu::APPROVAL_ACTION_EDIT,
            'group_id'           => $group_id,
            'customer_id'        => $customer_id,
            'account_id'         => $account_id,
            'product_id'         => $product_id,
            'sub_product_id'     => $sub_product_id,
            'config_item_id'     => $config_item_id,
            'config_item_source' => $config_item_source,
            'value'              => $value,
            'new_value'          => $item_value,
            'status'             => Enu::APPROVAL_STATUS_NEED_APPROVAL,
            'applicant'          => $admin,
        ];

        return ApprovalLogic::add($data);
    }



    /**
     * 获取上一级配置值
     *
     * 1. 子产品 -> 配置项
     *
     * @param $config_path string 当前级别
     * @param $product_id
     * @param $sub_product_id
     * @param $item_id
     * @param $item_source
     * @param $group_id
     * @param $customer_id
     * @param $account_id
     *
     * @return string
     * @throws Exception
     * @static
     * <AUTHOR> 2023-11-03 11:17:41
     */
    public static function get_last_level_config($config_path, $product_id, $sub_product_id, $item_id, $item_source, $group_id, $customer_id, $account_id){
        $res = null;

        if($sub_product_id > 0){
            $product_id = ProductModel::getFatherId($sub_product_id);
        }

        $config_path_map = config('config_path_map');
        $last_level_list = $config_path_map[$config_path];
        if(empty($last_level_list)){
            return '';
        }

        foreach($last_level_list as $_config_path){
            switch($_config_path){
                case 'item_default':
                    if($item_source == Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT) {
                        $item_info = ProductConfigItemModel::getInfoById($item_id);
                    }else{
                        $item_info = SubProductConfigItemModel::getInfoById($item_id);
                    }
                    $res = $item_info['item_default'];
                    break;
                case 'product':
                    $product_configs = ProductConfigModel::getAllItemConfigs($product_id, $item_id, '', '', '');
                    $res = isset($product_configs[0]['item_value'])?$product_configs[0]['item_value']:null;
                    break;
                case 'group_product':
                    $product_configs = ProductConfigModel::getAllItemConfigs($product_id, $item_id, $group_id, '', '');
                    $res = isset($product_configs[0]['item_value'])?$product_configs[0]['item_value']:null;
                    break;
                case 'customer_product':
                    $product_configs = ProductConfigModel::getAllItemConfigs($product_id, $item_id, $group_id, $customer_id, '');
                    $res = isset($product_configs[0]['item_value'])?$product_configs[0]['item_value']:null;
                    break;
                case 'group_sub_product':
                    $sub_product_configs = SubProductConfigModel::getAllItemConfigs($sub_product_id, $item_id, $item_source, $group_id, '', '');
                    $res = isset($sub_product_configs[0]['item_value'])?$sub_product_configs[0]['item_value']:null;
                    break;
                case 'customer_sub_product':
                    $sub_product_configs = SubProductConfigModel::getAllItemConfigs($sub_product_id, $item_id, $item_source, $group_id, $customer_id, '');
                    $res = isset($sub_product_configs[0]['item_value'])?$sub_product_configs[0]['item_value']:null;
                    break;
                case 'account_sub_product':
                case 'account_product':
                    $res = null;
                    break;
            }
            if($res !== null){
                break;
            }
        }
        return $res;
    }

    /**
     * 判断配置项类型是否是单选或多选
     *
     * @param $type
     *
     * @static
     * @return bool
     * <AUTHOR> 2023-11-14 16:41:48
     */
    public static function is_checkbox_or_radio($type){
        return $type == Enu::PRODUCT_CONFIG_ITEM_TYPE_CHECKBOX || $type == Enu::PRODUCT_CONFIG_ITEM_TYPE_RADIO;
    }


    /**
     * 获取配置值
     *
     * @param $item_type
     * @param $options
     * @param $value
     *
     * @static
     * @return mixed|string
     * <AUTHOR> 2023-11-14 16:16:54
     */
    public static function get_value($item_type,$options,$value){
        if(self::is_checkbox_or_radio($item_type)){
            $options = json_decode($options,true);
            $options = array_column($options, 'label', 'value');
            if($item_type == Enu::PRODUCT_CONFIG_ITEM_TYPE_CHECKBOX) {
                if(!empty($value)) {
                    $new_value = [];
                    $_new_value = explode(',', $value);
                    foreach ($_new_value as $v) {
                        $new_value[] = isset($options[$v])?$options[$v]:$v;
                    }
                    $value = implode(',', $new_value);
                }
            }else{
                $value = isset($options[$value])?$options[$value]:'';
            }
        }
        return $value;
    }
}