<?php
/**
 * 搜索逻辑
 */
namespace App\Logics\Config;

use App\Logics\Approval\ApprovalLogic;
use App\Logics\BaseLogic;
use App\Logics\Product\ProductLogic;
use App\Logics\System\RoleLogic;
use App\Models\Product\ProductModel;
use App\Models\Product\SubProductConfigItemModel;
use App\Utils\Helpers\Enu;
use Exception;

class SubProductConfigItemLogic extends BaseLogic{


    /**
     * 检查配置项在子产品中是否唯一
     *
     * @param $product_ids
     * @param $item_key
     *
     * @return bool
     * @throws Exception
     * @static
     * <AUTHOR> 2023-10-18 14:44:51
     */
    public static function check_unique($product_ids,$item_key){
        $where = [
            ['product_id', 'in', $product_ids],
            ['item_key', '=', $item_key],
        ];
        $product_config_item_count = SubProductConfigItemModel::getCount($where);
        return $product_config_item_count == 0;
    }

    /**
     * 子产品 添加配置项
     *
     * @param $product_id
     * @param $item_key
     * @param $item_name
     * @param $role
     * @param $modify
     * @param $config_scope
     * @param $item_type
     * @param $item_options
     * @param $item_default
     * @param $applicant
     *
     * @return mixed
     * @throws Exception
     * @static
     * <AUTHOR> 2023-11-09 18:30:10
     */
    public static function add_item($product_id, $item_key, $item_name, $role, $modify, $config_scope, $item_type, $item_options, $item_default, $applicant) {
        if(empty($product_id)){
            throw new Exception('',Enu::RESP_SUB_PRODUCT_MISSING_PRODUCT_ID);
        }
        if(empty($item_key)){
            throw new Exception('',Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_KEY);
        }
        if(empty($item_name)){
            throw new Exception('',Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_NAME);
        }
        if(empty($role)){
            throw new Exception('',Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_ROLE);
        }
        if(empty($modify)){
            throw new Exception('',Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_MODIFY);
        }
        if(empty($item_type)){
            throw new Exception('',Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_ITEM_TYPE);
        }
        if(empty($config_scope)){
            throw new Exception('',Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_CONFIG_SCOPE);
        }else{//不可以在产品配置中进行配置必须可被客户进行修改
            if($config_scope == Enu::PRODUCT_CONFIG_ITEM_PRODUCT_CAN_NOT_EDIT && $modify == Enu::PRODUCT_CONFIG_ITEM_CUSTOMER_CAN_NOT_MODIFY){
                throw new Exception('',Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_CONFLICT_CONFIG_SCOPE_AND_MODIFY);
            }
        }
        // if(empty($item_default)){
        //     throw new Exception('',Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_ITEM_DEFAULT);
        // }

        //校验权限
        $is_super_admin = RoleLogic::is_super_admin($applicant);
        if(!$is_super_admin){
            throw new Exception('',Enu::RESP_USER_HAVE_NO_AUTHORIZATION);
        }

        //判断是否是子产品
        $product_info = ProductModel::getInfoByProductId($product_id,['product_id','father_id']);
        if($product_info['father_id'] == 0){
            throw new Exception('',Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_NOT_SUB_PRODUCT_ID);
        }

        //判断配置项key是否已经存在
        $is_unqiue = ProductConfigItemLogic::check_unique($product_info['father_id'],$item_key);
        if(!$is_unqiue){
            throw new Exception('',Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_DUPLICATION_PRODUCT_KEY);
        }

        //判断配置项key在子产品中是否已经存在 同一子产品不可重复,不同子产品间可重复
        // $sub_product_ids = ProductLogic::get_sub_product_ids($product_info['father_id']);
        $sub_product_ids = [$product_id];
        $is_unqiue = SubProductConfigItemLogic::check_unique($sub_product_ids,$item_key);
        if(!$is_unqiue){
            throw new Exception('',Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_DUPLICATION_KEY);
        }

        $fmt_item_options = ProductConfigItemLogic::process_config($item_type,$item_options,$item_default);

        $item_data = [
            'product_id'   => $product_id,
            'item_key'     => $item_key,
            'item_name'    => $item_name,
            'role'         => $role,
            'modify'       => $modify,
            'config_scope' => $config_scope,
            'item_type'    => $item_type,
            'item_options' => $fmt_item_options,
            'item_default' => $item_default,
        ];

        $approval_data = [
            'approval_id'    => create_uuid('CA'),
            'type'           => Enu::APPROVAL_TYPE_PRODUCT_CONFIG_ITEM,
            'action'         => Enu::APPROVAL_ACTION_ADD,
            'product_id'     => $product_info['father_id'],
            'sub_product_id' => $product_id,
            'value'          => '',
            'new_value'      => json_encode($item_data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
            'status'         => Enu::APPROVAL_STATUS_NEED_APPROVAL,
            'applicant'      => $applicant,
        ];

        return ApprovalLogic::add($approval_data);
    }
}