<?php
namespace App\Logics\Config;

use App\Logics\Approval\ApprovalLogic;
use App\Logics\BaseLogic;
use App\Logics\Product\ProductLogic;
use App\Logics\System\RoleLogic;
use App\Models\Product\ProductConfigItemModel;
use App\Models\Product\ProductModel;
use App\Utils\Helpers\Enu;
use Exception;
use Yulore\Components\Config;

/**
 * 主产品配置项相关逻辑
 */
class ProductConfigItemLogic extends BaseLogic{

    public static $role_map = [
        'op' => Enu::USER_ROLE_TYPE_OPERATOR,//运营
        'te' => Enu::USER_ROLE_TYPE_TECHNICIAN,//技术
        'pm' => Enu::USER_ROLE_TYPE_PRODUCT_MANAGER,//产品
    ];

    /**
     * 检查配置项在主产品中是否唯一
     *
     * @param $product_id
     * @param $item_key
     *
     * @static
     * @return bool
     * @throws Exception
     * <AUTHOR> 2023-10-18 14:44:51
     */
    public static function check_unique($product_id,$item_key){
        $where = [
            ['product_id', '=', $product_id],
            ['item_key', '=', $item_key],
        ];
        $product_config_item_count = ProductConfigItemModel::getCount($where);
        return $product_config_item_count == 0;
    }


    /**
     * 校验并处理配置项选项
     *
     * @param $item_type
     * @param $item_options
     * @param $item_default
     *
     * @return false|string
     * @throws Exception
     * @static
     * <AUTHOR> 2023-10-19 11:56:33
     */
    public static function process_config($item_type,$item_options,$item_default) {
        if(ConfigLogic::is_checkbox_or_radio($item_type)){
            if(empty($item_options)){
                throw new Exception('',Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_OPTIONS);
            }
        }

        //处理配置选项
        if(!empty($item_options) && ConfigLogic::is_checkbox_or_radio($item_type)) {
            // if(empty($item_default)){
            //     throw new Exception('',Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_DEFAULT);
                // $this->response(Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_DEFAULT);
            // }
            $options_values = [];
            $rows = split_with_crlf($item_options);
            $fmt_item_options = [];
            $split_preg = "/[\s:]+/";//使用空白字符,逗号,冒号分割字符串
            foreach($rows as $row){
                if(empty($row)) {
                    continue;
                }
                $row_arr = preg_split($split_preg, trim($row));
                $fmt_item_options[] = [
                    'value' => $row_arr[0],
                    'label' => $row_arr[1],
                ];
                $options_values[] = $row_arr[0];
            }
            $default_values = explode(',',$item_default);
            foreach($default_values as $default_value){
                if(!in_array($default_value,$options_values)){
                    throw new Exception('',Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_DEFAULT_VALUE_NOT_IN_OPTIONS);
                }
            }
            $fmt_item_options = json_encode($fmt_item_options,JSON_UNESCAPED_UNICODE);
        }else{
            $fmt_item_options = '';
        }

        return $fmt_item_options;
    }


    /**
     * 获取主产品可继承配置项
     *
     *
     * @param      $product_id
     * @param      $group_id
     * @param      $customer_id
     * @param      $account_id
     * @param      $config_path
     * @param      $role
     * @param bool $customer_can_modify
     *
     * @return array
     * @throws Exception
     * @static
     * <AUTHOR> 2023-10-26 11:49:30
     */
    public static function get_extend_config_items($product_id,$group_id,$customer_id,$account_id,$config_path,$role,$customer_can_modify = false){
        $father_id = ProductModel::getFatherId($product_id);
        $config_items = ProductConfigItemModel::getExtendItems($father_id,$role,$customer_can_modify);

        $_item_list = [];
        foreach($config_items as $iinfo){
            if($iinfo['item_type'] == Enu::PRODUCT_CONFIG_ITEM_TYPE_CHECKBOX){
                $iinfo['item_default'] = explode(',', $iinfo['item_default']);
            }

            $last_level_config = ConfigLogic::get_last_level_config($config_path, $father_id, $product_id, $iinfo['id'], Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT, $group_id, $customer_id, $account_id);
            if($iinfo['item_type'] == Enu::PRODUCT_CONFIG_ITEM_TYPE_CHECKBOX){
                if(empty($last_level_config)){
                    $last_level_config = [];
                } else {
                    $last_level_config = explode(',', $last_level_config);
                }
            }

            $_info = [
                'product_id'         => $product_id,
                'modify'             => $iinfo['modify'],
                'config_item_source' => Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT,
                'config_item_id'     => $iinfo['id'],
                'config_item_name'   => $iinfo['item_name'],
                'config_item_key'    => $iinfo['item_key'],
                'item_type'          => $iinfo['item_type'],
                'item_options'       => json_decode($iinfo['item_options'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
                'item_value'         => $last_level_config,//展示最近的已配置值
                'can_del'            => true,//是否可以删除
            ];
            $_item_list[] = $_info;
        }

        return $_item_list;
    }


    /**
     *
     *
     * @param $product_id
     * @param $item_key
     * @param $item_name
     * @param $role
     * @param $modify
     * @param $extend
     * @param $config_scope
     * @param $item_type
     * @param $item_options
     * @param $item_default
     * @param $applicant
     *
     * @return int
     * @throws Exception
     * @static
     * <AUTHOR> 2023-11-09 16:06:41
     */
    public static function add_item($product_id,$item_key,$item_name,$role,$modify,$extend,$config_scope,$item_type,$item_options,$item_default,$applicant) {
        if(empty($product_id)){
            throw new Exception('',Enu::RESP_PRODUCT_MISSING_PRODUCT_ID);
        }
        if(empty($item_key)){
            throw new Exception('',Enu::RESP_PRODUCT_CONFIG_ITEM_MISSING_KEY);
        }
        if(empty($item_name)){
            throw new Exception('',Enu::RESP_PRODUCT_CONFIG_ITEM_MISSING_NAME);
        }
        if(empty($role)){
            throw new Exception('',Enu::RESP_PRODUCT_CONFIG_ITEM_MISSING_ROLE);
        }
        if(empty($modify)){
            throw new Exception('',Enu::RESP_PRODUCT_CONFIG_ITEM_MISSING_MODIFY);
        }
        if(empty($extend)){
            throw new Exception('',Enu::RESP_PRODUCT_CONFIG_ITEM_MISSING_EXTEND);
        }
        if(empty($item_type)){
            throw new Exception('',Enu::RESP_PRODUCT_CONFIG_ITEM_MISSING_ITEM_TYPE);
        }
        if(empty($config_scope)){
            throw new Exception('',Enu::RESP_PRODUCT_CONFIG_ITEM_MISSING_CONFIG_SCOPE);
        }else{//仅在客户维度配置 必须 可被客户进行修改
            if($config_scope == Enu::PRODUCT_CONFIG_ITEM_PRODUCT_CAN_NOT_EDIT && $modify == Enu::PRODUCT_CONFIG_ITEM_CUSTOMER_CAN_NOT_MODIFY){
                throw new Exception('',Enu::RESP_PRODUCT_CONFIG_ITEM_CONFLICT_CONFIG_SCOPE_AND_MODIFY);
            }
        }
        // if(empty($item_default)){
        //     $this->response(Enu::RESP_PRODUCT_CONFIG_ITEM_MISSING_ITEM_DEFAULT);
        // }
        //校验权限
        $is_super_admin = RoleLogic::is_super_admin($applicant);
        if(!$is_super_admin){
            throw new Exception('',Enu::RESP_USER_HAVE_NO_AUTHORIZATION);
        }

        $is_super_admin = RoleLogic::is_super_admin($applicant);
        if(!$is_super_admin){//角色校验 只有超级管理员可以添加配置项
            throw new Exception('',Enu::RESP_USER_HAVE_NO_AUTHORIZATION);
        }

        $is_unqiue = ProductConfigItemLogic::check_unique($product_id,$item_key);
        if(!$is_unqiue){//判断配置项key是否已经存在
            throw new Exception('',Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_DUPLICATION_PRODUCT_KEY);
        }

        $sub_product_ids = ProductLogic::get_sub_product_ids($product_id);
        $is_unqiue = SubProductConfigItemLogic::check_unique($sub_product_ids,$item_key);
        if(!$is_unqiue){//检查配置项是否唯一
            throw new Exception('',Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_DUPLICATION_KEY);
        }

        $fmt_item_options = ProductConfigItemLogic::process_config($item_type,$item_options,$item_default);

        $item_data = [
            'product_id'   => $product_id,
            'item_key'     => $item_key,
            'item_name'    => $item_name,
            'role'         => $role,
            'modify'       => $modify,
            'extend'       => $extend,
            'item_type'    => $item_type,
            'config_scope' => $config_scope,
            'item_options' => $fmt_item_options,
            'item_default' => $item_default,
        ];

        $approval_data = [
            'approval_id' => create_uuid('CA'),
            'type'        => Enu::APPROVAL_TYPE_PRODUCT_CONFIG_ITEM,
            'action'      => Enu::APPROVAL_ACTION_ADD,
            'product_id'  => $product_id,
            'value'       => '',
            'new_value'   => json_encode($item_data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
            'status'      => Enu::APPROVAL_STATUS_NEED_APPROVAL,
            'applicant'   => $applicant,
        ];

        return ApprovalLogic::add($approval_data);
    }
}