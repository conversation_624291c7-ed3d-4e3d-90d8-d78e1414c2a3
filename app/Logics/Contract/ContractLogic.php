<?php

namespace App\Logics\Contract;

use App\Models\Contract\ContractModel;
use App\Models\Customer\CustomerModel;
use App\Models\Product\ProductModel;
use App\Models\System\SystemUserModel;
use App\Utils\Helpers\Enu;
use Exception;
use PHPExcel_Cell;
use PHPExcel_Exception;
use PHPExcel_IOFactory;
use PHPExcel_Reader_Exception;
use PHPExcel_Shared_Date;

class ContractLogic
{
    /** @var array 合同类别 */
    private static $category_arr = [
        '客户类别' => Enu::CONTRACT_CATEGORY_CUSTOMER,
        '渠道类别' => Enu::CONTRACT_CATEGORY_SOURCE,
        '三方类别' => Enu::CONTRACT_CATEGORY_THRID
    ];

    /** @var array 合同类型 */
    private static $type = [
        '收入' => Enu::CONTRACT_TYPE_INCOME,
        '支出' => Enu::CONTRACT_TYPE_EXPEND,
        '保密' => Enu::CONTRACT_TYPE_SECRECY,
        '其他' => Enu::CONTRACT_TYPE_OTHER,
    ];

    /** @var array 签约类型 */
    private static $sign_type = [
        '新签' => Enu::CONTRACT_SIGN_TYPE_NEW_SIGN,
        '续签' => Enu::CONTRACT_SIGN_TYPE_RENEW_SIGN,
    ];

    //客户类别表头
    private static $contract_category_customer_header = ['合同编号','客户名称','合同类型','公司名称','合同开始日期','合同结束日期','签约类型','签约产品（主产品）','签约产品（子产品）','商务跟进人','备注','归档份数'];
    private static $contract_category_source_header   = ['合同编号','渠道简称','合同类型','渠道全称','合同开始日期','合同结束日期','签约类型','签约渠道产品','商务跟进人','备注','归档份数'];
    private static $contract_category_thrid_header    = ['合同编号','客户名称','合同类型','公司名称','第三方公司名称','合同开始日期','合同结束日期','签约类型','产品','商务跟进人','备注','归档份数'];


    public static function options(){
        $res['category'] = [[
                'value' => -1,
                'label' => '全部',
            ], [
                'value' => Enu::CONTRACT_CATEGORY_CUSTOMER,
                'label' => '客户类别',
            ], [
                'value' => Enu::CONTRACT_CATEGORY_SOURCE,
                'label' => '渠道类别',
            ], [
                'value' => Enu::CONTRACT_CATEGORY_THRID,
                'label' => '三方类别',
        ],];

        $res['type'] = [[
                'value' => -1,
                'label' => '全部',
            ], [
                'value' => Enu::CONTRACT_TYPE_INCOME,
                'label' => '收入',
            ], [
                'value' => Enu::CONTRACT_TYPE_EXPEND,
                'label' => '支出',
            ], [
                'value' => Enu::CONTRACT_TYPE_SECRECY,
                'label' => '保密',
            ], [
                'value' => Enu::CONTRACT_TYPE_OTHER,
                'label' => '其他',
        ],];

        $res['sign'] = [[
                'value' => -1,
                'label' => '全部',
            ], [
                'value' => Enu::CONTRACT_SIGN_TYPE_NEW_SIGN,
                'label' => '新签',
            ], [
                'value' => Enu::CONTRACT_SIGN_TYPE_RENEW_SIGN,
                'label' => '续签',
        ],];
        return $res;
    }


    /**
     * 列表条件
     *
     * @param $params
     *
     * @static
     * @return array
     * <AUTHOR> 2024-03-04 14:19:33
     */
    private static function get_list_where($params){
        $where = [];

        $contract_no       = $params['contract_no'];
        $contract_category = $params['contract_category'];
        $contract_type     = $params['contract_type'];
        $contract_start    = $params['contract_start'];
        $contract_end      = $params['contract_end'];
        $created_at_start  = $params['created_at_start'];
        $created_at_end    = $params['created_at_end'];
        $company_name      = $params['company_name'];
        if(!empty($contract_no)){
            $where[] = ['contract_no', '=', $contract_no];
        }
        if(!empty($contract_category)){
            $where[] = ['contract_category', '=', $contract_category];
        }
        if(!empty($contract_type) && $contract_type != -1){
            $where[] = ['contract_type', '=', $contract_type];
        }
        if(!empty($contract_start)){
            $where[] = ['contract_start', '>=', $contract_start];
        }
        if(!empty($contract_end)){
            $where[] = ['contract_end', '<', $contract_end];
        }
        if(!empty($created_at_start)){
            $where[] = ['created_at', '>=', $created_at_start];
        }
        if(!empty($created_at_end)){
            $where[] = ['created_at', '<', $created_at_end];
        }

        if(!empty($company_name)){
            $where[] = ['company_name', '=', $company_name];
        }

        $where[] = ['delated_at', 'is null', ''];

        return $where;
    }


    /**
     * 获取列表
     *
     * @param $params
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2024-02-21 19:15:40
     */
    public static function get_list($params) {
        $page              = $params['page'];
        $page_size         = $params['page_size'];

        $where = self::get_list_where($params);

        $field = ['*'];
        $limit = $page_size;
        $offset = ($page - 1) * $limit;
        $page = ['limit'=>$limit,'offset'=>$offset];
        $order = ['id','desc'];
        $_list = ContractModel::getAll($where,$field,$page,$order);

        if(empty($_list)){
            return $_list;
        }

        $category_map = array_flip(self::$category_arr);
        $type_map = array_flip(self::$type);
        $sign_map = array_flip(self::$sign_type);

        $salesman_list = array_unique(array_column($_list, 'salesman'));

        $system_user = SystemUserModel::getAll([['username','in',$salesman_list]]);
        $all_salesman = array_column($system_user, 'realname', 'username');

        $product_ids = [];
        foreach($_list as &$_info){
            $_product_ids = explode(",",$_info['product_id']);
            $_info['product_ids'] = $_product_ids;
            foreach($_product_ids as $_pid){
                $product_ids[$_pid] = $_pid;
            }
        }
        $product_ids = array_values($product_ids);

        $_product_infos = ProductModel::getInfoByProductIds($product_ids,['product_id','father_id','product_name']);
        $father_ids = [];
        $product_infos = [];
        foreach ($_product_infos as $product_info) {
            $father_id = $product_info['father_id'] == 0 ? $product_info['product_id'] : $product_info['father_id'];
            $father_ids[$product_info['product_id']] = $father_id;
            $product_infos[$product_info['product_id']] = $product_info['product_name'];
        }

        $_father_infos = ProductModel::getInfoByProductIds(array_values($father_ids),['product_id','product_name']);
        $father_infos = [];
        foreach($_father_infos as $info){
            $father_infos[$info['product_id']] = $info['product_name'];
        }

        $list = [];
        foreach($_list as $info){
            $main_product_name = [];
            $product_name = [];
            foreach ($info['product_ids'] as $info_product_id) {
                if(empty($info_product_id)) {
                    continue;
                }
                $info_father_id      = $father_ids[$info_product_id];
                $product_name[]      = $product_infos[$info_product_id];
                $main_product_name[] = $father_infos[$info_father_id];
            }

            $info['contract_category'] = $category_map[$info['contract_category']];
            $info['contract_type']     = $type_map[$info['contract_type']];
            $info['sign_type']         = $sign_map[$info['sign_type']];
            $info['contract_start']    = !empty($info['contract_start'])?substr($info['contract_start'],0,10):'';
            $info['contract_end']      = !empty($info['contract_end'])?substr($info['contract_end'],0,10):'';
            $info['created_at']        = substr($info['created_at'],0,10);
            $info['salesman']          = isset($all_salesman[$info['salesman']])?$all_salesman[$info['salesman']]:'';
            // $info['main_product_name'] = isset($father_infos[$info['product_id']])?$father_infos[$father_ids[$info['product_id']]]:'';
            $info['main_product_name'] = $main_product_name;
            $info['product_name']      = $product_name;
            $list[] = $info;
        }

        return $list;
    }


    /**
     *
     *
     * @param $params
     *
     * @static
     * @return int|mixed
     * @throws Exception
     * <AUTHOR> 2024-03-04 14:19:54
     */
    public static function get_count($params) {
        $where = self::get_list_where($params);
        return ContractModel::getCount($where);
    }


    /**
     * 校验文件
     * 是否存在excel
     * excel中数据是否与上传的pdf数量相同,名称是否匹配
     * 合同编号与excel中相关字段是否匹配
     *
     * @param $contract_category
     * @param $excel
     * @param $pdf s
     * @param $admin
     *
     * @return true
     * @throws PHPExcel_Exception
     * @throws PHPExcel_Reader_Exception
     * @throws Exception
     * @static
     * <AUTHOR> 2024-02-29 18:52:53
     */
    public static function add($contract_category,$excel,$pdf,$admin){
        if(empty($contract_category)){
            throw new Exception('',Enu::RESP_CONTRACT_UPLOAD_CATEGORY_EMPTY);
        }
        if(empty($excel)){
            throw new Exception('',Enu::RESP_CONTRACT_UPLOAD_EXCEL_EMPTY);
        }
        if(empty($pdf)){
            throw new Exception('',Enu::RESP_CONTRACT_UPLOAD_PDF_EMPTY);
        }

        $pdf_name_map = [];
        foreach($pdf as $p_file){
            $p_file_name = explode("_",$p_file);
            $p_file_name = substr($p_file_name[3], 0, -4);
            $pdf_name_map[$p_file_name] = $p_file;
        }

        //获取excel中行数是
        //下载excel文件
        //解析excel
        $excel_url = env('CONTRUCT_SITE_URL').$excel;

        // 初始化 cURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL,$excel_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            throw new Exception('',Enu::RESP_CONTRACT_LOAD_EXCEL_FAIL);
        }
        curl_close($ch);

        // 将响应保存到文件
        $excel_path = "/tmp/".$excel;
        file_put_contents($excel_path, $response);

        $inputFileType = PHPExcel_IOFactory::identify($excel_path);
        $objReader = PHPExcel_IOFactory::createReader($inputFileType);
        $objReader = $objReader->setReadDataOnly(true);
        $excel_obj = $objReader->load($excel_path);

        unlink($excel_path);

        // 读取第一个工作表
        $worksheet = $excel_obj->getActiveSheet();
        //行数
        $higest_data_row = $worksheet->getHighestDataRow();
        $higest_data_col = $worksheet->getHighestDataColumn();
        if($higest_data_row - 1 > count($pdf_name_map)){
            throw new Exception('',Enu::RESP_CONTRACT_CONTRACT_FILE_COUNT_GREATER_THAN_ROWS);
        }
        if($higest_data_row - 1 < count($pdf_name_map)){
            throw new Exception('',Enu::RESP_CONTRACT_CONTRACT_FILE_COUNT_LESS_THAN_ROWS);
        }
        // 读取单元格数据

        $col_range = range('A', $higest_data_col);

        $excel_headers = [
            //客户类别表头
            Enu::CONTRACT_CATEGORY_CUSTOMER => self::$contract_category_customer_header,
            //渠道类别
            Enu::CONTRACT_CATEGORY_SOURCE   => self::$contract_category_source_header,
            //三方类别
            Enu::CONTRACT_CATEGORY_THRID    => self::$contract_category_thrid_header,
        ];
        //判断传入类别与定义的表头是否匹配
        if(count($excel_headers[$contract_category]) != PHPExcel_Cell::columnIndexFromString($higest_data_col)){
            throw new Exception('',Enu::RESP_CONTRACT_CATEGORY_EXCEL_NOT_MATCH);
        }
        //校验表头单元格
        foreach($col_range as $c){
            $idx = PHPExcel_Cell::columnIndexFromString($c) - 1;
            $header_value = $excel_headers[$contract_category][$idx];
            $cell_local = $c."1";
            $cell_value =$worksheet->getCell($cell_local)->getValue();
            if($cell_value != $header_value){
                throw new Exception("请检查: ".$c."1,应为: ".$header_value,Enu::RESP_CONTRACT_CATEGORY_EXCEL_NOT_MATCH);
            }
        }

        //根据类型判断excel格式是否正确
        $excel_data =  [];
        for($r = 2; $r <= $higest_data_row; $r ++){
            $row_info = [];
            foreach($col_range as $c){
                $cell_local = $c.$r;
                $cell_value = $worksheet->getCell($cell_local)->getValue();
                $idx = PHPExcel_Cell::columnIndexFromString($c) - 1;
                $head = $excel_headers[$contract_category][$idx];
                $row_info[$head] = $cell_value;
            }
            $excel_data[$r] = self::check_row_and_return_data($contract_category, $row_info, $r, $pdf_name_map,$admin);
        }

        //校验数据库中是否存在
        $contract_nos = array_column($excel_data,'contract_no');
        $has_contract_no = ContractModel::hasContract($contract_nos);
        if($has_contract_no !== false){
            throw new Exception("合同编号: ".$has_contract_no."已经存在!",Enu::RESP_CONTRACT_CONTRACT_NO_DUPLICATION);
        }

        foreach($excel_data as $insert_data){
            //存入数据库
            ContractModel::add($insert_data);
        }

        return true;
    }


    /**
     * 校验数据并返回行数据
     *
     * @param $contract_category
     * @param $row_info
     * @param $row
     * @param $pdf_name_map
     * @param $admin
     *
     * @return array
     * @throws Exception
     * @static
     * <AUTHOR> 2024-03-01 15:23:35
     */
    private static function check_row_and_return_data($contract_category,$row_info,$row,$pdf_name_map,$admin){
        if(empty($row_info['合同编号'])){
            throw new Exception("数据异常: 第".$row."行, 合同编号 缺失",Enu::RESP_CONTRACT_MISSING_CONTRACT_NO);
        }
        if(!in_array($row_info['合同类型'],array_keys(self::$type))){
            throw new Exception("合同类型错误: ".$row,Enu::RESP_CONTRACT_TYPE_ERR);
        }
        if(!in_array($row_info['签约类型'],array_keys(self::$sign_type))){
            throw new Exception("合同类型错误: ".$row,Enu::RESP_CONTRACT_SIGN_ERR);
        }
        if(!isset($pdf_name_map[$row_info['合同编号']])){
            throw new Exception("数据异常: 第".$row."行, 合同文件 缺失",Enu::RESP_CONTRACT_MISSING_CONTRACT_FILE);
        }
        $url = $pdf_name_map[$row_info['合同编号']];

        $now = date("Y-m-d H:i:s");

        $dateBll = new PHPExcel_Shared_Date();//处理时间格式
        $contract_start_time = '';
        if(!empty($row_info['合同开始日期'])) {
            $contract_start_time = $dateBll->ExcelToPHP($row_info['合同开始日期']);
            $contract_start_time = date("Y-m-d 00:00:00", $contract_start_time);
        }

        $contract_end_time = '';
        if(!empty($row_info['合同结束日期'])) {
            $contract_end_time = $dateBll->ExcelToPHP($row_info['合同结束日期']);
            $contract_end_time = date("Y-m-d 23:59:59", $contract_end_time);
        }

        $customer_id = '';
        $customer_name = '';
        $company_name = '';
        $source_name = '';
        $source_full_name = '';
        $third_party_company_name = '';
        $product_name = '';
        $product_id = '';
        $third_product_name = '';
        $source_product_name = '';
        $salesman = '';
        if($contract_category == Enu::CONTRACT_CATEGORY_CUSTOMER){
            $customer_name = $row_info['客户名称'];
            //根据客户名称查询客户id
            $customer_info = CustomerModel::getInfoByCustomerName($customer_name);
            if(!empty($customer_info)){
                $customer_id = $customer_info['customer_id'];
                $salesman = $customer_info['salesman'];
            }
            $company_name = $row_info['公司名称'];
            $product_name = $row_info['签约产品（子产品）'];
            $_product_name = explode("\n",$row_info['签约产品（子产品）']);

            $product_list = ProductModel::getInfoByProductNames($_product_name);
            $product_ids = array_column($product_list, 'product_id');
            $product_id  = implode(",",$product_ids);
        }

        if($contract_category == Enu::CONTRACT_CATEGORY_SOURCE){
            $source_name         = $row_info['渠道简称'];
            $source_full_name    = $row_info['渠道全称'];
            $source_product_name = $row_info['签约渠道产品'];
            $salesman            = $row_info['商务跟进人'];
            //根据商务名字(汉字)查询商务名字(拼音)
            $salesman_info = SystemUserModel::getUserByRealname($salesman);
            $salesman = $salesman_info['username'];
        }

        if($contract_category == Enu::CONTRACT_CATEGORY_THRID){
            $customer_name = $row_info['客户名称'];
            //根据客户名称查询客户id
            $customer_info = CustomerModel::getInfoByCustomerName($customer_name);
            if(!empty($customer_info)){
                $customer_id = $customer_info['customer_id'];
                $salesman = $customer_info['salesman'];
            }
            $company_name = $row_info['公司名称'];
            $third_party_company_name = $row_info['第三方公司名称'];
            $third_product_name = $row_info['产品'];
        }

        $res = [
            'contract_no'              => $row_info['合同编号'],
            'contract_category'        => $contract_category,
            'contract_type'            => self::$type[$row_info['合同类型']],
            'sign_type'                => self::$sign_type[$row_info['签约类型']],
            'customer_id'              => $customer_id,
            'customer_name'            => $customer_name,
            'company_name'             => $company_name,
            'source_name'              => $source_name,
            'source_full_name'         => $source_full_name,
            'third_party_company_name' => $third_party_company_name,
            'product_id'               => $product_id,
            'product_name'             => $product_name,
            'third_product_name'       => $third_product_name,
            'source_product_name'      => $source_product_name,
            'salesman'                 => $salesman,
            'salesman_name'            => $row_info['商务跟进人'],
            'archive_num'              => $row_info['归档份数'],
            'file_url'                 => $url,
            'remark'                   => $row_info['备注'],
            'admin'                    => $admin,
            // 'delated_at'               => '',
            'created_at'               => $now,
            'updated_at'               => $now,
        ];

        if(!empty($contract_end_time)){
            $res['contract_end'] = $contract_end_time;
        }
        if(!empty($contract_start_time)){
            $res['contract_start'] = $contract_start_time;
        }

        return $res;
    }


    /**
     * 删除
     *
     * @throws Exception
     * <AUTHOR> 2024-02-22 13:57:08
     *
     * @static
     */
    public static function del($contract_no,$admin){
        if(empty($contract_no)){
            throw new Exception("",Enu::RESP_CONTRACT_MISSING_CONTRACT_NO);
        }

        $now = date("Y-m-d H:i:s");

        $where = [];
        $where[] = ['contract_no', '=', $contract_no];

        return ContractModel::edit($where,['delated_at' => $now,'admin' => $admin]);
    }


    /**
     * 下载文件
     *
     * @throws Exception
     * <AUTHOR> 2024-02-22 13:59:57
     *
     * @static
     */
    public static function company_list($company_name){

        $res = ContractModel::getListByCompanyName($company_name);

        return array_unique(array_column($res, 'company_name'));
    }

    /**
     * 导出excel
     *
     * <AUTHOR> 2024-02-22 13:57:24
     *
     * @static
     */
    public static function excel(){

    }

    /**
     * 下载文件
     *
     * <AUTHOR> 2024-02-22 13:59:57
     *
     * @static
     */
    public static function download(){

    }
}