<?php

namespace App\Logics\Approval;

use App\Logics\BaseLogic;
use App\Models\Approval\ApprovalModel;
use App\Models\Customer\AccountModel;
use App\Models\Customer\CustomerModel;
use App\Models\Product\ProductConfigModel;
use App\Models\Product\ProductModel;
use App\Models\Product\SubProductConfigModel;
use App\Repositories\Product\ProductConfigItemRepositorie;
use App\Repositories\Product\ProductConfigRepositorie;
use App\Repositories\Product\SubProductConfigItemRepositorie;
use App\Repositories\Product\SubProductConfigRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

/**
 * 审批相关逻辑
 */
class ApprovalLogic extends BaseLogic
{

    const approver_role = [
        Enu::USER_ROLE_TYPE_PRODUCT_MANAGER => Enu::USER_ROLE_TYPE_APPROVAL_PRODUCT_MANAGER,//产品 10 -> 90
        Enu::USER_ROLE_TYPE_OPERATOR        => Enu::USER_ROLE_TYPE_APPROVAL_OPERATOR,       //运营  3 -> 93
        Enu::USER_ROLE_TYPE_TECHNICIAN      => Enu::USER_ROLE_TYPE_APPROVAL_TECHNICIAN,     //技术  5 -> 95
    ];

    /**
     * 检查是否存在
     *
     * @param $type
     * @param $action
     * @param $group_id
     * @param $customer_id
     * @param $account_id
     * @param $product_id
     * @param $sub_product_id
     * @param $config_item_id
     * @param $config_item_source
     *
     * @return void
     * @throws Exception
     * @static
     * <AUTHOR> 2023-11-13 10:49:11
     */
    private static function check($type,$action,$group_id,$customer_id,$account_id,$product_id,$sub_product_id,$config_item_id,$config_item_source){
        //校验是否在审批中已经存在
        if($sub_product_id != 0){
            $product_id = ProductModel::getFatherId($sub_product_id);
        }
        $approval_info = ApprovalModel::hasApproval($type,$group_id,$customer_id,$account_id,$product_id,$sub_product_id,$config_item_id,$config_item_source);
        if($approval_info){
            throw new Exception('该配置正在审批中',Enu::RESP_APPROVAL_DUPLICATION);
        }

        if($action == Enu::APPROVAL_ACTION_DELETE) {
            $config_info = [];
            if (Enu::APPROVAL_TYPE_PRODUCT_CONFIG == $type) {
                $config_info = self::get_product_config_info($product_id, $sub_product_id, $config_item_id, $config_item_source);
            } else if (Enu::APPROVAL_TYPE_CUSTOMER_PRODUCT_CONFIG == $type) {
                $config_info = self::get_customer_config_info($product_id, $sub_product_id, $group_id, $customer_id, $account_id, $config_item_id, $config_item_source);
            }

            if (empty($config_info)) {
                throw new Exception("没有对应配置", Enu::RESP_CUSTOMER_PRODUCT_CONFIG_HAVE_NO_CONFIG);
            }
        }
    }



    /**
     * 添加审批
     *
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-10-09 18:21:22
     *
     * @static
     */
    public static function add($data){
        if(in_array($data['type'],[Enu::APPROVAL_TYPE_CUSTOMER_PRODUCT_CONFIG,Enu::APPROVAL_TYPE_PRODUCT_CONFIG])) {
            $sub_product_id = $data['sub_product_id'];
            if ($sub_product_id > 0) {
                $father_id          = ProductModel::getFatherId($sub_product_id);
                $data['product_id'] = $father_id;
            }
        }
        if($data['type'] == Enu::APPROVAL_TYPE_CUSTOMER_PRODUCT_CONFIG){
            //朴全主体,客户id
            list($group_id,$customer_id,$account_id) = self::get_superior_ids($data['group_id'],$data['customer_id'],$data['account_id']);
            $data['group_id']    = $group_id;
            $data['customer_id'] = $customer_id;
            $data['account_id']  = $account_id;
        }

        if($data['type'] != Enu::APPROVAL_TYPE_PRODUCT_CONFIG_ITEM){//添加配置项不进行校验
            //校验是否在审批中已经存在
            self::check($data['type'],$data['action'],$data['group_id'],$data['customer_id'],$data['account_id'],$data['product_id'],$data['sub_product_id'],$data['config_item_id'],$data['config_item_source']);
        }

        $ta = get_tmp_key($data['type'],$data['action']);
        //计算可审批角色
        switch($ta){
            case get_tmp_key(Enu::APPROVAL_TYPE_PRODUCT_CONFIG_ITEM, Enu::APPROVAL_ACTION_ADD)://产品配置项-添加
                $data['approver_role'] = Enu::USER_ROLE_TYPE_APPROVAL_TECHNICIAN;
                break;
            case get_tmp_key(Enu::APPROVAL_TYPE_PRODUCT_CONFIG, Enu::APPROVAL_ACTION_DELETE)://产品配置-修改
                //删除产品配置,只可以删除继承自主产品的配置项
                $config_item_info = ProductConfigItemRepositorie::getProductConfigItemById($data['config_item_id']);
                $data['approver_role'] = self::approver_role[$config_item_info['role']];
                break;
            case get_tmp_key(Enu::APPROVAL_TYPE_CUSTOMER_PRODUCT_CONFIG, Enu::APPROVAL_ACTION_DELETE)://客户产品配置-删除
            case get_tmp_key(Enu::APPROVAL_TYPE_PRODUCT_CONFIG, Enu::APPROVAL_ACTION_ADD)://产品配置-添加
            case get_tmp_key(Enu::APPROVAL_TYPE_PRODUCT_CONFIG, Enu::APPROVAL_ACTION_EDIT)://产品配置-修改
            case get_tmp_key(Enu::APPROVAL_TYPE_CUSTOMER_PRODUCT_CONFIG, Enu::APPROVAL_ACTION_EDIT)://客户产品配置-修改
            case get_tmp_key(Enu::APPROVAL_TYPE_CUSTOMER_PRODUCT_CONFIG, Enu::APPROVAL_ACTION_ADD)://客户产品配置-添加
                if($data['config_item_source'] == Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT){
                    $config_item_info = ProductConfigItemRepositorie::getProductConfigItemById($data['config_item_id']);
                }else{
                    $config_item_info = SubProductConfigItemRepositorie::getSubProductConfigItemById($data['config_item_id']);
                }
                $data['approver_role'] = self::approver_role[$config_item_info['role']];
                break;
            default:
                break;
        }


        $now_str = date(Enu::TIME_FORMAT);
        $data['approved_at'] = $now_str;
        $data['created_at']  = $now_str;
        $data['updated_at']  = $now_str;

        return ApprovalModel::add($data,true);
    }


    /**
     * 获取详情
     *
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-10-09 18:25:02
     *
     * @static
     */
    public static function info($approval_id){
        $where = [
            ['approval_id', '=', $approval_id],
        ];
        return ApprovalModel::getOne($where);
    }


    /**
     * 列表
     *
     * @param $where
     * @param $field
     * @param $page_size
     * @param $page
     * @param $order
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-10-09 18:26:11
     */
    public static function getApprovalList( $where = [],$field = ['*'],$page_size = 10,$page = 1,$order = ['id','desc']){
        $limit = $page_size;
        $offset = ($page - 1) * $limit;
        $page = ['limit'=>$limit,'offset'=>$offset];
        return ApprovalModel::getAll($where,$field,$page,$order);
    }


    /**
     * 总量
     *
     * @param $where
     *
     * @static
     * @return int|mixed
     * @throws Exception
     * <AUTHOR> 2023-10-09 18:26:16
     */
    public static function getApprovalCount($where){
        return ApprovalModel::getCount($where);
    }

    /**
     * 驳回
     *
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-10-09 18:22:01
     *
     * @static
     */
    public static function reject($approval_id,$comments,$admin){
        $now_str = date(Enu::TIME_FORMAT);

        $where = [
            ['approval_id', '=', $approval_id],
            ['status', '=', Enu::APPROVAL_STATUS_NEED_APPROVAL],
        ];
        $data = [
            'status'      => Enu::APPROVAL_STATUS_REJECT,
            'comments'    => $comments,
            'approver'    => $admin,
            'approved_at' => $now_str,
            'updated_at'  => $now_str,
        ];
        return ApprovalModel::edit($where,$data);
    }

    /**
     * 撤销
     *
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-10-09 18:22:01
     *
     * @static
     */
    public static function cancel($approval_id){
        $now_str = date(Enu::TIME_FORMAT);

        $where = [
            ['approval_id', '=', $approval_id],
            ['status', '=', Enu::APPROVAL_STATUS_NEED_APPROVAL],
        ];
        $data = [
            'status' => Enu::APPROVAL_STATUS_CANCEL,
            'approved_at' => $now_str,
            'updated_at'  => $now_str,
        ];
        return ApprovalModel::edit($where,$data);
    }


    /**
     *
     * 通过审批
     *
     * @return bool
     * @throws Exception
     * <AUTHOR> 2023-10-09 18:22:47
     *
     * @static
     */
    public static function pass($approval_id,$admin){
        try {
            $exec_res = self::exec_config($approval_id,$admin);
            if(!$exec_res){
                throw new Exception('应用更改失败',Enu::RESP_FAILED);
            }
        }catch(Exception $e){
            throw new Exception('应用更改失败:'.$e->getMessage(),$e->getCode());
        }


        $now_str = date(Enu::TIME_FORMAT);
        $where = [
            ['approval_id', '=', $approval_id],
            ['status', '=', Enu::APPROVAL_STATUS_NEED_APPROVAL],
        ];
        $data = [
            'status'      => Enu::APPROVAL_STATUS_PASS,
            'approver'    => $admin,
            'approved_at' => $now_str,
            'updated_at'  => $now_str,
        ];
        return ApprovalModel::edit($where,$data);
    }


    /**
     * 审批通过,执行配置修改
     *
     * @param $approval_id
     * @param $admin
     *
     * @return bool
     * @throws Exception
     * @static
     * <AUTHOR> 2023-10-09 18:32:48
     */
    private static function exec_config($approval_id,$admin){
        $info = self::info($approval_id);
        $type = $info['type'];
        $action = $info['action'];
        $ta = get_tmp_key($type,$action);

        $product_id         = $info['product_id'];
        $sub_product_id     = $info['sub_product_id'];
        $group_id           = $info['group_id'];
        $customer_id        = $info['customer_id'];
        $account_id         = $info['account_id'];
        $config_item_id     = $info['config_item_id'];
        $item_value         = $info['new_value'];
        $applicant          = $info['applicant'];
        $config_item_source = $info['config_item_source'];

        $config_path = ApprovalLogic::get_config_path($product_id,$sub_product_id,$group_id,$customer_id,$account_id);

        switch($ta){
            case get_tmp_key(Enu::APPROVAL_TYPE_PRODUCT_CONFIG_ITEM,Enu::APPROVAL_ACTION_ADD)://产品配置项-添加
                $res = self::add_product_item_approval_pass($sub_product_id == 0,$item_value);
                break;
            case get_tmp_key(Enu::APPROVAL_TYPE_PRODUCT_CONFIG, Enu::APPROVAL_ACTION_EDIT)://产品配置-修改
            case get_tmp_key(Enu::APPROVAL_TYPE_PRODUCT_CONFIG, Enu::APPROVAL_ACTION_ADD)://产品配置-添加
                $res = self::edit_or_add_product_config_approval_pass($action,$product_id,$sub_product_id,$config_item_id,$item_value,$applicant,$config_item_source,$config_path);
                break;
            case get_tmp_key(Enu::APPROVAL_TYPE_CUSTOMER_PRODUCT_CONFIG,Enu::APPROVAL_ACTION_EDIT)://客户产品配置-修改
            case get_tmp_key(Enu::APPROVAL_TYPE_CUSTOMER_PRODUCT_CONFIG,Enu::APPROVAL_ACTION_ADD)://客户产品配置-添加
                $res = self::edit_customer_product_approval_pass($product_id,$sub_product_id,$group_id,$customer_id,$account_id,$config_item_id,$item_value,$applicant,$config_item_source,$config_path);
                break;
            case get_tmp_key(Enu::APPROVAL_TYPE_PRODUCT_CONFIG,Enu::APPROVAL_ACTION_DELETE)://产品配置-删除 仅从主产品继承的配置项可以删除
                $res = self::delete_product_config_approval_pass($sub_product_id,$config_item_id,$admin);
                break;
            case get_tmp_key(Enu::APPROVAL_TYPE_CUSTOMER_PRODUCT_CONFIG,Enu::APPROVAL_ACTION_DELETE)://客户产品配置-删除
                $res = self::delete_customer_product_approval_pass($product_id,$sub_product_id,$group_id,$customer_id,$account_id,$config_item_id,$item_value,$applicant,$config_item_source,$config_path);
                break;
            default:
                $res = false;
                break;
        }
        return $res;
    }


    /**
     * 产品配置编辑 添加
     *
     * @param $action
     * @param $product_id
     * @param $sub_product_id
     * @param $config_item_id
     * @param $item_value
     * @param $applicant
     * @param $config_item_source
     * @param $config_path
     *
     * @return bool
     * @throws Exception
     * @static
     * <AUTHOR> 2023-11-02 10:45:30
     */
    private static function edit_or_add_product_config_approval_pass($action,$product_id,$sub_product_id,$config_item_id,$item_value,$applicant,$config_item_source,$config_path){
        if($sub_product_id > 0){//子产品
            if($action == Enu::APPROVAL_ACTION_EDIT){//更新
                $res = SubProductConfigRepositorie::edit($sub_product_id,$config_item_id,$item_value,$applicant,$config_item_source, $config_path);
            }else{//不存在 -> 添加
                $res = SubProductConfigRepositorie::add($sub_product_id,$config_item_id,$item_value,$applicant,$config_item_source, $config_path);
            }
        } else {//主产品
            if ($action == Enu::APPROVAL_ACTION_EDIT) {//更新
                $res = ProductConfigRepositorie::edit($product_id, $config_item_id, $item_value, $applicant, $config_path);
            } else {//不存在 -> 添加
                $res = ProductConfigRepositorie::add($product_id, $config_item_id, $item_value, $applicant, $config_path);
            }
        }
        return $res > 0;
    }


    /**
     * 删除产品配置 仅删除继承自主产品的配置项
     *
     * @param $sub_product_id
     * @param $config_item_id
     * @param $applicant
     * @param $config_item_source
     *
     * @static
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-11-07 11:26:37
     */
    private static function delete_product_config_approval_pass($sub_product_id,$config_item_id,$admin){
        return SubProductConfigModel::delSubProductConfig($sub_product_id,$config_item_id,$admin);
    }




    /**
     * 客户产品配置 通过审批 生效 编辑
     *
     * @return bool
     * @throws Exception
     * <AUTHOR> 2023-10-09 19:48:36
     *
     */
    private static function edit_customer_product_approval_pass($product_id, $sub_product_id, $group_id, $customer_id, $account_id, $config_item_id, $item_value, $applicant, $config_item_source,$config_path){
        //主产品配置
        if($sub_product_id == 0){
            $product_obj = new ProductConfigRepositorie();
            $config_info = $product_obj::infoWithGCA($product_id,$group_id,$customer_id,$account_id,$config_item_id);
        }else{//子产品配置
            $product_id = $sub_product_id;
            $product_obj = new SubProductConfigRepositorie();
            $config_info = $product_obj::infoWithGCA($product_id,$group_id,$customer_id,$account_id,$config_item_id,$config_item_source);
        }

        if(!$config_info){
            $res = $product_obj::addWithGCA($product_id, $group_id, $customer_id, $account_id, $config_item_id, $item_value, $applicant, $config_path, $config_item_source);
        }else{
            $res = $product_obj::editWithGCA($product_id, $group_id, $customer_id, $account_id, $config_item_id, $item_value, $applicant, $config_path);
        }

        return $res > 0;
    }


    /**
     * 删除客户产品配置
     *
     * @param $product_id
     * @param $sub_product_id
     * @param $group_id
     * @param $customer_id
     * @param $account_id
     * @param $config_item_id
     * @param $item_value
     * @param $applicant
     * @param $config_item_source
     * @param $config_path
     *
     * @static
     * @return bool
     * @throws Exception
     * <AUTHOR> 2023-11-07 18:21:45
     */
    private static function delete_customer_product_approval_pass($product_id, $sub_product_id, $group_id, $customer_id, $account_id, $config_item_id, $item_value, $applicant, $config_item_source,$config_path){
        $config_info = self::get_customer_config_info($product_id,$sub_product_id,$group_id,$customer_id,$account_id,$config_item_id,$config_item_source);
        if(empty($config_info)){
            throw new Exception("没有对应配置",Enu::RESP_CUSTOMER_PRODUCT_CONFIG_HAVE_NO_CONFIG);
        }

        //主产品配置
        if($sub_product_id == 0){
            $product_obj = new ProductConfigRepositorie();
        }else{//子产品配置
            $product_id = $sub_product_id;
            $product_obj = new SubProductConfigRepositorie();
        }

        $res = $product_obj::deleteWithGCA($product_id, $group_id, $customer_id, $account_id, $config_item_id, $applicant, $config_path);

        return $res > 0;
    }


    /**
     * 查询客户配置
     *
     * @param $product_id
     * @param $sub_product_id
     * @param $group_id
     * @param $customer_id
     * @param $account_id
     * @param $config_item_id
     * @param $config_item_source
     *
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-11-10 19:32:31
     */
    private static function get_customer_config_info($product_id,$sub_product_id,$group_id,$customer_id,$account_id,$config_item_id,$config_item_source){
        if($sub_product_id == 0){
            $product_obj = new ProductConfigRepositorie();
            $config_info = $product_obj::infoWithGCA($product_id,$group_id,$customer_id,$account_id,$config_item_id);
        }else{//子产品配置
            $product_id = $sub_product_id;
            $product_obj = new SubProductConfigRepositorie();
            $config_info = $product_obj::infoWithGCA($product_id,$group_id,$customer_id,$account_id,$config_item_id,$config_item_source);
        }

        return $config_info;
    }


    /**
     * 查询产品配置
     *
     * @param $product_id
     * @param $sub_product_id
     * @param $config_item_id
     * @param $config_item_source
     *
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2023-11-10 19:32:31
     */
    private static function get_product_config_info($product_id,$sub_product_id,$config_item_id,$config_item_source){
        if($sub_product_id == 0){
            $config_info = ProductConfigRepositorie::getProductConfigByProductIdAndItemId($product_id,$config_item_id);
        }else{//子产品配置
            $config_info = SubProductConfigRepositorie::getSubProductConfigByProductIdAndItemId($sub_product_id,$config_item_id,$config_item_source);
        }

        return $config_info;
    }

    /**
     * 产品添加配置项 审批通过
     *
     * @param $is_father_product
     * @param $new_value
     *
     * @return bool
     * @throws Exception
     * @static
     * <AUTHOR> 2023-10-19 11:09:40
     */
    private static function add_product_item_approval_pass($is_father_product,$new_value) {
        $data = json_decode($new_value,true);
        //主产品配置
        if($is_father_product){
            $id = ProductConfigItemRepositorie::createProductConfigItem($data);
        }else{//子产品配置
            $id = SubProductConfigItemRepositorie::createSubProductConfigItem($data);
        }
        return $id > 0;
    }


    /**
     * 获取上级id
     * 主体id,客户id,账号id 只存在一个 都为空返回空
     * 如果账号id存在 返回 主体id,客户id,账号id
     * 如果客户id存在 返回 主体id,客户id,''
     * 如果主体id存在 返回 主体id,'',''
     *
     * @param $group_id
     * @param $customer_id
     * @param $account_id
     *
     * @static
     * @return array
     * @throws Exception
     * <AUTHOR> 2023-11-02 14:31:04
     */
    private static function get_superior_ids($group_id,$customer_id,$account_id){
        if(empty($group_id) && empty($customer_id) && empty($account_id)){
            return [$group_id,$customer_id,$account_id];
        }

        if(!empty($account_id)){
            $account_info = AccountModel::getInfoByAccountId($account_id);
            $customer_id = $account_info['customer_id'];
        }

        if(!empty($customer_id)){
            $customer_info = CustomerModel::getInfoByCustomerId($customer_id);
            $group_id = $customer_info['group_id'];
        }

        return [$group_id,$customer_id,$account_id];
    }


    /**
     * 设置配置项是多选类型的话 更新前后差异比较
     */
    public static function compareOptions($old = "", $new = "", $item_options_map = ""){
        if(empty($old)){
            $before = [];//更新前
        }else{
            $before = explode(",", $old);//更新前
        }

        if(empty($new)){
            $after = [];//更新后
        }else{
            $after = explode(",", $new);//更新后
        }

        //组装一个更新前 更新后全量的数组
        $all_list = array_unique(array_merge($before, $after));
        sort($all_list);
        
        $item_options_map = json_decode($item_options_map, true);
        $item_options_map = array_column($item_options_map, 'label', 'value');
        $tmp = [];
        foreach ($all_list as $value){
            //不存在的话 使用一个占位符代替
            if(in_array($value, $before)){
                $row['before'] = isset($item_options_map[$value]) ? $item_options_map[$value] :"-";
            }else{
                $row['before'] = "--";
            }

            if(in_array($value, $after)){
                $row['after'] = isset($item_options_map[$value]) ? $item_options_map[$value] :"-";
            }else{
                $row['after'] = "--";
            }

            if($row['before'] == $row['after']){
                $row['eq'] = true;
            }else{
                $row['eq'] = false;
            }

            $tmp[] = $row;
        }

        return $tmp;
    }


    /**
     * 获取配置路径
     *
     * @param $product_id
     * @param $sub_product_id
     * @param $group_id
     * @param $customer_id
     * @param $account_id
     *
     * @static
     * @return string
     * <AUTHOR> 2023-11-02 17:36:41
     */
    public static function get_config_path($product_id, $sub_product_id, $group_id, $customer_id, $account_id) {
        $prefix = '';
        if ($product_id <= 0 && $sub_product_id <= 0) {
            return '';
        }

        if ($sub_product_id > 0) {
            $suffix = 'sub_product';
        } else {
            $suffix = 'product';
        }

        $flag = empty($group_id) ? '_' : 'g';
        $flag .= empty($customer_id) ? '_' : 'c';
        $flag .= empty($account_id) ? '_' : 'a';

        switch($flag){
            case 'g__':
                $prefix = 'group_';
                break;
            case 'gc_':
            case '_c_':
                $prefix = 'customer_';
                break;
            case 'gca':
            case '_ca':
                $prefix = 'account_';
                break;
        }

        return $prefix . $suffix;
    }
}