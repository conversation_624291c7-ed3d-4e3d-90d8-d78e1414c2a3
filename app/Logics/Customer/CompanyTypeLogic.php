<?php
namespace App\Logics\Customer;

use App\Logics\BaseLogic;
use App\Models\Customer\CompanyTypeModel;
use Exception;

/**
 * 公司类型
 */
class CompanyTypeLogic extends BaseLogic
{

    /**
     * 获取公司类型
     * 返回Element plus级联选择器结构
     *
     * @throws Exception
     */
    public static function getCompanyTypeMap(){
        $_company_type_list = CompanyTypeModel::getAllTypes();

        $_company_type_map = array_column($_company_type_list, 'name','id');

        $company_type_map = [];

        foreach($_company_type_list as $ct_info){
            $t_name = $ct_info['name'];
            if($ct_info['parent_id'] > 0) {
                $t_name = $_company_type_map[$ct_info['parent_id']].'--'.$ct_info['name'];
            }
            $company_type_map[$ct_info['id']] = $t_name;
        }

        return $company_type_map;
    }
}