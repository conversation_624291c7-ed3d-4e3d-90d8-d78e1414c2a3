<?php
namespace App\Logics\Customer;

use App\Logics\BaseLogic;
use App\Models\Customer\AccountModel;
use App\Models\Customer\CustomerModel;
use Exception;

/**
 * 客户
 */
class CustomerLogic extends BaseLogic
{


    /**
     * 获取客户-账号级联列表
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2023-11-06 16:47:27
     *
     * @static
     */
    public static function customer_account_list($group_id) {
        $res = [];

        //获取所有客户
        $customer_list = CustomerModel::getListWidthGroupId($group_id);
        $customer_ids = array_column($customer_list, 'customer_id');
        //获取所有账号
        $_account_list = AccountModel::getListByCustomerIds($customer_ids);
        $account_list = [];
        foreach($_account_list as $account_info){
            $account_list[$account_info['customer_id']][] = $account_info;
        }
        //拼装数据
        foreach($customer_list as $customer_info){
            $customer_id = $customer_info['customer_id'];
            $_account_list = [];
            if(isset($account_list[$customer_id])){
                foreach($account_list[$customer_id] as $account_info){
                    $_account_list[] = [
                        'value' =>  $account_info['account_id'],
                        'label' =>  $account_info['account_name'],
                    ];
                }
            }
            $tmp = [
                'value' =>  $customer_id,
                'label' =>  $customer_info['name'],
            ];
            if(!empty($_account_list)){
                $tmp['children'] = $_account_list;
            }
            $res[] = $tmp;
        }

        return $res;
    }


    public static function customerList($group_id) {
        $res = [];
        //获取所有客户
        $customer_list = CustomerModel::getListWidthGroupId($group_id);
        //拼装数据
        foreach($customer_list as $customer_info){
            $customer_id = $customer_info['customer_id'];
            $tmp = [
                'value' =>  $customer_id,
                'label' =>  $customer_info['name'],
            ];

            $res[] = $tmp;
        }

        return $res;
    }

    public static function accountList($customer_id) {
        $res = [];
        //获取所有账号
        $account_list = AccountModel::getListByCustomerId($customer_id);
        //拼装数据
        foreach($account_list as $account_info){
            $account_id = $account_info['account_id'];
            $tmp = [
                'value' =>  $account_id,
                'label' =>  $account_info['account_name'],
            ];

            $res[] = $tmp;
        }

        return $res;
    }

}