<?php
namespace App\Logics\System;

use App\Logics\BaseLogic;
use App\Models\Customer\AccountModel;
use App\Models\Customer\CustomerModel;
use App\Models\System\SystemNodeModel;
use App\Models\System\SystemRoleNodeModel;
use App\Models\System\SystemUserRoleModel;
use App\Utils\Helpers\Enu;
use Exception;

/**
 * 客户
 */
class RoleLogic extends BaseLogic
{

    /**
     * 获取登录用户角色
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2023-11-06 17:45:25
     *
     * @static
     */
    public static function get_user_role($username) {
        return SystemUserRoleModel::getUserRoles($username);
    }


    /**
     * 判断当前登录用户是否为超级管理员
     *
     * @param $username
     *
     * @static
     * @return bool
     * @throws Exception
     * <AUTHOR> 2023-11-09 12:09:31
     */
    public static function is_super_admin($username) {
        $roles = self::get_user_role($username);

        return in_array(Enu::USER_ROLE_TYPE_SUPER, $roles);
    }


    /**
     * 获取角色拥有权限的接口列表
     *
     * @param $roles
     *
     * @return array
     * @throws Exception
     * @static
     * <AUTHOR> 2024-03-06 14:25:28
     */
    public static function get_role_permission_routes($roles){
        //获取角色拥有权限的节点
        $node_ids = SystemRoleNodeModel::getAll([['roleid','in',$roles]]);
        $node_ids = array_column($node_ids, null,'nodeid');

        //获取配置后台根节点
        $fin_root_node = SystemNodeModel::getOne([['node','=','FinManage'],['status','=',1]]);
        $fin_nodes = SystemNodeModel::getAll([['pid','=',$fin_root_node['id']],['status','=',1]]);
        $_fin_nodes = array_column($fin_nodes, null,'id');
        $fin_nodes = [];
        foreach($_fin_nodes as $node_id => $node_info){
            if(isset($node_ids[$node_id])){
                $route = explode("@",$node_info['node'])[1];
                $fin_nodes[$route] = $node_info['name'];
            }
        }

        return $fin_nodes;
    }
}