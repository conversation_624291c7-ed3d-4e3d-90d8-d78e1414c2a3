<?php

namespace App\Logics\Product;

use App\Logics\BaseLogic;
use App\Logics\System\RoleLogic;
use App\Models\Product\ProductConfigItemModel;
use App\Models\Product\ProductModel;
use App\Utils\Helpers\Enu;
use Exception;

/**
 * 产品相关逻辑
 */
class ProductLogic extends BaseLogic
{

    /**
     * 根据主产品id 获取主产品列表和子产品列表
     *
     * @param $father_ids
     *
     * @return array[]
     * <AUTHOR> 2023-10-16 11:56:56
     */
    private static function split_products($father_ids = null){
        if(!is_null($father_ids) && !is_array($father_ids) ) {
            $father_ids = [$father_ids];
        }
        $products = ProductModel::getListByFatherids($father_ids);

        $product_list = [];
        $sub_product_list = [];
        foreach($products as $product){
            if($product['father_id'] == 0){
                $product_list[$product['product_id']] = $product;
            }else{
                $sub_product_list[$product['father_id']][$product['product_id']] = $product;
            }
        }

        return [$product_list,$sub_product_list];
    }

    /**
     * 产品级联下拉列表
     *
     * @return array
     * <AUTHOR> 2023-10-16 11:41:02
     *
     * @static
     */
    public static function options(){
        //暂时仅展示邦秒验产品数据
        list($product_list,$sub_product_list) = self::split_products([Enu::PRODUCT_BMY_PRODUCT_ID]);

        $products = [];
        foreach($product_list as $info){
            $product_id = intval($info['product_id']);
            $products_info = [
                'value' => $product_id,
                'label' => $info['product_name'],
            ];
            if(key_exists($product_id, $sub_product_list)){
                $children = [];
                foreach ($sub_product_list[$product_id] as $sub_info){
                    $_sub_info = [
                        'value' => intval($sub_info['product_id']),
                        'label' => $sub_info['product_name'],
                    ];
                    $children[] = $_sub_info;
                }
                $products_info['children'] = $children;
            }
            $products[] = $products_info;
        }

        return [
            'code' => Enu::RESP_OK,
            'data' => $products
        ];
    }


    /**
     * 获取产品列表
     *
     * @param $page
     * @param $page_size
     * @param $product_id array [主产品id,子产品id]
     *
     * @static
     * @return array
     * @throws Exception
     * <AUTHOR> 2023-10-16 11:48:13
     */
    public static function product_list($product_id,$page,$page_size,$admin){
        $where = [];
        $sub_product_id = 0;
        if($product_id){
            if(count($product_id) > 0){
                $where[] = ['product_id','=',$product_id[0]];
            }
            if(count($product_id) > 1){
                $sub_product_id = $product_id[1];
            }
        }

        $where[] = ['father_id','in',Enu::PRODUCT_FATHER_ID_ARRAY];

        $fields = ['product_id','product_name','father_id'];

        //查看主产品列表
        $_list = ProductModel::getProductList($where,$fields,$page_size,$page);
        $count = ProductModel::getProductCount($where);

        $list = [];
        $product_ids = [];
        foreach ($_list as $info){
            $product_ids[] = $info['product_id'];
        }

        if(!empty($product_ids)) {
            list($product_list, $sub_product_list) = self::split_products($product_ids);
            foreach ($product_list as $info) {
                $product_id = intval($info['product_id']);
                if (key_exists($product_id, $sub_product_list)) {
                    $children = [];
                    foreach ($sub_product_list[$product_id] as $sub_info) {
                        //如果参数没有子产品id 所有子产品都返回  如果传入子产品id 仅该子产品返回
                        if ($sub_product_id == 0 || $sub_info['product_id'] == $sub_product_id) {
                            $children[] = $sub_info;
                        }
                    }
                    $info['children'] = $children;
                }
                $list[] = $info;
            }
        }

        $res = [
            'list'                 => $list,
            'count'                => $count,
            'page'                 => $page,
            'page_size'            => $page_size,
            'can_edit_config_item' => RoleLogic::is_super_admin($admin),
        ];

        return ['code' => Enu::RESP_OK, 'data' => $res];
    }


    /**
     * 获取主产品的子产品id
     *
     * @param $product_id
     *
     * @static
     * @return array
     * @throws Exception
     * <AUTHOR> 2023-10-18 14:50:48
     */
    public static function get_sub_product_ids($product_id) {
        $list = ProductModel::getAll([['father_id','=',$product_id]]);
        return array_column($list, 'product_id');
    }


    /**
     * 获取主产品
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2023-10-24 14:53:04
     *
     * @static
     */
    public static function get_main_product_list(){
        //暂时只显示几个主要的主产品
        $show_main_product = [200, 210, 615, 1000, 1100, 10000];
        $list = ProductModel::getAll([['father_id','in',[0,401]]]);
        $products = [];
        foreach($list as $info) {
            $product_id    = intval($info['product_id']);
            if(!in_array($product_id, $show_main_product)){
                continue;
            }
            $products[] = [
                'value' => $product_id,
                'label' => $info['product_name'],
            ];
        }
        return $products;
    }
}