<?php

namespace App\Logics\Product;

use App\Logics\BaseLogic;
use App\Logics\System\RoleLogic;
use App\Models\Customer\AccountModel;
use App\Models\Customer\CustomerModel;
use App\Models\Product\AccountProductModel;
use App\Models\Product\ProductConfigItemModel;
use App\Models\Product\ProductModel;
use App\Utils\Helpers\Enu;
use Exception;

/**
 * 产品相关逻辑
 */
class AccountProductLogic extends BaseLogic
{


    /**
     * 查询主体下的客户的账号 或 客户的账号 或 账号,是否开通了主产品下的子产品 或 某个子产品
     *
     * @param $group_id
     * @param $customer_id
     * @param $account_id
     * @param $product_id
     * @param $sub_product_id
     *
     * @return bool
     * @throws Exception
     * @static
     * <AUTHOR> 2023-12-14 15:43:52
     */
    public static function has_product($group_id,$customer_id,$account_id,$product_id,$sub_product_id){
        $customer_id_arr = $account_id_arr = [];
        if(!empty($group_id)){
            $customer_id_arr = CustomerModel::getListByGroupIds([$group_id]);
            $customer_id_arr = array_column($customer_id_arr, 'customer_id');
        }

        if(!empty($customer_id)){
            $customer_id_arr = [$customer_id];
        }

        if(empty($account_id)){
            if(!empty($customer_id_arr)) {
                $account_id_arr = AccountModel::getListByAccountIds($customer_id_arr);
                $account_id_arr = array_column($account_id_arr, 'account_id');
            }else{
                return false;
            }
        }

        if(!empty($account_id)){
            $account_id_arr = [$account_id];
        }


        if(!empty($product_id) && empty($sub_product_id)){
            $sub_product_id_arr = ProductModel::getSubProductsByFatherId($product_id);
            $sub_product_id_arr = array_column($sub_product_id_arr, 'product_id');
        }else if(!empty($sub_product_id)){
            $sub_product_id_arr = [$sub_product_id];
        }else{
            return false;
        }

        $account_product_list = AccountProductModel::getAccountProByPidsAccIds($sub_product_id_arr,$account_id_arr);
        return !empty($account_product_list);
    }
}