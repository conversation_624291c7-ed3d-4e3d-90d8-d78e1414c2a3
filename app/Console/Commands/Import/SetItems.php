<?php

namespace App\Console\Commands\Import;

use App\Console\Commands\BaseCommand;
use App\Repositories\Product\ProductConfigItemRepositorie;
use App\Repositories\Product\SubProductConfigItemRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

/**
 * 添加配置项
 * php artisan Import\\SetItems b576d3cde50d8d39da6e7e135b129159
 *
 */
class SetItems extends BaseCommand
{
    private $type_map = [
        '单行文本框' => Enu::PRODUCT_CONFIG_ITEM_TYPE_INPUT,
        '多行文本框' => Enu::PRODUCT_CONFIG_ITEM_TYPE_MULTI_INPUT,
        '单选按钮' => Enu::PRODUCT_CONFIG_ITEM_TYPE_RADIO,
        '多选按钮' => Enu::PRODUCT_CONFIG_ITEM_TYPE_CHECKBOX,
        '时间元素' => Enu::PRODUCT_CONFIG_ITEM_TYPE_DATE,
        '富文本编辑框' => '',
        '单选下拉框' => Enu::PRODUCT_CONFIG_ITEM_TYPE_RADIO,
        '多选下拉框' => Enu::PRODUCT_CONFIG_ITEM_TYPE_CHECKBOX,
        '多选文本框' => Enu::PRODUCT_CONFIG_ITEM_TYPE_CHECKBOX,
    ];

    private $role_map = [
        '技术' => Enu::USER_ROLE_TYPE_TECHNICIAN,
        '运营' => Enu::USER_ROLE_TYPE_OPERATOR,
        '产品' => Enu::USER_ROLE_TYPE_PRODUCT_MANAGER,
    ];


    /** @var array[] 不使用的配置项 */
    private $ban_item_key = [
        [201, 'queryMode'],
        [213, 'queryMode'],
        [201, 'jump_over'],
        [202, 'jump_over'],
        [203, 'jump_over'],
        [204, 'jump_over'],
        [205, 'jump_over'],
        [206, 'jump_over'],
        [207, 'jump_over'],
        [208, 'jump_over'],
        [211, 'jump_over'],
        [212, 'jump_over'],
        [213, 'jump_over'],
        [214, 'jump_over'],
        [215, 'jump_over'],
        [216, 'jump_over'],
        [217, 'jump_over'],
        [218, 'jump_over'],
        [219, 'jump_over'],
        [220, 'jump_over'],
        [221, 'jump_over'],
        [222, 'jump_over'],
        [224, 'jump_over'],
        [225, 'jump_over'],
        [226, 'jump_over'],
        [230, 'jump_over'],
        [231, 'jump_over'],
        [232, 'jump_over'],
        [233, 'jump_over'],
        [234, 'jump_over'],
        [235, 'jump_over'],
        [236, 'jump_over'],
        [237, 'jump_over'],
        [238, 'jump_over'],
        [239, 'jump_over'],
        [240, 'jump_over'],
        [320, 'jump_over'],
        [321, 'jump_over'],
        [322, 'jump_over'],
        [323, 'jump_over'],
        [324, 'jump_over'],
        [325, 'jump_over'],
        [326, 'jump_over'],
        [327, 'jump_over'],
        [328, 'jump_over'],
        [329, 'jump_over'],
        [330, 'jump_over'],
        [331, 'jump_over'],
        [340, 'jump_over'],
        [900, 'jump_over'],
        [201, 'isVersion'],
        [230, 'isVersion'],
        [201, 'isIDNumEncryption'],
        [213, 'isIDNumEncryption'],
        [215, 'isIDNumEncryption'],
        [221, 'isIDNumEncryption'],
        [222, 'isIDNumEncryption'],
        [230, 'isIDNumEncryption'],
        [231, 'isIDNumEncryption'],
        [232, 'isIDNumEncryption'],
        [233, 'isIDNumEncryption'],
        [234, 'isIDNumEncryption'],
        [235, 'isIDNumEncryption'],
        [237, 'isIDNumEncryption'],
        [238, 'isIDNumEncryption'],
        [201, 'isencryption'],
        [202, 'isencryption'],
        [203, 'isencryption'],
        [204, 'isencryption'],
        [205, 'isencryption'],
        [206, 'isencryption'],
        [207, 'isencryption'],
        [208, 'isencryption'],
        [211, 'isencryption'],
        [212, 'isencryption'],
        [213, 'isencryption'],
        [214, 'isencryption'],
        [215, 'isencryption'],
        [216, 'isencryption'],
        [217, 'isencryption'],
        [218, 'isencryption'],
        [219, 'isencryption'],
        [220, 'isencryption'],
        [221, 'isencryption'],
        [222, 'isencryption'],
        [224, 'isencryption'],
        [225, 'isencryption'],
        [226, 'isencryption'],
        [230, 'isencryption'],
        [231, 'isencryption'],
        [232, 'isencryption'],
        [233, 'isencryption'],
        [234, 'isencryption'],
        [235, 'isencryption'],
        [236, 'isencryption'],
        [237, 'isencryption'],
        [238, 'isencryption'],
        [239, 'isencryption'],
        [240, 'isencryption'],
        [320, 'isencryption'],
        [321, 'isencryption'],
        [322, 'isencryption'],
        [323, 'isencryption'],
        [324, 'isencryption'],
        [325, 'isencryption'],
        [326, 'isencryption'],
        [327, 'isencryption'],
        [328, 'isencryption'],
        [329, 'isencryption'],
        [330, 'isencryption'],
        [331, 'isencryption'],
        [340, 'isencryption'],
        [900, 'isencryption'],
        [202, 'necessary_name'],
        [203, 'necessary_name'],
        [204, 'necessary_name'],
        [205, 'necessary_name'],
        [206, 'necessary_name'],
        [207, 'necessary_name'],
        [211, 'necessary_name'],
        [212, 'necessary_name'],
        [215, 'necessary_name'],
        [216, 'necessary_name'],
        [217, 'necessary_name'],
        [218, 'necessary_name'],
        [219, 'necessary_name'],
        [220, 'necessary_name'],
        [224, 'necessary_name'],
        [225, 'necessary_name'],
        [236, 'necessary_name'],
        [239, 'necessary_name'],
        [240, 'necessary_name'],
        [320, 'necessary_name'],
        [321, 'necessary_name'],
        [322, 'necessary_name'],
        [323, 'necessary_name'],
        [324, 'necessary_name'],
        [325, 'necessary_name'],
        [326, 'necessary_name'],
        [327, 'necessary_name'],
        [328, 'necessary_name'],
        [329, 'necessary_name'],
        [330, 'necessary_name'],
        [331, 'necessary_name'],
        [202, 'necessary_idnum'],
        [203, 'necessary_idnum'],
        [204, 'necessary_idnum'],
        [205, 'necessary_idnum'],
        [206, 'necessary_idnum'],
        [207, 'necessary_idnum'],
        [211, 'necessary_idnum'],
        [212, 'necessary_idnum'],
        [215, 'necessary_idnum'],
        [216, 'necessary_idnum'],
        [217, 'necessary_idnum'],
        [218, 'necessary_idnum'],
        [219, 'necessary_idnum'],
        [220, 'necessary_idnum'],
        [224, 'necessary_idnum'],
        [225, 'necessary_idnum'],
        [236, 'necessary_idnum'],
        [239, 'necessary_idnum'],
        [240, 'necessary_idnum'],
        [320, 'necessary_idnum'],
        [321, 'necessary_idnum'],
        [322, 'necessary_idnum'],
        [323, 'necessary_idnum'],
        [324, 'necessary_idnum'],
        [325, 'necessary_idnum'],
        [326, 'necessary_idnum'],
        [327, 'necessary_idnum'],
        [328, 'necessary_idnum'],
        [329, 'necessary_idnum'],
        [330, 'necessary_idnum'],
        [331, 'necessary_idnum'],
        [900, 'necessary_idnum'],
        [203, 'support_jindun'],
        [216, 'support_jindun'],
        [320, 'support_jindun'],
        [321, 'support_jindun'],
        [322, 'support_jindun'],
        [323, 'support_jindun'],
        [324, 'support_jindun'],
        [325, 'support_jindun'],
        [326, 'support_jindun'],
        [327, 'support_jindun'],
        [328, 'support_jindun'],
        [329, 'support_jindun'],
        [330, 'support_jindun'],
        [331, 'support_jindun'],
        [205, 'channel_switch'],
        [212, 'channel_switch'],
        [214, 'isNameEncryption'],
        [230, 'isNameEncryption'],
        [231, 'isNameEncryption'],
        [232, 'isNameEncryption'],
        [233, 'isNameEncryption'],
        [234, 'isNameEncryption'],
        [235, 'isNameEncryption'],
        [237, 'isNameEncryption'],
        [238, 'isNameEncryption'],
        [340, 'isNameEncryption'],
        [215, 'denominator'],
        [233, 'denominator'],
        [234, 'denominator'],
        [235, 'denominator'],
        [237, 'denominator'],
        [215, 'black_type_list'],
        [217, 'called_encryption'],
        [227, 'net_time_billing'],
        [227, 'need_switch'],
        [227, 'call_operator'],
        [227, 'no_valid_operator'],
        [230, 'denominator_cheap'],
        [230, 'call_jindun_score'],
        [231, 'denominator_cheap'],
        [231, 'call_jindun_score'],
        [232, 'call_jindun_score'],
        [233, 'denominator_cheap'],
        [233, 'denominator'],
        [233, 'call_jindun_score'],
        [234, 'denominator_cheap'],
        [234, 'denominator'],
        [234, 'call_jindun_score'],
        [235, 'denominator_cheap'],
        [235, 'denominator'],
        [235, 'call_jindun_score'],
        [237, 'denominator_cheap'],
        [237, 'denominator'],
        [237, 'call_jindun_score'],
        [238, 'call_jindun_score'],
        [901, 'temp'],
        [218, 'necessary_phone'],
        [224, 'segment_filter'],
        [227, 'segment_filter'],
        [310, 'time_out'],
        [310, 'only_one_pid'],
        [311, 'time_out'],
        [311, 'only_one_pid'],
        [312, 'time_out'],
        [312, 'only_one_pid'],
        [313, 'time_out'],
        [313, 'only_one_pid'],
        [314, 'time_out'],
        [314, 'only_one_pid'],
        [314, 'offer_subscore'],
        [315, 'time_out'],
        [315, 'only_one_pid'],
        [315, 'offer_subscore'],
        [316, 'time_out'],
        [316, 'only_one_pid'],
        [316, 'offer_subscore'],
        [317, 'temp'],
        [318, 'time_out'],
        [318, 'only_one_pid'],
        [41001, 'segment_filter'],
        [41001, 'no_valid_operator'],
        [41001, 'net_time_billing'],
        [41002, 'segment_filter'],
        [41002, 'no_valid_operator'],
        [41002, 'net_time_billing'],
        [202, 'need_real_operator'],
        [203, 'need_real_operator'],
        [213, 'need_real_operator'],
    ];

    private $parent_pids = [210, 1000];

    /**
     *
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-11-20 14:25:32
     *
     */
    public function handle()
    {
        // die('防止误操作');
        $token = $_SERVER['argv'][2];
        if ($token != 'b576d3cde50d8d39da6e7e135b129159') {
            echo '禁止使用!', PHP_EOL;
            die;
        }
        echo "导入配置项:" . PHP_EOL;
        $this->fmt_ban();
        $this->set_item();
    }


    private function fmt_ban()
    {
        $ban_item_key = [];

        foreach ($this->ban_item_key as $ban_info) {
            $ban_item_key[$ban_info[0]][] = $ban_info[1];
        }
        $this->ban_item_key = $ban_item_key;
    }


    /**
     * 导入配置项
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-11-20 11:40:58
     *
     */
    private function set_item()
    {
        $file_path = SYSTEM_PATH . 'storage/current_config_items.csv';
        $handle = fopen($file_path, 'r');
        echo "file path : ", $file_path, PHP_EOL;
        $headers = fgetcsv($handle);

        $product_config = $sub_product_config = [];

        while (($data = fgetcsv($handle)) !== FALSE) {

            $product_id = $data[1];//产品id
            $item_key = $data[2];//配置项key
            $item_name = $data[3];//配置项名称
            $role = $data[14];//角色
            $modify = $data[16];//是否可以被客户修改 1 可修改  2 不可修改 0 子产品无该属性
            $item_type = $data[4];//配置类型
            $item_options = $data[5];//配置选项
            $item_default = $data[6];//配置默认值
            $extend = $data[15];//是否可以被子产品继承 1:可继承  2:不可继承 0 子产品无该属性
            $config_scope = $data[17];//配置范围 1 可在客户,产品维度配置 2 仅在客户维度配置

            $role = $this->role_map[$role];
            $item_type = $this->type_map[$item_type];
            $item_options = $this->fmt_options($item_options);
            $item_default = $this->fmt_default($item_type, $item_default);


            if (isset($this->ban_item_key[$product_id]) && in_array($item_key, $this->ban_item_key[$product_id])) {
                echo $product_id, "\t", $item_key, "\t", "配置项已忽略", PHP_EOL;
                continue;
            }

            $item_data = [
                'product_id' => $product_id,
                'item_key' => $item_key,
                'item_name' => $item_name,
                'role' => $role,
                'modify' => $modify,
                'item_type' => $item_type,
                'item_options' => $item_options,
                'item_default' => $item_default,
                'config_scope' => $config_scope,
            ];
            if (in_array($data[1], $this->parent_pids)) {
                $item_data['extend'] = $extend;
                $product_config[] = $item_data;
            } else {
                $sub_product_config[] = $item_data;
            }
        }

        echo "主产品配置", PHP_EOL;
        foreach ($product_config as $data) {
            $data['item_options'] = json_encode($data['item_options'], JSON_UNESCAPED_UNICODE);
            $id = ProductConfigItemRepositorie::createProductConfigItem($data);
            echo $id, PHP_EOL;
        }
        echo "子产品配置", PHP_EOL;
        foreach ($sub_product_config as $data) {
            $data['item_options'] = json_encode($data['item_options'], JSON_UNESCAPED_UNICODE);
            $id = SubProductConfigItemRepositorie::createSubProductConfigItem($data);
            echo $id, PHP_EOL;
        }
        echo "完成", PHP_EOL;
    }

    /**
     * 配置选项
     *
     * @param $item_options
     *
     * @return array
     * <AUTHOR> 2023-11-20 12:17:57
     */
    private function fmt_options($item_options)
    {
        if (!empty($item_options)) {
            $item_options = json_decode($item_options, true);
            $res = [];
            foreach ($item_options as $io) {
                $res[] = [
                    'value' => $io['opt_val'],
                    'label' => $io['opt_name'],
                ];
            }
            return $res;
        }
        return $item_options;
    }

    /**
     * 默认值
     *
     * @param $item_type
     * @param $item_default
     *
     * @return string
     * <AUTHOR> 2023-11-20 12:17:52
     */
    private function fmt_default($item_type, $item_default)
    {
        if ($item_type == Enu::PRODUCT_CONFIG_ITEM_TYPE_CHECKBOX) {
            $item_default = trim($item_default, '"');
            $item_default = json_decode($item_default, true);
            if (empty($item_default)) {
                return '';
            }
            return implode(',', $item_default);
        }
        return $item_default;
    }
}


