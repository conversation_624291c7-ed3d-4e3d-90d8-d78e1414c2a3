<?php

namespace App\Console\Commands\Import;

use App\Console\Commands\BaseCommand;
use App\Models\Product\ProductModel;


/**
 * 获取配置项
 * php artisan Import\\GetCurrentItems
 * php artisan Import\\GetCurrentItems b576d3cde50d8d39da6e7e135b129159
 */
class GetCurrentItems extends BaseCommand
{

    private $type_map = [
        1 => '单行文本框',
        2 => '多行文本框',
        3 => '单选按钮',
        4 => '多选按钮',
        5 => '时间元素',
        6 => '富文本编辑框',
        7 => '单选下拉框',
        8 => '多选下拉框',
        9 => '多选文本框',
    ];

    private $operate_fields = [
        'channels',
        'use_cache',
        'tel_list',
        'channel_stop_status',
        'is_new_format',
        'version',
        'need_switch',
        'enable_back',
        'zyjc_province',
        'consistency_repair',
        'product_ids'
    ];

    public function handle()
    {
        // die('防止误操作');
        $token = $_SERVER['argv'][2];
        if ($token != 'b576d3cde50d8d39da6e7e135b129159') {
            echo '禁止使用!', PHP_EOL;
            die;
        }
        if (!isset($_SERVER['argv'][3]) || !is_numeric($_SERVER['argv'][3])) {
            echo '请输入产品id', PHP_EOL;
            die;
        }
        $product_id = $_SERVER['argv'][3];
        if (!in_array($product_id, [210, 1000])) {
            echo '产品ID错误' . PHP_EOL;
            die;
        }

        $this->current_config_item($product_id);
    }

    /**
     * 获取统计当前配置项
     *
     * @param $product_id
     *
     * @return void
     * <AUTHOR> 2023-11-16 19:58:16
     */
    private function current_config_item($product_id)
    {
        $info = ProductModel::getMainProductInfo($product_id);
        // var_dump($sub_product_list);
        $config_item_list = [];
        // foreach($sub_product_list as $info) {
        //     var_dump($info);
        //     die;
        $items = json_decode($info['data'], true);
        foreach ($items as $_item) {
            if (!in_array($_item['name'], $this->operate_fields)) {
                continue;
            }
            $item = [
                'product_name' => $info['product_name'],
                'product_id' => $info['product_id'],
                'name' => $_item['name'],
                'cn_name' => $_item['cn_name'],
                'type' => isset($this->type_map[$_item['type']]) ? $this->type_map[$_item['type']] : '--',
                'option' => isset($_item['option']) ? json_encode($_item['option'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) : '',
                'default' => $_item['default'],
                'is_need' => strval($_item['is_need']),
                'is_updated' => strval($_item['is_updated']),
                'order' => strval($_item['order']),
                'support_type' => strval($_item['support_type']),
                'limit' => strval($_item['limit']),
                'pw_type' => strval($_item['pw_type']),
                'client_show' => strval($_item['client_show']),
                'role' => '技术',
//                    'extend'       => $_item['name'] == 'out_fields' ? '2' : '1',//是否可以被子产品继承 1:可继承  2:不可继承 0 子产品无该属性
                'extend' => 2,
                'modify' => '1',//可修改
                'config_scope' => '2',//全部范围 配置范围 1 可在客户,产品维度配置 2 仅在客户维度配置
            ];
            // die;
            if ($_item['type'] == 4) {
                // dd(json_encode($_item['default']));
                // $item['default'] = implode(',',$_item['default']);
                $item['default'] = json_encode($_item['default']);
            }
            if ($_item['name'] == 'new_format_type') {
                $item['default'] = 1;
            }
            if ($_item['name'] == 'use_cache') {
                $item['default'] = 0;
            }


            $tk = get_tmp_key($info['product_id'], $_item['name']);
            // $config_item_list[$info['product_id']][] = $item;
            $config_item_list[$tk] = $item;
        }
        // }
        // die;

        $file_path = SYSTEM_PATH . 'storage/current_config_items.csv';
        if (file_exists($file_path)) {
            unlink($file_path);
            echo "存在", $file_path, "并已删除", PHP_EOL;
        }


        echo "file path : ", $file_path, PHP_EOL;
        $file = fopen($file_path, "w+");
        fwrite($file, "\xEF\xBB\xBF");//bom
        $header = "产品名称,产品id,配置项key,配置项名称,配置项类型,配置项选项,配置项默认值,is_need,is_updated,order,support_type,limit,pw_type,client_show,角色,是否可继承,是否可修改,配置范围";
        fputcsv($file, explode(",", $header));
        foreach ($config_item_list as $tk => $item_info) {
            fputcsv($file, $item_info);
        }
        fclose($file);
    }
}


