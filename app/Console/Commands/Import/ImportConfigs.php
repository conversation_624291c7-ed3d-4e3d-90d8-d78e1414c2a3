<?php

namespace App\Console\Commands\Import;

use App\Console\Commands\BaseCommand;
use App\Logics\Product\ProductLogic;
use App\Models\Customer\AccountModel;
use App\Models\Customer\CustomerModel;
use App\Models\Product\AccountProductModel;
use App\Models\Product\ProductConfigItemModel;
use App\Models\Product\SubProductConfigItemModel;
use App\Repositories\Product\ProductConfigRepositorie;
use App\Repositories\Product\SubProductConfigRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

/**
 * 导入配置
 * php artisan Import\\ImportConfigs b576d3cde50d8d39da6e7e135b129159
 */
class ImportConfigs extends BaseCommand
{

    /** @var array */
    private $items = [];

    /** @var */
    private $sub_product_ids;


    /** @var array[] 不使用的配置项 */
    private $ban_item_key = [
        [201, 'queryMode'],
        [213, 'queryMode'],
        [201, 'jump_over'],
        [202, 'jump_over'],
        [203, 'jump_over'],
        [204, 'jump_over'],
        [205, 'jump_over'],
        [206, 'jump_over'],
        [207, 'jump_over'],
        [208, 'jump_over'],
        [211, 'jump_over'],
        [212, 'jump_over'],
        [213, 'jump_over'],
        [214, 'jump_over'],
        [215, 'jump_over'],
        [216, 'jump_over'],
        [217, 'jump_over'],
        [218, 'jump_over'],
        [219, 'jump_over'],
        [220, 'jump_over'],
        [221, 'jump_over'],
        [222, 'jump_over'],
        [224, 'jump_over'],
        [225, 'jump_over'],
        [226, 'jump_over'],
        [230, 'jump_over'],
        [231, 'jump_over'],
        [232, 'jump_over'],
        [233, 'jump_over'],
        [234, 'jump_over'],
        [235, 'jump_over'],
        [236, 'jump_over'],
        [237, 'jump_over'],
        [238, 'jump_over'],
        [239, 'jump_over'],
        [240, 'jump_over'],
        [320, 'jump_over'],
        [321, 'jump_over'],
        [322, 'jump_over'],
        [323, 'jump_over'],
        [324, 'jump_over'],
        [325, 'jump_over'],
        [326, 'jump_over'],
        [327, 'jump_over'],
        [328, 'jump_over'],
        [329, 'jump_over'],
        [330, 'jump_over'],
        [331, 'jump_over'],
        [340, 'jump_over'],
        [900, 'jump_over'],
        [201, 'isVersion'],
        [230, 'isVersion'],
        [201, 'isIDNumEncryption'],
        [213, 'isIDNumEncryption'],
        [215, 'isIDNumEncryption'],
        [221, 'isIDNumEncryption'],
        [222, 'isIDNumEncryption'],
        [230, 'isIDNumEncryption'],
        [231, 'isIDNumEncryption'],
        [232, 'isIDNumEncryption'],
        [233, 'isIDNumEncryption'],
        [234, 'isIDNumEncryption'],
        [235, 'isIDNumEncryption'],
        [237, 'isIDNumEncryption'],
        [238, 'isIDNumEncryption'],
        [201, 'isencryption'],
        [202, 'isencryption'],
        [203, 'isencryption'],
        [204, 'isencryption'],
        [205, 'isencryption'],
        [206, 'isencryption'],
        [207, 'isencryption'],
        [208, 'isencryption'],
        [211, 'isencryption'],
        [212, 'isencryption'],
        [213, 'isencryption'],
        [214, 'isencryption'],
        [215, 'isencryption'],
        [216, 'isencryption'],
        [217, 'isencryption'],
        [218, 'isencryption'],
        [219, 'isencryption'],
        [220, 'isencryption'],
        [221, 'isencryption'],
        [222, 'isencryption'],
        [224, 'isencryption'],
        [225, 'isencryption'],
        [226, 'isencryption'],
        [230, 'isencryption'],
        [231, 'isencryption'],
        [232, 'isencryption'],
        [233, 'isencryption'],
        [234, 'isencryption'],
        [235, 'isencryption'],
        [236, 'isencryption'],
        [237, 'isencryption'],
        [238, 'isencryption'],
        [239, 'isencryption'],
        [240, 'isencryption'],
        [320, 'isencryption'],
        [321, 'isencryption'],
        [322, 'isencryption'],
        [323, 'isencryption'],
        [324, 'isencryption'],
        [325, 'isencryption'],
        [326, 'isencryption'],
        [327, 'isencryption'],
        [328, 'isencryption'],
        [329, 'isencryption'],
        [330, 'isencryption'],
        [331, 'isencryption'],
        [340, 'isencryption'],
        [900, 'isencryption'],
        [202, 'necessary_name'],
        [203, 'necessary_name'],
        [204, 'necessary_name'],
        [205, 'necessary_name'],
        [206, 'necessary_name'],
        [207, 'necessary_name'],
        [211, 'necessary_name'],
        [212, 'necessary_name'],
        [215, 'necessary_name'],
        [216, 'necessary_name'],
        [217, 'necessary_name'],
        [218, 'necessary_name'],
        [219, 'necessary_name'],
        [220, 'necessary_name'],
        [224, 'necessary_name'],
        [225, 'necessary_name'],
        [236, 'necessary_name'],
        [239, 'necessary_name'],
        [240, 'necessary_name'],
        [320, 'necessary_name'],
        [321, 'necessary_name'],
        [322, 'necessary_name'],
        [323, 'necessary_name'],
        [324, 'necessary_name'],
        [325, 'necessary_name'],
        [326, 'necessary_name'],
        [327, 'necessary_name'],
        [328, 'necessary_name'],
        [329, 'necessary_name'],
        [330, 'necessary_name'],
        [331, 'necessary_name'],
        [202, 'necessary_idnum'],
        [203, 'necessary_idnum'],
        [204, 'necessary_idnum'],
        [205, 'necessary_idnum'],
        [206, 'necessary_idnum'],
        [207, 'necessary_idnum'],
        [211, 'necessary_idnum'],
        [212, 'necessary_idnum'],
        [215, 'necessary_idnum'],
        [216, 'necessary_idnum'],
        [217, 'necessary_idnum'],
        [218, 'necessary_idnum'],
        [219, 'necessary_idnum'],
        [220, 'necessary_idnum'],
        [224, 'necessary_idnum'],
        [225, 'necessary_idnum'],
        [236, 'necessary_idnum'],
        [239, 'necessary_idnum'],
        [240, 'necessary_idnum'],
        [320, 'necessary_idnum'],
        [321, 'necessary_idnum'],
        [322, 'necessary_idnum'],
        [323, 'necessary_idnum'],
        [324, 'necessary_idnum'],
        [325, 'necessary_idnum'],
        [326, 'necessary_idnum'],
        [327, 'necessary_idnum'],
        [328, 'necessary_idnum'],
        [329, 'necessary_idnum'],
        [330, 'necessary_idnum'],
        [331, 'necessary_idnum'],
        [900, 'necessary_idnum'],
        [203, 'support_jindun'],
        [216, 'support_jindun'],
        [320, 'support_jindun'],
        [321, 'support_jindun'],
        [322, 'support_jindun'],
        [323, 'support_jindun'],
        [324, 'support_jindun'],
        [325, 'support_jindun'],
        [326, 'support_jindun'],
        [327, 'support_jindun'],
        [328, 'support_jindun'],
        [329, 'support_jindun'],
        [330, 'support_jindun'],
        [331, 'support_jindun'],
        [205, 'channel_switch'],
        [212, 'channel_switch'],
        [214, 'isNameEncryption'],
        [230, 'isNameEncryption'],
        [231, 'isNameEncryption'],
        [232, 'isNameEncryption'],
        [233, 'isNameEncryption'],
        [234, 'isNameEncryption'],
        [235, 'isNameEncryption'],
        [237, 'isNameEncryption'],
        [238, 'isNameEncryption'],
        [340, 'isNameEncryption'],
        [215, 'denominator'],
        [233, 'denominator'],
        [234, 'denominator'],
        [235, 'denominator'],
        [237, 'denominator'],
        [215, 'black_type_list'],
        [217, 'called_encryption'],
        [227, 'net_time_billing'],
        [227, 'need_switch'],
        [227, 'call_operator'],
        [227, 'no_valid_operator'],
        [230, 'denominator_cheap'],
        [230, 'call_jindun_score'],
        [231, 'denominator_cheap'],
        [231, 'call_jindun_score'],
        [232, 'call_jindun_score'],
        [233, 'denominator_cheap'],
        [233, 'denominator'],
        [233, 'call_jindun_score'],
        [234, 'denominator_cheap'],
        [234, 'denominator'],
        [234, 'call_jindun_score'],
        [235, 'denominator_cheap'],
        [235, 'denominator'],
        [235, 'call_jindun_score'],
        [237, 'denominator_cheap'],
        [237, 'denominator'],
        [237, 'call_jindun_score'],
        [238, 'call_jindun_score'],
        [901, 'temp'],
        [218, 'necessary_phone'],
        [224, 'segment_filter'],
        [227, 'segment_filter'],
        [310, 'time_out'],
        [310, 'only_one_pid'],
        [311, 'time_out'],
        [311, 'only_one_pid'],
        [312, 'time_out'],
        [312, 'only_one_pid'],
        [313, 'time_out'],
        [313, 'only_one_pid'],
        [314, 'time_out'],
        [314, 'only_one_pid'],
        [314, 'offer_subscore'],
        [315, 'time_out'],
        [315, 'only_one_pid'],
        [315, 'offer_subscore'],
        [316, 'time_out'],
        [316, 'only_one_pid'],
        [316, 'offer_subscore'],
        [317, 'temp'],
        [318, 'time_out'],
        [318, 'only_one_pid'],
        [41001, 'segment_filter'],
        [41001, 'no_valid_operator'],
        [41001, 'net_time_billing'],
        [41002, 'segment_filter'],
        [41002, 'no_valid_operator'],
        [41002, 'net_time_billing'],
        [202, 'need_real_operator'],
        [203, 'need_real_operator'],
        [213, 'need_real_operator'],
    ];

    /**
     *
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-11-20 15:01:21
     *
     */
    public function handle()
    {
        // die('防止误操作');
        $token = $_SERVER['argv'][2];
        if ($token != 'b576d3cde50d8d39da6e7e135b129159') {
            echo '禁止使用!', PHP_EOL;
            die;
        }

        if (!isset($_SERVER['argv'][3]) || !is_numeric($_SERVER['argv'][3])) {
            echo '请输入产品id', PHP_EOL;
            die;
        }
        $product_id = $_SERVER['argv'][3];
        if (!in_array($product_id, [210, 1000])) {
            echo '产品ID错误' . PHP_EOL;
            die;
        }

        echo "导入当前配置:" . PHP_EOL;
        $this->fmt_ban();

        // $this->sub_product_ids = ProductLogic::get_sub_product_ids($product_id);

        $this->get_items($product_id);
        $this->config($product_id);
    }


    private function fmt_ban()
    {
        $ban_item_key = [];

        foreach ($this->ban_item_key as $ban_info) {
            $ban_item_key[$ban_info[0]][] = $ban_info[1];
        }
        $this->ban_item_key = $ban_item_key;
    }


    /**
     * 获取配置项
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-11-20 15:07:44
     */
    private function get_items($product_id)
    {
        $product_items = ProductConfigItemModel::getAllProductConfigItem($product_id);
        foreach ($product_items as $_item) {
            $this->items[$_item['item_key']][$_item['product_id']] = $_item;
        }
        // $sub_product_items = SubProductConfigItemModel::getListByItemNameAndSubProductIds('',$this->sub_product_ids);
        // foreach($sub_product_items as $_item){
        //     $this->items[$_item['item_key']][$_item['product_id']] = $_item;
        // }
    }

    /**
     * 获取当前配置,导入配置后台
     *
     * @param $product_id
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-11-20 14:59:57
     */
    private function config($product_id)
    {
        $current_configs = AccountProductModel::getAccountProByPidsAccIds([$product_id]);
        $account_ids = array_column($current_configs, 'account_id');

        $account_list = AccountModel::getListByAccountIds($account_ids);
        $account_customer_id_map = [];
        $customer_ids = [];
        foreach ($account_list as $account_info) {
            $account_customer_id_map[$account_info['account_id']] = $account_info['customer_id'];
            $customer_ids[$account_info['customer_id']] = $account_info['customer_id'];
        }

        $customer_list = CustomerModel::getCustomerListByCustomerIdsNoPage($customer_ids);
        $customer_group_id_map = [];
        foreach ($customer_list as $customer_info) {
            if (!empty($customer_info['group_id'])) {
                $customer_group_id_map[$customer_info['customer_id']] = $customer_info['group_id'];
            }
        }

        $config_path = 'account_product';

        foreach ($current_configs as $cc) {
            $configs = json_decode($cc['data'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            foreach ($configs as $config => $value) {
                if (isset($this->items[$config][$cc['product_id']])) {
                    $config_item_source = Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_SUB;
                    $config_item_info = $this->items[$config][$cc['product_id']];
                } else {
                    if (isset($this->items[$config][$product_id])) {
                        $config_item_source = Enu::SUB_PRODUCT_CONFIG_ITEM_SOURCE_PRODUCT;
                        $config_item_info = $this->items[$config][$product_id];
                    } else {
                        echo $cc['product_id'], "\t", $cc['account_id'], "\t", $config, "\t", "配置项不存在", PHP_EOL;
                        continue;
                    }

                }

                if (isset($this->ban_item_key[$cc['product_id']]) && in_array($config, $this->ban_item_key[$cc['product_id']])) {
                    echo $cc['product_id'], "\t", $config, "\t", "配置项已忽略", PHP_EOL;
                    continue;
                }

                $sub_product_id = $cc['product_id'];
                $config_item_id = $config_item_info['id'];
                $customer_id = $account_customer_id_map[$cc['account_id']];
                $group_id = isset($customer_group_id_map[$customer_id]) ? $customer_group_id_map[$customer_id] : '';
                $account_id = $cc['account_id'];
                $item_value = is_array($value) ? implode(',', $value) : $value;

                $res = ProductConfigRepositorie::addWithGCA($sub_product_id, $group_id, $customer_id, $account_id, $config_item_id, $item_value, 'admin', $config_path, $config_item_source);
                if ($res <= 0) {
                    echo $res, PHP_EOL;
                }
            }
        }
    }
}


