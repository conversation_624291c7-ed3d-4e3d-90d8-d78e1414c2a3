<?php

namespace App\Console\Commands\Import;

use App\Console\Commands\BaseCommand;
use App\Logics\Config\SubProductConfigItemLogic;
use App\Models\Product\SubProductConfigItemModel;
use App\Models\Product\SubProductConfigModel;
use App\Repositories\Product\ProductConfigItemRepositorie;
use App\Repositories\Product\SubProductConfigItemRepositorie;
use App\Utils\Helpers\Enu;
use Exception;

/**
 * php artisan Import\\GetUnconfigInfo
 */
class GetUnconfigInfo extends BaseCommand
{


    /**
     * 查询有配置项没有配置的账号-子产品数据
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-12-12 17:16:50
     *
     */
    public function handle() {
        // die('防止误操作');
        $token = $_SERVER['argv'][2];
        if($token != 'b576d3cde50d8d39da6e7e135b129159'){
            echo '禁止使用!',PHP_EOL;die;
        }

        echo "查询有配置项没有配置的账号-子产品数据:".PHP_EOL;
        $this->get_unconfig_info();
    }


    /**
     * 查询有配置项没有配置的账号
     * 1 获取子产品配置项
     * 2 按子产品id查询帐号产品配置
     * 3 计算没有配置的配置项
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-12-12 17:17:38
     *
     */
    private function get_unconfig_info() {
        $_items = SubProductConfigItemModel::getAll([]);
        $items = array_column($_items,null, 'id');
        $item_ids = [];
        foreach($_items as $_item){
            $item_ids[$_item['product_id']][] = $_item['id'];
        }


        $_configs = SubProductConfigModel::getAll([]);
        $account_configs = [];
        foreach($_configs as $_config){
            $account_configs[$_config['sub_product_id']][$_config['account_id']][] = $_config['config_item_id'];
        }


        foreach($item_ids as $product_id => $item_id_arr){
            if(!isset($account_configs[$product_id])){
                echo $product_id," 没有进行配置",PHP_EOL;
                continue;
            }
            foreach($account_configs[$product_id] as $account_id => $config_id_arr){
                $diff = array_diff($item_id_arr,$config_id_arr);
                if(!empty($diff)){
                    foreach($diff as $item_id){
                        echo $account_id,"\t",$product_id,"\t",$items[$item_id]['item_key'],"\t",$items[$item_id]['item_name'],PHP_EOL;
                    }
                }
            }
        }
    }
}


