<?php

namespace App\Console\Commands\Import;

use App\Console\Commands\BaseCommand;
use App\Models\Approval\ApprovalModel;
use App\Models\Product\ProductConfigItemModel;
use App\Models\Product\ProductConfigModel;
use App\Models\Product\SubProductConfigItemModel;
use App\Models\Product\SubProductConfigModel;
use Exception;

/**
 * 清空配置项和配置
 * php artisan Import\\EmptyItemsAndConfigs
 */
class EmptyItemsAndConfigs extends BaseCommand
{

    /**
     *
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-11-20 14:25:32
     *
     */
    public function handle() {
        // die('防止误操作');
        $token = $_SERVER['argv'][2];
        if($token != 'b576d3cde50d8d39da6e7e135b129159'){
            echo '禁止使用!',PHP_EOL;die;
        }

        echo "清空配置项和配置:".PHP_EOL;

        $this->empty_items_and_configs();
    }


    /**
     * 导入配置项
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2023-11-20 11:40:58
     *
     */
    private function empty_items_and_configs() {
        $file = SYSTEM_PATH . 'storage/current_config_items.csv';
        if(file_exists($file)){
            unlink($file);
            echo $file,"\t已删除",PHP_EOL;
        }else{
            echo $file,"\t不存在",PHP_EOL;
        }


        $count = ProductConfigItemModel::getCount([]);
        if($count > 0) {
            $sql = 'delete from product_config_item where id > 0';
            $res = ProductConfigItemModel::exec($sql);
            if ($res > 0) {
                echo "product_config_item\t已清空", PHP_EOL;
            }
        }else{
            echo "product_config_item\t无数据", PHP_EOL;
        }

        $count = SubProductConfigItemModel::getCount([]);
        if($count > 0) {
            $sql = 'delete from sub_product_config_item where id > 0';
            $res = SubProductConfigItemModel::exec($sql);
            if($res > 0) {
                echo "sub_product_config_item\t已清空", PHP_EOL;
            }
        }else{
            echo "sub_product_config_item\t无数据", PHP_EOL;
        }


        $count = ProductConfigModel::getCount([]);
        if($count > 0) {
            $sql = 'delete from product_config where id > 0';
            $res = ProductConfigModel::exec($sql);
            if ($res > 0) {
                echo "product_config\t已清空", PHP_EOL;
            }
        }else{
            echo "product_config\t无数据", PHP_EOL;
        }

        $count = SubProductConfigModel::getCount([]);
        if($count > 0) {
            $sql = 'delete from sub_product_config where id > 0';
            $res = SubProductConfigModel::exec($sql);
            if ($res > 0) {
                echo "sub_product_config\t已清空", PHP_EOL;
            }
        }else{
            echo "sub_product_config\t无数据", PHP_EOL;
        }


        $count = SubProductConfigModel::getCount([]);
        if($count > 0) {
            $sql = 'delete from approval where id > 0';
            $res = ApprovalModel::exec($sql);
            if ($res > 0) {
                echo "approval\t已清空", PHP_EOL;
            }
        }else{
            echo "approval\t无数据", PHP_EOL;
        }
    }
}


