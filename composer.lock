{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "b3dfd42f9f977eb172155e424097e7ee", "packages": [{"name": "phpoffice/phpexcel", "version": "1.8.1", "source": {"type": "git", "url": "https://github.com/PHPOffice/PHPExcel.git", "reference": "372c7cbb695a6f6f1e62649381aeaa37e7e70b32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PHPExcel/zipball/372c7cbb695a6f6f1e62649381aeaa37e7e70b32", "reference": "372c7cbb695a6f6f1e62649381aeaa37e7e70b32", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-xml": "*", "ext-xmlwriter": "*", "php": ">=5.2.0"}, "type": "library", "autoload": {"psr-0": {"PHPExcel": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "http://blog.maartenballiauw.be"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://blog.rootslabs.net"}, {"name": "<PERSON>"}], "description": "PHPExcel - OpenXML - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "http://phpexcel.codeplex.com", "keywords": ["OpenXML", "excel", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PHPExcel/issues", "source": "https://github.com/PHPOffice/PHPExcel/tree/master"}, "abandoned": "phpoffice/phpspreadsheet", "time": "2015-05-01T07:00:55+00:00"}, {"name": "yulore/framework", "version": "1.0.0", "source": {"type": "git", "url": "*************************:packagist/yulore-framework.git", "reference": "94f134b33a0f83bf165c042c0d76c5f49f091ea8"}, "dist": {"type": "tar", "url": "https://packagist.dianhua.cn/dist/yulore/framework/yulore-framework-1.0.0-134a3a.tar", "reference": "94f134b33a0f83bf165c042c0d76c5f49f091ea8", "shasum": "cffae11b747db2a6880932d93090ced980bd716b"}, "require": {"php": ">=5.5.0"}, "type": "library", "autoload": {"psr-4": {"Yulore\\": "src"}, "files": ["src/helpers.php"]}, "license": ["MIT"], "authors": [{"name": "yang<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "yulore framework", "homepage": "http://gitlab.dev.yulore.com/packagist/yulore-framework", "time": "2022-06-30T11:19:13+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=5.6", "ext-json": "*", "ext-curl": "*"}, "platform-dev": [], "plugin-api-version": "2.3.0"}