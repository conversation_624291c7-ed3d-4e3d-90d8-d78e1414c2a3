# value		format									result
0.0,		"0.0",									"0.0"
0.0,		"0",									"0"
0,			"0.0",									"0.0"
0,			"0",									"0"
0,			"##0",									"000"
12,			"#.0#",									"12.0"
0.1,		"0.0",									"0.1"
0.1,		"0",									"0"
5.5555,		"0.###",								"5.556"
5.5555,		"0.0##",								"5.556"	
5.5555,		"0.00#",								"5.556"
5.5555,		"0.000",								"5.556"
5.5555,		"0.0000",								"5.5555"
12345.6789,	'"#,##0.00"',							'"12,345.68"'
12345.6789,	'"#,##0.000"',							'"12,345.679"'
12345.6789,	'"£ #,##0.00"',							'"£ 12,345.68"'
12345.6789,	'"$ #,##0.000"',						'"$ 12,345.679"'
5.6789,		'"#,##0.00"',							'"5.68"'
12000,		'"#,###"',								'"12,000"'
12000,		'"#,"',									'12'
12200000,	'"0.0,,"',								'12.2'		// Scaling test
0.08,		"0%",									"8%"
0.8,		"0%",									"80%"
2.8,		"0%",									"280%"
125.74,		'$0.00" Surplus";$-0.00" Shortage"',	"$125.74 Surplus"
-125.74,	'$0.00" Surplus";$-0.00" Shortage"',	"$-125.74 Shortage"
-125.74,	'$0.00" Surplus";$0.00" Shortage"',		"$125.74 Shortage"
5.25,		'# ???/???',							"5 1/4"		// Fraction
5.3,		'# ???/???',							"5 3/10"	// Vulgar Fraction
5.25,		'???/???',								"21/4"
123456789,	'(000) 0-0000-000',						"(001) 2-3456-789"
123456789,	'0 (+00) 0000 00 00 00',				"0 (+00) 0123 45 67 89"
123456789,	'0000:00:00',							"12345:67:89"
-123456789,	'0000:00:00',							"-12345:67:89"
1234567.89,	'0000:00.00',							"12345:67.89"
-1234567.89,'0000:00.00',							"-12345:67.89"
