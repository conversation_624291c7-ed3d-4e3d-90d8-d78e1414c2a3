# formula, expectedResultExcel, expectedResultOpenOffice
'=TRUE',	TRUE,	TRUE
'=1 + 2.5',	3.5,	3.5
'=2.5 + 1',	3.5,	3.5
'=1 - 2.5',	-1.5,	-1.5
'=2.5 - 1',	1.5,	1.5
'=3 > 1',	TRUE,	TRUE
'=3 > 3',	FALSE,	FALSE
'=1 > 3',	FALSE,	FALSE
'=3 < 1',	FALSE,	FALSE
'=3 < 3',	FALSE,	FALSE
'=1 < 3',	TRUE,	TRUE
'=3 = 1',	FALSE,	FALSE
'=3 = 3',	TRUE,	TRUE
'=1 = 1.0',	TRUE,	TRUE
'=3 >= 1',	TRUE,	TRUE
'=3 >= 3',	TRUE,	TRUE
'=1 >= 3',	FALSE,	FALSE
'=3 <= 1',	FALSE,	FALSE
'=3 <= 3',	TRUE,	TRUE
'=1 <= 3',	TRUE,	TRUE
'=3 <> 1',	TRUE,	TRUE
'=3 <> 3',	FALSE,	FALSE
'=1 <> 1.0',	FALSE,	FALSE
'="a" > "a"',	FALSE,	FALSE
'="A" > "A"',	FALSE,	FALSE
'="A" > "a"',	FALSE,	TRUE
'="a" > "A"',	FALSE,	FALSE
'="a" < "a"',	FALSE,	FALSE
'="A" < "A"',	FALSE,	FALSE
'="A" < "a"',	FALSE,	FALSE
'="a" < "A"',	FALSE,	TRUE
'="a" = "a"',	TRUE,	TRUE
'="A" = "A"',	TRUE,	TRUE
'="A" = "a"',	TRUE,	FALSE
'="a" = "A"',	TRUE,	FALSE
'="a" <= "a"',	TRUE,	TRUE
'="A" <= "A"',	TRUE,	TRUE
'="A" <= "a"',	TRUE,	FALSE
'="a" <= "A"',	TRUE,	TRUE
'="a" >= "a"',	TRUE,	TRUE
'="A" >= "A"',	TRUE,	TRUE
'="A" >= "a"',	TRUE,	TRUE
'="a" >= "A"',	TRUE,	FALSE
'="a" <> "a"',	FALSE,	FALSE
'="A" <> "A"',	FALSE,	FALSE
'="A" <> "a"',	FALSE,	TRUE
'="a" <> "A"',	FALSE,	TRUE
'="A" > "b"',	FALSE,	TRUE
'="a" > "b"',	FALSE,	FALSE
'="b" > "a"',	TRUE,	TRUE
'="b" > "A"',	TRUE,	FALSE
'="a2" > "a10"',	TRUE,	TRUE // Test natural sorting is not used
