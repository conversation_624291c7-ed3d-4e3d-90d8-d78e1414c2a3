<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitbe3d9b6e82306b48eaf6160f376f8bc8
{
    public static $files = array (
        '6e7607951e9875159fcccf73c8472e2c' => __DIR__ . '/..' . '/yulore/framework/src/helpers.php',
    );

    public static $prefixLengthsPsr4 = array (
        'Y' => 
        array (
            '<PERSON>lore\\' => 7,
        ),
        'A' => 
        array (
            'App\\' => 4,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Yulore\\' => 
        array (
            0 => __DIR__ . '/..' . '/yulore/framework/src',
        ),
        'App\\' => 
        array (
            0 => __DIR__ . '/../..' . '/app',
        ),
    );

    public static $prefixesPsr0 = array (
        'P' => 
        array (
            'PHPExcel' => 
            array (
                0 => __DIR__ . '/..' . '/phpoffice/phpexcel/Classes',
            ),
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitbe3d9b6e82306b48eaf6160f376f8bc8::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitbe3d9b6e82306b48eaf6160f376f8bc8::$prefixDirsPsr4;
            $loader->prefixesPsr0 = ComposerStaticInitbe3d9b6e82306b48eaf6160f376f8bc8::$prefixesPsr0;
            $loader->classMap = ComposerStaticInitbe3d9b6e82306b48eaf6160f376f8bc8::$classMap;

        }, null, ClassLoader::class);
    }
}
