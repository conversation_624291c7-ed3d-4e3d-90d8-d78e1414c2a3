{"defaultProvider": "claude-4-sonnet", "fallbackProvider": "o4-mini", "contexts": [{"match": "^app/.*\\.php$", "provider": "claude-4-sonnet"}, {"match": "^routes/.*\\.php$", "provider": "o4-mini"}, {"match": "^tests?/.*\\.php$", "provider": "claude-3.5-sonnet"}, {"match": "^resources/views/.*\\.(blade\\.php|html|twig)$", "provider": "o4-mini"}, {"match": "^config/.*\\.php$|^bootstrap/.*\\.php$", "provider": "o4-mini"}, {"match": "^database/.*\\.php$|^database/.*\\.(sql|seed|seeder\\.php)$", "provider": "claude-3.5-sonnet"}, {"match": "^composer\\.(json|lock)$", "provider": "claude-3.5-sonnet"}, {"match": ".*\\.(env|env\\.example)$", "provider": "o4-mini"}, {"match": ".*\\.md$|^doc/.*", "provider": "claude-3.5-sonnet", "alternates": ["gpt-4o", "o4-mini"]}, {"match": ".*\\.mdc$", "provider": "claude-4-sonnet", "alternates": ["gpt-4o", "o4-mini"]}, {"match": ".*\\.(json|yaml|yml)$", "provider": "o4-mini"}, {"match": "^public/.*\\.(js|ts|jsx|tsx|css|scss)$", "provider": "o4-mini"}]}