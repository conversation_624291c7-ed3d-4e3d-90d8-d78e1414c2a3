## 配置管理后台核心功能梳理

### 业务功能结构图

```mermaid
graph TD
    A["配置管理后台"] --> B["产品配置管理"]
    A --> C["客户配置管理"]
    A --> D["配置搜索查询"]
    A --> E["审批流程管理"]

    B --> B1["主产品配置"]
    B --> B2["子产品配置"]
    B1 --> B11["配置项定义"]
    B1 --> B12["默认配置值"]
    B2 --> B21["配置项继承"]
    B2 --> B22["配置项覆盖"]

    C --> C1["主体级配置"]
    C --> C2["客户级配置"]
    C --> C3["账号级配置"]
    C1 --> C11["group_id + product_id"]
    C2 --> C21["customer_id + product_id"]
    C3 --> C31["account_id + product_id"]

    D --> D1["客户产品列表"]
    D --> D2["配置搜索"]
    D --> D3["配置明细查看"]
    D1 --> D11["按主体筛选"]
    D1 --> D12["按产品筛选"]
    D2 --> D21["多维度搜索"]
    D2 --> D22["配置项搜索"]

    E --> E1["配置变更审批"]
    E --> E2["审批状态管理"]
    E1 --> E11["新增配置"]
    E1 --> E12["修改配置"]
    E1 --> E13["删除配置"]
```

根据代码分析，该配置管理后台主要服务于金融产品的多层级配置管理，核心功能如下：

### 1. **层次化配置体系**

#### 1.1 配置层级结构

- **产品默认配置** (`item_default`) - 最基础层级
- **主产品配置** (`product`) - 产品全局配置
- **主体产品配置** (`group_product`) - 主体维度配置
- **客户产品配置** (`customer_product`) - 客户维度配置
- **账号产品配置** (`account_product`) - 账号维度配置

#### 1.2 配置继承机制

- 子产品可继承主产品配置项 (`extend = 1`)
- 支持配置项覆盖和优先级处理
- 基于 `config_path` 的配置路径管理

### 2. **产品配置管理**

#### 2.1 主产品配置项管理 (`ProductConfigItemModel`)

```php
- 配置项类型：text, radio, checkbox, select 等
- 角色权限控制：运营(op)、技术(te)、产品(pm)
- 配置范围：是否允许客户修改 (modify)
- 继承属性：子产品是否可继承 (extend)
```

#### 2.2 子产品配置项管理 (`SubProductConfigItemModel`)

```php
- 支持从主产品继承配置项
- 独立的配置项定义
- 配置项来源标识 (config_item_source)
```

### 3. **多维度客户配置**

#### 3.1 三级配置体系

- **主体配置** (`group_id`) - 企业集团级别配置
- **客户配置** (`customer_id`) - 单个客户配置
- **账号配置** (`account_id`) - 具体账号配置

#### 3.2 配置优先级

按优先级从高到低：账号配置 > 客户配置 > 主体配置 > 产品默认配置

### 4. **配置搜索与查询功能**

#### 4.1 客户产品列表 (`CustomerProductController`)

- 多维度筛选：主体、客户、账号、产品
- 分页展示和合并计算
- 产品开通状态查询

#### 4.2 配置搜索 (`SearchController`)

- 支持按配置项名称搜索
- 按产品维度过滤
- 配置统计和明细展示

#### 4.3 配置明细查看

- 主体维度明细 (`getGroupDetailList`)
- 客户维度明细 (`getCusDetailList`)
- 账号维度明细 (`getAccDetailList`)

### 5. **审批流程管理**

#### 5.1 配置变更审批

```php
审批类型：APPROVAL_TYPE_CUSTOMER_PRODUCT_CONFIG
支持操作：
- ADD: 新增配置
- EDIT: 修改配置
- DELETE: 删除配置
```

#### 5.2 审批流程特点

- 所有配置变更需要审批
- 支持审批前后值对比
- 审批完成后自动更新配置

### 6. **权限与安全控制**

#### 6.1 角色权限管理

- 基于用户角色的配置项访问控制
- 配置项的可修改性控制 (`modify`)
- 产品开通状态验证

#### 6.2 数据安全

- 软删除机制 (`deleted_at`)
- 操作审计日志
- 配置变更追踪

### 7. **业务应用场景**

#### 7.1 金融产品配置

- 利率配置、费率配置
- 产品参数个性化设置
- 客户专属配置管理

#### 7.2 多租户支持

- 支持企业集团、子公司、部门账号的层级管理
- 配置隔离和继承
- 灵活的权限分配

### 8. **技术架构特点**

#### 8.1 数据模型设计

- 主产品配置表 (`product_config`)
- 子产品配置表 (`sub_product_config`)
- 配置项定义表 (`product_config_item`, `sub_product_config_item`)

#### 8.2 业务逻辑分层

- Controller 层：接口控制和参数验证
- Logic 层：复杂业务逻辑处理
- Repository 层：数据访问封装
- Model 层：数据模型定义

这个配置管理后台为金融产品提供了灵活、安全、可控的多层级配置管理能力，支持复杂的企业级配置需求和审批流程管理。
