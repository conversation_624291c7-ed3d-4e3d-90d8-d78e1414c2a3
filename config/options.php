<?php
//各种状态,表中的枚举 对应关系
use App\Utils\Helpers\Enu;
return [
    /** 公司名称 */
    'dhb_sign_corp' => [
        Enu::DHB_SIGN_CORP_DEBAOTC => '北京德宝天辰信息技术有限公司',
        Enu::DHB_SIGN_CORP_YIXIN   => '翊新故事科技有限公司',
        Enu::DHB_SIGN_CORP_YULORE  => '北京羽乐创新科技有限公司',
    ],
    'group' => [
        'status' => [//客户状态
            Enu::CUSTOMER_GROUP_STATUS_ENABLE    => '可用',
            Enu::CUSTOMER_GROUP_STATUS_DISENABLE => '禁用',
        ],
    ],
    /** 客户相关 */
    'customer' => [
        'status' => [//客户状态
            Enu::CUSTOMER_STATUS_ENABLE    => '可用',
            Enu::CUSTOMER_STATUS_DISENABLE => '禁用',
        ],
        'type' => [//客户类型
            Enu::CUSTOMER_TYPE_FIN => '金融客户',
            Enu::CUSTOMER_TYPE_COM => '企服客户',
        ],
        'payment_type' => [//付费类型
            Enu::CUSTOMER_PAYMENT_TYPE_PREPAID  => '预付费',
            Enu::CUSTOMER_PAYMENT_TYPE_POSTPAID => '后付费',
        ],
        'reconciliation_cycle' => [//客户对账周期
            Enu::CUSTOMER_RECONCILIATION_CYCLE_MONTHLY   => '月度',
            Enu::CUSTOMER_RECONCILIATION_CYCLE_QUARTERLY => '季度',
            Enu::CUSTOMER_RECONCILIATION_CYCLE_ANNUALLY  => '年度',
        ],
        'sign_type' => [//征信客户分类
            Enu::CUSTOMER_SIGN_TYPE_DHB => '电话邦签约',
            Enu::CUSTOMER_SIGN_TYPE_PD  => '朴道签约',
            Enu::CUSTOMER_SIGN_TYPE_ZSJ => '浙数交签约',
        ],
        'contract_status' => [//客户签约状态
            Enu::CUSTOMER_CONTRACT_STATUS_PAID    => '已签约已付款',
            Enu::CUSTOMER_CONTRACT_STATUS_SIGNED  => '已签约未付款',
            Enu::CUSTOMER_CONTRACT_STATUS_UNSIGN  => '未签约',
            Enu::CUSTOMER_CONTRACT_STATUS_OTHER   => '其他',
            Enu::CUSTOMER_CONTRACT_STATUS_SPECIAL => '特殊客户',
        ],
        'channel_mode' => [//是否为渠道客户
            Enu::CUSTOMER_CHANNEL_MODE_CHANNEL_CUSTOMER => '渠道客户',
            Enu::CUSTOMER_CHANNEL_MODE_JUST_CUSTOMER => '非渠道客户',//(直客)
        ],
        'source_id' => [//渠道跟进人可见数
            -1 => '全部',
            Enu::CUSTOMER_SIGN_TYPE_DHB => '电话邦签约',
            Enu::CUSTOMER_SIGN_TYPE_PD  => '朴道签约',
            Enu::CUSTOMER_SIGN_TYPE_ZSJ => '浙数交签约',
        ],
        'email_type' => [//账单发送邮件类型
            Enu::CUSTOMER_EMAIL_TYPE_STANDARD => '标准',
            Enu::CUSTOMER_EMAIL_TYPE_NOT_STANDARD => '非标准'
        ],
    ],
    'config_path' => [
        Enu::CONFIG_DIM_GROUP_PRODUCT        => '主体-主产品',
        Enu::CONFIG_DIM_CUSTOMER_PRODUCT     => '客户-主产品',
        Enu::CONFIG_DIM_ACCOUNT_PRODUCT      => '账号-主产品',
        Enu::CONFIG_DIM_GROUP_SUB_PRODUCT    => '主体-子产品',
        Enu::CONFIG_DIM_CUSTOMER_SUB_PRODUCT => '客户-子产品',
        Enu::CONFIG_DIM_ACCOUNT_SUB_PRODUCT  => '账号-子产品',
    ],
    // 'config' => [
    //     Enu::CONFIG_STATUS_NONE     => '无配置,无审批',
    //     Enu::CONFIG_STATUS_APPROVAL => '审批中',
    //     Enu::CONFIG_STATUS_CONFIG   => '已经配置',
    // ],
    /** 产品配置 */
    'product_config' => [
        'role' => [
            Enu::USER_ROLE_TYPE_TECHNICIAN      => '技术',
            Enu::USER_ROLE_TYPE_OPERATOR        => '运营',
            Enu::USER_ROLE_TYPE_PRODUCT_MANAGER => '产品',
        ],
        'modify' => [
            Enu::PRODUCT_CONFIG_ITEM_CUSTOMER_CAN_MODIFY     => '是',//'可被客户修改',
            Enu::PRODUCT_CONFIG_ITEM_CUSTOMER_CAN_NOT_MODIFY => '否',//'不可被客户修改',
        ],
        'extend' => [
            Enu::PRODUCT_CONFIG_ITEM_SUB_CAN_EXTEND     => '是',//'子产品可继承',
            Enu::PRODUCT_CONFIG_ITEM_SUB_CAN_NOT_EXTEND => '否',//'子产品不可继承',
        ],
        'item_type' => [//配置项类型
            Enu::PRODUCT_CONFIG_ITEM_TYPE_RADIO       => '单选',
            Enu::PRODUCT_CONFIG_ITEM_TYPE_CHECKBOX    => '多选',
            Enu::PRODUCT_CONFIG_ITEM_TYPE_INPUT       => '单行输入',
            Enu::PRODUCT_CONFIG_ITEM_TYPE_MULTI_INPUT => '多行输入',
            Enu::PRODUCT_CONFIG_ITEM_TYPE_DATE        => '日期',
            // Enu::PRODUCT_CONFIG_ITEM_TYPE_TIME        => '时间',
        ],
        'config_scope' => [//配置范围
            Enu::PRODUCT_CONFIG_ITEM_PRODUCT_CAN_EDIT     => '可在所有维度配置',
            Enu::PRODUCT_CONFIG_ITEM_PRODUCT_CAN_NOT_EDIT => '不可在产品维度配置',
        ],
    ],
    /** 子产品配置 */
    'sub_product_config' => [
        'role' => [
            Enu::USER_ROLE_TYPE_TECHNICIAN      => '技术',
            Enu::USER_ROLE_TYPE_OPERATOR        => '运营',
            Enu::USER_ROLE_TYPE_PRODUCT_MANAGER => '产品',
        ],
        'modify' => [
            Enu::PRODUCT_CONFIG_ITEM_CUSTOMER_CAN_MODIFY     => '是',//'可被客户修改',
            Enu::PRODUCT_CONFIG_ITEM_CUSTOMER_CAN_NOT_MODIFY => '否',//'不可被客户修改',
        ],
        'item_type' => [//配置项类型
            Enu::PRODUCT_CONFIG_ITEM_TYPE_RADIO       => '单选',
            Enu::PRODUCT_CONFIG_ITEM_TYPE_CHECKBOX    => '多选',
            Enu::PRODUCT_CONFIG_ITEM_TYPE_INPUT       => '单行输入',
            Enu::PRODUCT_CONFIG_ITEM_TYPE_MULTI_INPUT => '多行输入',
            Enu::PRODUCT_CONFIG_ITEM_TYPE_DATE        => '日期',
            // Enu::PRODUCT_CONFIG_ITEM_TYPE_TIME        => '时间',
        ],
        'config_scope' => [//配置范围
            Enu::PRODUCT_CONFIG_ITEM_PRODUCT_CAN_EDIT     => '可在所有维度配置',
            Enu::PRODUCT_CONFIG_ITEM_PRODUCT_CAN_NOT_EDIT => '不可在产品维度配置',
        ],
    ],
    'approval' => [
        'status' => [
            Enu::APPROVAL_STATUS_NEED_APPROVAL => '待审批',
            Enu::APPROVAL_STATUS_PASS          => '已通过',
            Enu::APPROVAL_STATUS_REJECT        => '已驳回',
            Enu::APPROVAL_STATUS_CANCEL        => '已撤销',
        ],
        'type' => [
            Enu::APPROVAL_TYPE_PRODUCT_CONFIG          => '产品配置',
            Enu::APPROVAL_TYPE_CUSTOMER_PRODUCT_CONFIG => '客户产品配置',
            Enu::APPROVAL_TYPE_PRODUCT_CONFIG_ITEM     => '产品配置项',
        ],
        'action' => [
            Enu::APPROVAL_ACTION_ADD    => '添加',
            Enu::APPROVAL_ACTION_DELETE => '删除',
            Enu::APPROVAL_ACTION_EDIT   => '编辑',
        ],
        'approval_role' => [

        ],
    ],
];