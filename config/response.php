<?php
use App\Utils\Helpers\Enu;
return [
    'status' => [
        Enu::RESP_OK                  => '成功',
        Enu::RESP_FAILED              => '失败',

        // 10XX 用户相关错误
        Enu::RESP_USER_MISSING_TOKEN         => '缺少token',
        Enu::RESP_USER_TOKEN_EXPIRED         => '用户token过期',
        Enu::RESP_USER_STATUS_ERR            => '用户状态不可用',
        Enu::RESP_USER_NOT_FOND              => '找不到用户',
        Enu::RESP_USER_HAVE_NO_AUTHORIZATION => '没有权限进行该操作',

        // 1[1-9]XX 主体,客户,账号相关的错误
        // 11XX 客户
        Enu::RESP_CUSTOMER_MISSING_NAME        => '缺少客户名称',
        Enu::RESP_CUSTOMER_USED                => '客户名称已存在',
        Enu::RESP_CUSTOMER_BILL_EMAIL_LIMIT    => '账单收件人最多只允许设置8个邮箱',
        Enu::RESP_CUSTOMER_BILL_CC_EMAIL_LIMIT => '账单抄送人最多只允许设置20个邮箱',
        Enu::RESP_CUSTOMER_EMAIL_TYPE_ERR      => '不是标准的邮箱格式',
        Enu::RESP_CUSTOMER_MISSING_ID          => '请选择客户',
        Enu::RESP_CUSTOMER_MISSING_COMPANY     => '请选择客户公司',
        Enu::RESP_CUSTOMER_MISSING_GROUP       => '请选择客户主体',
        Enu::RESP_CUSTOMER_CAN_NOT_FIND        => '客户已经禁用',
        Enu::RESP_CUSTOMER_NOTHING_CHANGE      => '客户无修改',
        Enu::RESP_CUSTOMER_UPDATE_FAIL         => '客户更新失败,请重试',

        //12XX 历史商务
        Enu::RESP_CUSTOMER_SALESMAN_SAME_MONTH => '商务跟进人切换间隔应大于一个月',

        //13XX 主体
        Enu::RESP_GROUP_MISSING_GROUP_ID       => '请传入主体id',
        Enu::RESP_GROUP_MISSING_CUSTOMER_ID    => '请传入客户id',
        Enu::RESP_GROUP_WORNG_CUSTOMER         => '客户状态错误',
        Enu::RESP_GROUP_CUSTOMER_BIND          => '客户已经关联主体',
        Enu::RESP_GROUP_MISSING_GROUP_NAME     => '请传入主体名称',
        Enu::RESP_GROUP_DUPLICATION_GROUP_NAME => '主体名称重复',

        //14XX 产品
        Enu::RESP_PRODUCT_MISSING_PRODUCT_ID       => '缺少产品id',
        Enu::RESP_PRODUCT_MISSING_PRODUCT_NAME     => '缺少产品名称',
        Enu::RESP_PRODUCT_MISSING_PRODUCT_KEY      => '缺少产品key',
        Enu::RESP_PRODUCT_DUPLICATION_PRODUCT_NAME => '产品名称重复',
        Enu::RESP_PRODUCT_DUPLICATION_PRODUCT_ID   => '产品id重复',


        //15XX 产品配置项
        Enu::RESP_PRODUCT_CONFIG_ITEM_MISSING_ID                       => '缺少产品配置项id',
        Enu::RESP_PRODUCT_CONFIG_ITEM_MISSING_KEY                      => '缺少产品配置项key',
        Enu::RESP_PRODUCT_CONFIG_ITEM_MISSING_NAME                     => '缺少产品配置项名称',
        Enu::RESP_PRODUCT_CONFIG_ITEM_MISSING_ROLE                     => '缺少产品配置项角色',
        Enu::RESP_PRODUCT_CONFIG_ITEM_MISSING_MODIFY                   => '缺少产品配置项是否可被客户修改',
        Enu::RESP_PRODUCT_CONFIG_ITEM_MISSING_EXTEND                   => '缺少产品配置项是否可被子产品继承',
        Enu::RESP_PRODUCT_CONFIG_ITEM_MISSING_ITEM_TYPE                => '缺少产品配置项类型',
        Enu::RESP_PRODUCT_CONFIG_ITEM_MISSING_ITEM_DEFAULT             => '缺少产品配置项默认值',
        Enu::RESP_PRODUCT_CONFIG_ITEM_DUPLICATION_KEY                  => '产品配置项key重复',
        Enu::RESP_PRODUCT_CONFIG_ITEM_MISSING_DEFAULT                  => '请传入默认值',
        Enu::RESP_PRODUCT_CONFIG_ITEM_DEFAULT_VALUE_NOT_IN_OPTIONS     => '默认值与选项中值不匹配',
        Enu::RESP_PRODUCT_CONFIG_ITEM_MISSING_CONFIG_PATH              => '缺少配置路径',
        Enu::RESP_PRODUCT_CONFIG_ITEM_MISSING_CONFIG_SCOPE             => '缺少配置范围',
        Enu::RESP_PRODUCT_CONFIG_ITEM_CONFLICT_CONFIG_SCOPE_AND_MODIFY => '参数冲突 配置范围为仅在客户维度配置时须可被客户修改',

        //16XX 产品配置值
        Enu::RESP_PRODUCT_CONFIG_MISSING_VALUE    => '缺少产品配置值',
        Enu::RESP_PRODUCT_CONFIG_UNEXPECTED_VALUE => '产品配置值错误',
        Enu::RESP_PRODUCT_CONFIG_HAVE_NO_MODIFY   => '配置项没有更改',


        //17XX 子产品
        Enu::RESP_SUB_PRODUCT_MISSING_FATHER_ID        => '缺少主产品id',
        Enu::RESP_SUB_PRODUCT_MISSING_PRODUCT_ID       => '缺少子产品id',
        Enu::RESP_SUB_PRODUCT_MISSING_PRODUCT_NAME     => '缺少子产品名称',
        Enu::RESP_SUB_PRODUCT_MISSING_PRODUCT_KEY      => '缺少子产品key',
        Enu::RESP_SUB_PRODUCT_DUPLICATION_PRODUCT_ID   => '子产品id重复',
        Enu::RESP_SUB_PRODUCT_DUPLICATION_PRODUCT_NAME => '子产品名称重复',
        Enu::RESP_SUB_PRODUCT_NONE_FATHER_PRODUCT      => '主产品不存在',

        //18XX
        Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_ID                       => '缺少子产品配置项key',
        Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_KEY                      => '缺少子产品配置项key',
        Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_NAME                     => '缺少子产品配置项名称',
        Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_ROLE                     => '缺少子产品配置项角色',
        Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_MODIFY                   => '缺少子产品配置项是否可被客户修改',
        Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_EXTEND                   => '缺少子产品配置项是否可被子产品继承',
        Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_ITEM_TYPE                => '缺少子产品配置项类型',
        Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_ITEM_DEFAULT             => '缺少子产品配置项默认值',
        Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_DUPLICATION_KEY                  => '子产品配置项key重复',
        Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_DUPLICATION_PRODUCT_KEY          => '与主产品配置项key重复',
        Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_NOT_SUB_PRODUCT_ID               => '请传入子产品id',
        Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_DEFAULT                  => '请传入默认值',
        Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_DEFAULT_VALUE_NOT_IN_OPTIONS     => '默认值与选项中值不匹配',
        Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_OPTIONS                  => '缺少配置选项',
        Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_MISSING_CONFIG_SCOPE             => '缺少配置范围',
        Enu::RESP_SUB_PRODUCT_CONFIG_ITEM_CONFLICT_CONFIG_SCOPE_AND_MODIFY => '参数冲突 配置范围为仅在客户维度配置时须可被客户修改',


        Enu::RESP_SUB_PRODUCT_CONFIG_WORNG_SOURCE  => '错误的配置来源!仅可删除继承自主产品的配置项!',
        Enu::RESP_SUB_PRODUCT_CONFIG_WORNG_ITEM_ID => '错误的配置id!查询不到配置项信息!',



        //19XX
        Enu::RESP_CUSTOMER_PRODUCT_CONFIG_WORNG_PARA                                    => '参数错误,请传入主体,客户,账号,主产品,子产品 相关id',
        Enu::RESP_CUSTOMER_PRODUCT_CONFIG_MISSING_ACCOUNT_ID                            => '缺少账户id',
        Enu::RESP_CUSTOMER_PRODUCT_CONFIG_MISSING_PRODUCT_ID_OR_SUB_PRODUCT_ID          => '请传入产品id或子产品id',
        Enu::RESP_CUSTOMER_PRODUCT_CONFIG_WORNG_MODIFY_VALUE                            => '请传入正确参数 是否可被客户修改 1:可修改 2:不可修改',
        Enu::RESP_CUSTOMER_PRODUCT_CONFIG_WORNG_SOURCE_VALUE                            => '配置来源错误',
        Enu::RESP_CUSTOMER_PRODUCT_CONFIG_MISSING_GROUP_ID_OR_CUSTOMER_ID_OR_ACCOUNT_ID => '请传入主体id,客户id或账号id',
        Enu::RESP_CUSTOMER_PRODUCT_CONFIG_MISSING_ITEM_ID                               => '缺少配置项id',
        Enu::RESP_CUSTOMER_PRODUCT_CONFIG_HAVE_NO_CONFIG                                => '该配置项尚未配置',
        Enu::RESP_CUSTOMER_PRODUCT_CUSTOMER_HAS_NO_OPEN_THIS_PRODUCT                    => '该客户没有开通此产品',
        Enu::RESP_CUSTOMER_PRODUCT_ILLEG_ITEM_ID                                        => '产品无此配置项',


        //20XX
        Enu::RESP_APPROVAL_ADD_FAIL     => '添加审批失败',
        Enu::RESP_APPROVAL_STATUS_WORNG => '审批状态错误',
        Enu::RESP_APPROVAL_DUPLICATION  => '该配置正在审批中',

        //21xx
        Enu::RESP_SEARCH_PARAMS_EMPTY   => '参数缺失',



        //22xx
        Enu::RESP_CONTRACT_UPLOAD_CATEGORY_EMPTY    => '上传合同未选择类别',
        Enu::RESP_CONTRACT_UPLOAD_EXCEL_EMPTY       => '上传合同未选择excel文件',
        Enu::RESP_CONTRACT_UPLOAD_PDF_EMPTY         => '上传合同未选择pdf文件',
        Enu::RESP_CONTRACT_LOAD_EXCEL_FAIL          => 'excel文件获取失败',
        Enu::RESP_CONTRACT_CATEGORY_EXCEL_NOT_MATCH => '选择的类别与excel内容符,请检查',
        Enu::RESP_CONTRACT_TYPE_ERR                 => '合同类型错误',
        Enu::RESP_CONTRACT_SIGN_ERR                 => '签约类型错误',
        Enu::RESP_CONTRACT_MISSING_CONTRACT_NO      => '合同编号缺失',
        Enu::RESP_CONTRACT_MISSING_CONTRACT_FILE    => '合同文件缺失',
        Enu::RESP_CONTRACT_CONTRACT_FILE_COUNT_GREATER_THAN_ROWS => '合同文件数量大于数据行数',
        Enu::RESP_CONTRACT_CONTRACT_FILE_COUNT_LESS_THAN_ROWS    => '合同文件数量小于数据行数',
        Enu::RESP_CONTRACT_CONTRACT_NO_DUPLICATION  => '合同编号重复',

        // 5xxx异常
        Enu::RESP_SERVICE_UNAVAILABLE => '程序运行异常',
    ],
];