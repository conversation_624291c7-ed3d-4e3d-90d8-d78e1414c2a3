<?php
return [
    'auth_url' => env('AUTH_URL'),
    'site_url' => env('SITE_URL'),
    'dhb_item_id' => env('AUTH_DHB_ITEMID'),

    'app_host' => 'yulore.dianhua.cn',

    'app_prefix' => 'yulore',

    'app_pids' => [1],

    //路由表
    'router' => include_once("router.php"),

    //数据库
    'database' => include_once("database.php"),

    //响应状态码和code
    'response' => include_once("response.php"),

    //各种状态
    'options' => include_once("options.php"),

    //默认邮件发送配置
    'mailer' => [
        // 'mail_host' => 'smtp.263.net',
        // 'mail_port' => 25,
        // 'mail_encryption' => 'tls',
        // 'mail_username' => '<EMAIL>',
        // 'mail_password' => 'financial321',
        // 'mail_from_address' => '<EMAIL>',
        // 'mail_from_name' => '金融服务部',
        // 'mail_to_address' => ['修伟' => '<EMAIL>']
    ],
    // 该配置项可能在其他维度已配置,请核对后再配置!
    'config_path_map' => [
        'product'              => ['item_default'],
        'sub_product'          => ['product','item_default'],
        'group_product'        => [],
        'customer_product'     => ['group_product'],
        'account_product'      => ['customer_product','group_product'],
        'group_sub_product'    => [],
        'customer_sub_product' => ['group_sub_product'],
        'account_sub_product'  => ['customer_sub_product','group_sub_product'],
    ],

    //不需要使用token访问方法白名单
    'without_token_methods' => [
        '\App\Controllers\IndexController@auth_url' => 0,
        '\App\Controllers\IndexController@validate' => 0,
        '\App\Controllers\IndexController@index' => 0,
        '\App\Controllers\IndexController@customer_options' => 0,
    ],
];
