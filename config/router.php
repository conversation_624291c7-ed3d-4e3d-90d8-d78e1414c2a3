<?php
return [
    'post' => [//更新 创建 删除 数据
               ['index', 'IndexController@index'],
               ['validate', 'IndexController@validate'],
               ['check_token', 'IndexController@check_token'],

               /* 主体 */
               ['group/link', 'Customer\CustomerGroupController@link'],
               ['group/add', 'Customer\CustomerGroupController@addGroup'],
               ['group/edit', 'Customer\CustomerGroupController@edigGroup'],
               /* 客户 */
               ['customer/add', 'Customer\CustomerController@add'],
               ['customer/edit', 'Customer\CustomerController@edit'],
               /* 产品 */
               ['product/add', 'Product\ProductController@add'],
               ['product/edit', 'Product\ProductController@edit'],
               ['product/add_config_item', 'Product\ProductController@addConfigItem'],
               ['product/edit_config', 'Product\ProductController@editConfig'],
               /* 子产品 */
               ['sub_product/add', 'Product\SubProductController@add'],
               ['sub_product/edit', 'Product\SubProductController@edit'],
               ['sub_product/add_config_item', 'Product\SubProductController@addConfigItem'],
               ['sub_product/edit_config', 'Product\SubProductController@editConfig'],
               ['sub_product/del_config', 'Product\SubProductController@delConfig'],
               /* 客户产品 */
               ['account_product/add', 'AccountProduct\AccountProductController@add'],
               /* 客户配置 */
               ['config/edit', 'Config\CustomerProductController@edit'],
               ['config/delete', 'Config\CustomerProductController@delete'],
               /* 审批 */
               ['approval/pass', 'Approval\ApprovalController@pass'],//审批通过
               ['approval/reject', 'Approval\ApprovalController@reject'],//审批驳回
               ['approval/cancel', 'Approval\ApprovalController@cancel'],//审批撤销


               /* 合同 */
               ['contract/add', 'Contract\ContractController@add'],//合同列表添加
               ['contract/list', 'Contract\ContractController@contractList'],//合同列表
               ['contract/del', 'Contract\ContractController@del'],//合同列表添加
               ['contract/company_list', 'Contract\ContractController@companyList'],//公司列表
    ],
    'get'  => [//获取数据
               ['index', 'IndexController@index'],
               ['auth_url', 'IndexController@auth_url'],

               /* 各种筛选下拉框数据 */
               ['options/products', 'OptionsController@products'],
               ['options/main_products', 'OptionsController@mainProductList'],
               ['options/customer_account_list', 'OptionsController@customerAccountList'],
               ['options/customer_list', 'OptionsController@customerList'],
               ['options/account_list', 'OptionsController@accountList'],

               /* 主体 */
               ['group/unlink_customer', 'Customer\CustomerGroupController@getAllUnlinkCustomer'],
               ['group/group_customers', 'Customer\CustomerGroupController@getGroupCutomerList'],
               ['group/list', 'Customer\CustomerGroupController@getGroupList'],
               ['group/info', 'Customer\CustomerGroupController@info'],
               ['group/options', 'Customer\CustomerGroupController@getGroupOption'],
               /* 客户 */
               ['customer/options', 'Customer\CustomerController@customerOptions'],//客户列表使用
               ['customer/modify_options', 'Customer\CustomerController@customerModifyOptions'],//添加,编辑客户使用
               ['customer/list', 'Customer\CustomerController@customerList'],
               ['customer/info', 'Customer\CustomerController@info'],

               /* 产品 */
               ['product/info', 'Product\ProductController@info'],
               ['product/list', 'Product\ProductController@productList'],
               ['product/options', 'Product\ProductController@options'],
               ['product/item_options', 'Product\ProductController@itemOptions'],
               ['product/config_info', 'Product\ProductController@configInfo'],
               ['product/extend_config_items', 'Product\ProductController@extendConfigItems'],//获取主产品可继承配置项

               /* 客户产品 */
               ['account_product/options', 'AccountProduct\AccountProductController@options'],

               /** 配置 */
               // ['config/check', 'Config\ConfigController@check'],//废弃

               /* 子产品 */
               ['sub_product/list', 'Product\SubProductController@subProductList'],
               ['sub_product/options', 'Product\SubProductController@options'],
               ['sub_product/info', 'Product\SubProductController@info'],
               ['sub_product/item_options', 'Product\SubProductController@itemOptions'],
               ['sub_product/config_info', 'Product\SubProductController@configInfo'],


               /* 配置 */
               ['config/customer_list', 'Config\CustomerProductController@customerProductList'],
               ['config/customer_list_options', 'Config\CustomerProductController@customerProductOptions'],
               ['config/info', 'Config\CustomerProductController@info'],//客户配置项列表
               // ['config/search', 'Config\SearchController@searchList'],//客户配置项搜索 特殊配置
               ['config/search', 'Config\SearchController@configList'],//客户配置项搜索 特殊配置
               ['config/customer_config_list', 'Config\SearchController@searchList'],//客户配置项搜索 客户配置

                /* 搜索明细列表 */
               ['search/detail_list', 'Config\SearchController@detailList'],//明细


               /* 审批 */
               ['approval/list', 'Approval\ApprovalController@approvalList'],//审批列表
               ['approval/options', 'Approval\ApprovalController@options'],//审批列表筛选项


               /* 合同 */
               ['contract/options', 'Contract\ContractController@options'],//合同列表筛选项
    ],
    'options'  => [//获取数据
               ['index', 'IndexController@index'],
               ['auth_url', 'IndexController@auth_url'],

               /* 各种筛选下拉框数据 */
               ['options/products', 'OptionsController@products'],

               /* 主体 */
               ['group/unlink_customer', 'Customer\CustomerGroupController@getAllUnlinkCustomer'],
               ['group/group_customers', 'Customer\CustomerGroupController@getGroupCutomerList'],
               ['group/list', 'Customer\CustomerGroupController@getGroupList'],
               ['group/info', 'Customer\CustomerGroupController@info'],
               ['group/options', 'Customer\CustomerGroupController@getGroupOption'],
               /* 客户 */
               ['customer/options', 'Customer\CustomerController@customerOptions'],//客户列表使用
               ['customer/modify_options', 'Customer\CustomerController@customerModifyOptions'],//添加,编辑客户使用
               ['customer/list', 'Customer\CustomerController@customerList'],
               ['customer/info', 'Customer\CustomerController@info'],

               /* 产品 */
               ['product/info', 'Product\ProductController@info'],
               ['product/list', 'Product\ProductController@productList'],
               ['product/options', 'Product\ProductController@options'],
               ['product/item_options', 'Product\ProductController@itemOptions'],
               ['product/config_info', 'Product\ProductController@configInfo'],

               /* 客户产品 */
               ['account_product/options', 'AccountProduct\AccountProductController@options'],

               /** 配置 */
               ['config/list', 'Config\ConfigController@configList'],

               /* 子产品 */
               ['sub_product/list', 'Product\SubProductController@subProductList'],
               ['sub_product/options', 'Product\SubProductController@options'],
               ['sub_product/info', 'Product\SubProductController@info'],
               ['sub_product/item_options', 'Product\SubProductController@itemOptions'],
               ['sub_product/config_info', 'Product\SubProductController@configInfo'],


               /* 配置 */
               ['config/customer_list', 'Config\CustomerProductController@customerProductList'],
               ['config/customer_list_options', 'Config\CustomerProductController@customerProductOptions'],
               ['config/info', 'Config\CustomerProductController@info'],//客户配置项列表
               ['config/search', 'Config\SearchController@searchList'],//客户配置项搜索

                /* 搜索明细列表 */
               ['search/detail_list', 'Config\SearchController@detailList'],//明细


               /* 审批 */
               ['approval/list', 'Approval\ApprovalController@approvalList'],//审批列表
               ['approval/options', 'Approval\ApprovalController@options'],//审批列表筛选项
               ['index', 'IndexController@index'],
               ['validate', 'IndexController@validate'],
               /* 主体 */
               ['group/link', 'Customer\CustomerGroupController@link'],
               ['group/add', 'Customer\CustomerGroupController@addGroup'],
               ['group/edit', 'Customer\CustomerGroupController@edigGroup'],
               /* 客户 */
               ['customer/add', 'Customer\CustomerController@add'],
               ['customer/edit', 'Customer\CustomerController@edit'],
               /* 产品 */
               ['product/add', 'Product\ProductController@add'],
               ['product/edit', 'Product\ProductController@edit'],
               ['product/add_config_item', 'Product\ProductController@addConfigItem'],
               ['product/edit_config', 'Product\ProductController@editConfig'],
               /* 子产品 */
               ['sub_product/add', 'Product\SubProductController@add'],
               ['sub_product/edit', 'Product\SubProductController@edit'],
               ['sub_product/add_config_item', 'Product\SubProductController@addConfigItem'],
               ['sub_product/edit_config', 'Product\SubProductController@editConfig'],
               /* 客户产品 */
               ['account_product/add', 'AccountProduct\AccountProductController@add'],
               /* 客户配置 */
               ['config/edit', 'Config\CustomerProductController@edit'],
               /* 审批 */
               ['approval/pass', 'Approval\ApprovalController@pass'],//审批通过
               ['approval/reject', 'Approval\ApprovalController@reject'],//审批驳回
               ['approval/cancel', 'Approval\ApprovalController@cancel'],//审批撤销
    ],
];
