<?php
return [
    // mysql 相关配置
    'mysql' => [
        'default' => [
            'write' => [
                'host' => env('DB_WRITE_HOST'),
                'port' => env('DB_PORT', 3306),
                'database' => env('DB_DATABASE'),
                'username' => env('DB_USERNAME'),
                'password' => env('DB_PASSWORD'),
                'charset' => 'utf8',
                'timezone' => '+08:00',
                'persistent' => false,
                'timeout' => 10
            ],
            'read' => [
                'host' => env('DB_WRITE_HOST'),
                'port' => env('DB_PORT', 3306),
                'database' => env('DB_DATABASE'),
                'username' => env('DB_USERNAME'),
                'password' => env('DB_PASSWORD'),
                'charset' => 'utf8',
                'timezone' => '+08:00',
                'persistent' => false,
                'timeout' => 10
            ],
        ],
        'crs' => [
            'write' => [
                'host' => env('DB_CRS_WRITE_HOST'),
                'port' => env('DB_CRS_PORT', 3306),
                'database' => env('DB_CRS_DATABASE'),
                'username' => env('DB_CRS_USERNAME'),
                'password' => env('DB_CRS_PASSWORD'),
                'charset' => 'utf8',
                'timezone' => '+08:00',
                'persistent' => false,
                'timeout' => 10
            ],
            'read' => [
                'host' => env('DB_CRS_WRITE_HOST'),
                'port' => env('DB_CRS_PORT', 3306),
                'database' => env('DB_CRS_DATABASE'),
                'username' => env('DB_CRS_USERNAME'),
                'password' => env('DB_CRS_PASSWORD'),
                'charset' => 'utf8',
                'timezone' => '+08:00',
                'persistent' => false,
                'timeout' => 10
            ],
        ],
        'rc' => [
            'write' => [
                'host' => env('DB_RC_WRITE_HOST'),
                'port' => env('DB_RC_PORT', 3306),
                'database' => env('DB_RC_DATABASE'),
                'username' => env('DB_RC_USERNAME'),
                'password' => env('DB_RC_PASSWORD'),
                'charset' => 'utf8',
                'timezone' => '+08:00',
                'persistent' => false,
                'timeout' => 10
            ],
            'read' => [
                'host' => env('DB_RC_WRITE_HOST'),
                'port' => env('DB_RC_PORT', 3306),
                'database' => env('DB_RC_DATABASE'),
                'username' => env('DB_RC_USERNAME'),
                'password' => env('DB_RC_PASSWORD'),
                'charset' => 'utf8',
                'timezone' => '+08:00',
                'persistent' => false,
                'timeout' => 10
            ],
        ],
    ],
    // mongo 相关配置
    'mongo' => [
        'default' => [
            'dsn' => env('MONGODB_DSN', '127.0.0.1'),
            'database' => env('MONGODB_DATABASE', 'test'),
        ],
    ],
    // redis 相关配置
    'redis' => [
        'default' => [
            'host' => env('REDIS_DEFAULT_HOST'),
            'port' => env('REDIS_DEFAULT_PORT', 6379),
            'database' => env('REDIS_DEFAULT_DATABASE', 0),
            'password' => '',
        ],
        'queue' => [
            'host' => env('REDIS_QUEUE_HOST'),
            'port' => env('REDIS_QUEUE_PORT', 6379),
            'database' => env('REDIS_QUEUE_DATABASE', 0),
            'password' => '',
        ],
        'elk' => [
            'host' => env('REDIS_ELK_HOST'),
            'port' => env('REDIS_ELK_PORT', 6379),
            'database' => env('REDIS_ELK_DATABASE', 1),
            'password' => '',
        ]
    ],
];